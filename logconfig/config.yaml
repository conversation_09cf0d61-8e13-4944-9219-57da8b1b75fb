# logging 默认配置
version: 1
formatters:
  simple:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: simple
    stream: ext://sys.stdout
  errorhandler:
    class: logging.FileHandler
    level: ERROR
    formatter: simple
    filename: error.log
  strategyhandler:
    class: logging.handlers.TimedRotatingFileHandler
    level: INFO
    formatter: simple
    when: midnight
    backupCount: 7
    filename: strategy.log
  strategieshandler:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: simple
    backupCount: 1
    maxBytes: 1000000
    filename: strategies.log
  websockethandler:
    class: logging.handlers.TimedRotatingFileHandler
    level: INFO
    formatter: simple
    when: midnight
    backupCount: 7
    filename: websocket.log
  redishandler:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: simple
    backupCount: 1
    maxBytes: 5000000 
    filename: redis.log
  mysqlhandler:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: simple
    backupCount: 1
    maxBytes: 5000000 
    filename: mysql.log
  apihandler:
    class: logging.handlers.TimedRotatingFileHandler
    level: INFO
    formatter: simple
    when: midnight
    backupCount: 7
    filename: api.log
  bt2handler:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: simple
    backupCount: 1
    maxBytes: 5000000
    filename: bt2.log
  datafeedhandler:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: simple
    backupCount: 1
    maxBytes: 5000000
    filename: datafeed.log
  uvicornhandler:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: simple
    backupCount: 1
    maxBytes: 5000000
    filename: uvicorn.log
  brokerhandler:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: simple
    backupCount: 1
    maxBytes: 5000000
    filename: broker.log
  taskhandler:
    class: logging.handlers.TimedRotatingFileHandler
    level: INFO
    formatter: simple
    when: midnight
    backupCount: 7
    filename: task.log
  scripthandler:
    class: logging.FileHandler
    level: INFO
    formatter: simple
    filename: script.log
loggers:
  dev:
    level: DEBUG
    handlers: [console]
    propagate: False
  prod:
    level: WARNING
    handlers: [console]
    propagate: False
  # uvicorn 配置
  uvicorn:
    level: INFO
    handlers: [uvicornhandler, console]
    # propagate: False
  # 策略配置
  bt2.strategy:
    level: INFO
    handlers: [strategyhandler]
    propagate: False
  bt2.strategies:
    level: INFO
    handlers: [strategieshandler]
    propagate: False
  # api配置
  bt2.api:
    level: INFO
    handlers: [apihandler, errorhandler]
    propagate: False
  # websocket配置
  bt2.ws:
    level: INFO
    handlers: [websockethandler, errorhandler]
    propagate: False
  # redis配置
  bt2.redismanager:
    level: INFO
    handlers: [redishandler, errorhandler]
    propagate: False
  bt2.mysql:
    level: INFO
    handlers: [mysqlhandler, errorhandler]
    propagate: False
  # 数据源配置
  bt2.datafeeds:
    level: INFO
    handlers: [datafeedhandler, errorhandler]
    propagate: False
    # 数据源配置
  bt2.datafeed:
    level: INFO
    handlers: [datafeedhandler, errorhandler]
    propagate: False
  # broker配置
  bt2.brokers:
    level: INFO
    handlers: [brokerhandler, errorhandler]
    propagate: False
  # 任务配置
  bt2.task:
    level: INFO
    handlers: [taskhandler, errorhandler]
    propagate: False
  # 模块总配置
  bt2:
    level: ERROR
    handlers: [bt2handler, errorhandler, console]
    propagate: False
  # 脚本配置
  script:
    handlers: [console, scripthandler]
    level: INFO
    propagate: False
root:
  handlers: [console, errorhandler]
  level: INFO
  propagate: False
