## logger管理器
import os
import sys
import yaml
import logging.config
import logging


def setup_logging(root_dir, task_name = None):
    # 使用setup_logging初始化日志系统
    dir_path = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(dir_path, 'config.yaml')
    with open(config_path, 'r') as file:
        config = yaml.safe_load(file)
    # 检查文件夹路径
    check_dir_exist(root_dir, task_name=task_name)
    # 重置logger
    logging.shutdown()
    # 修改路径
    if not task_name:
        task_dir = ''
        tasks_dir = ''
    else:
        task_dir = str(task_name)
        tasks_dir = 'tasks'
    for _, handlerconfig in config.get('handlers', {}).items():
        if handlerconfig.get('filename'):
            logdir, logfile = os.path.split(handlerconfig['filename'])
            new_dir = os.path.join(root_dir, logdir, tasks_dir, task_dir, logfile)
            handlerconfig['filename'] = new_dir
    logging.config.dictConfig(config)
    sys.excepthook = handle_uncatched_exception


def check_dir_exist(root_dir, task_name = None):
    # 检查启动服务文件下是否有logs文件夹, 没有则创建一个
    log_dir = root_dir
    if task_name != None:
        log_dir = os.path.join(log_dir, 'tasks', str(task_name))
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

def handle_uncatched_exception(exc_type, exc_value, exc_traceback):
    """
    捕获所有未处理的异常并记录到日志。
    """
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    logging.error("Uncaught exception", exc_info=(exc_type, exc_value, exc_traceback))