# from logconfig import setup_logging
# setup_logging("testlogs")


from bt2.mysql import DBManager
from bt2.context import DBTaskStatusContext
from bt2.redismanager import (
    MultiKLineRedisManager,
    DynParamRedisManager,
    PlatIndRedisManager,
    RiskRedisManager,
    RunControlRedisManager,
    MultiOpenIntRedisManager,
)
from bt2.sizers import NoneSize
from bt2.ws import MultiKLineWSManager, BinanceBrokerWSManager
from bt2.api import BinanceBrokerAPIManager
from bt2.storages import IndCsvManager, TradeCSVStorage, OrderCSVStorage
from bt2.cerebro import Cerebro2
from bt2.brokers import BackBroker2, BinanceLiveBroker
from bt2.datafeeds import OneWSRedisDataFeed
from bt2.strategies import *


def demo():
    # 手动配置mysql数据库
    taskmanager = DBManager(uri="mysql+pymysql://lq:123@localhost/quant")

    # 手动配置数据源redis
    klinelivemanager = MultiKLineRedisManager(
        host="*************",
        port=6379,
        db=13,
        password="fhj666888",
    )

    # 手动配置持仓量redis
    openintmanager = MultiOpenIntRedisManager(
        host="*************",
        port=6379,
        db=13,
        password="fhj666888",
    )

    # 手动配置历史数据websocket
    wsmanager = MultiKLineWSManager(
        uri="ws://apiws.karlet.com/market",
    )

    # 手动配置币安账户api
    brokerapimanager = BinanceBrokerAPIManager(
        uri="http://api.karlet.com",
    )

    # 手动配置币安账户websocket
    brokermanager = BinanceBrokerWSManager(uri="ws://*************:8080/ws")

    # 手动配置动态参数redis
    dynparammanager = DynParamRedisManager(
        host="127.0.0.1",
        port=6379,
        db=12,
        password="fhj666888",
    )

    # 手动配置运行控制redis
    controlmanager = RunControlRedisManager(
        host="127.0.0.1",
        port=6379,
        db=12,
        password="fhj666888",
    )

    # 手动配置指标csv存储
    indcsvmanager = IndCsvManager(filedir="/home/<USER>/workspace/bt2/logs/indcsv")

    # 手动配置平台指标
    platindmanager = PlatIndRedisManager(
        host="*************",
        port=6379,
        db=13,
        password="fhj666888",
    )

    # 手动配置风控
    riskmanager = RiskRedisManager(
        host="127.0.0.1",
        port=6379,
        db=13,
        password="fhj666888",
    )

    with (
        taskmanager,
        klinelivemanager,
        openintmanager,
        wsmanager,
        brokermanager,
        brokerapimanager,
        dynparammanager,
        indcsvmanager,
        riskmanager,
        platindmanager,
        controlmanager,
    ):
        try:
            ## 解析参数
            # 任务id
            task_id = 1
            # 品种名
            symbol = "ethusdt"
            # 是否进行历史回测
            simulate = True

            # 策略
            strategy = YugeStrategy

            # 获取策略默认参数
            context_params = strategy.get_default_data_params(symbol=symbol)

            # 对默认参数做修改
            for d in context_params:
                # 是否采用实时数据
                d["is_live"] = not simulate
                # 历史数据时间长度
                # d["days_ago"] = 10
                d["start_date"] = "2025-08-01"
                d["end_date"] = "2025-09-01"

            stra_params = {
                "simulate": simulate,
            }

            # 创建cerebro
            cerebro = Cerebro2(
                cheat_on_open=True, stdstats=True, exactbars=0, preload=False
            )

            # 创建代理人
            if simulate:
                # 模拟broker
                broker = BackBroker2(cashbalance=100000)
            else:
                # 真实broker
                broker = BinanceLiveBroker(
                    account_info={
                        "account_id": 20,
                        "apipass": "Fhj666888!",
                        "apikey": "fMGL7caH5mS1TB7lf70TyVk9uxTVLYIAcbttMXHkKUmtob5mk3EtAIu8zBhYkzN6",
                        "apisecret": "Y4SohGTjBqgPk1h3hIRIuMFeHfxDDMOMoMH7jtol4BvKnduQdXGJNIqz9CTi5HbU",
                        "platform": "binance_futures",
                        "purpose": 0,
                    },
                    dyn_key=[task_id, "TestStrategy"],
                    risk_control=True,
                )
            cerebro.setbroker(broker=broker)

            # 创建数据源对象
            for data_params in context_params:
                dataname = data_params.pop("dataname")
                data = OneWSRedisDataFeed(dataname=dataname, **data_params)
                cerebro.adddata(data)

            # 增加策略
            cerebro.addstrategy(strategy=strategy, **stra_params)

            # 设置仓位控制
            cerebro.addsizer(NoneSize)

            # 增加存储
            # cerebro.addstorage(bt2.storages.ConditionIndicatorCSVStorage, redump=False, simulate=True)
            # cerebro.addstorage(bt2.storages.EquityCSVStorage, redump=False, simulate=True)
            # cerebro.addstorage(TradeCSVStorage, redump=False, simulate=True)
            # cerebro.addstorage(OrderCSVStorage, redump=False, simulate=True)

            # 运行
            with DBTaskStatusContext(task_id=task_id, conn_db=False):
                res = cerebro.run()

            return res[0], cerebro

        except Exception as e:
            raise e


stra, cerebro = demo()

# 生成统计报告
print("\n" + "=" * 80)
print("生成策略统计报告")
print("=" * 80)

# 生成报告
report = stra.report()

# 打印摘要
from bt2.analyzers import print_report_summary

print_report_summary(report)

# 保存报告文件
from bt2.analyzers import ReportOutputManager, export_to_excel

output_manager = ReportOutputManager(output_dir="reports")

saved_files = output_manager.save_report(
    report=report, strategy_name=stra.__class__.__name__, symbol="ethusdt", format="all"
)

print(f"\n报告已保存到以下文件:")
for file_path in saved_files:
    print(f"  - {file_path}")

# 保存Excel格式
excel_path = f"reports/{stra.__class__.__name__}_ethusdt_report.xlsx"
excel_file = export_to_excel(report, excel_path)
if excel_file:
    print(f"  - {excel_file}")

# 显示图表
stra.bplot()
