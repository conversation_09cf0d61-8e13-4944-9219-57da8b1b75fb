#!/usr/bin/env python3
"""
策略统计报告使用示例
"""

import bt2
from bt2.mysql import DBManager
from bt2.context import DBTaskStatusContext
from bt2.redismanager import (
    MultiKLineRedisManager,
    DynParamRedisManager,
    PlatIndRedisManager,
    RiskRedisManager,
    RunControlRedisManager,
    MultiOpenIntRedisManager,
)
from bt2.sizers import NoneSize
from bt2.ws import MultiKLineWSManager, BinanceBrokerWSManager
from bt2.api import BinanceBrokerAPIManager
from bt2.storages import IndCsvManager
from bt2.cerebro import Cerebro2
from bt2.brokers import BackBroker2
from bt2.datafeeds import OneWSRedisDataFeed
from bt2.strategies import *
from bt2.analyzers import ReportOutputManager, print_report_summary, export_to_excel
import backtrader as bt


def run_strategy_with_report():
    """运行策略并生成统计报告"""
    
    # 初始化管理器
    taskmanager = DBManager()
    klinelivemanager = MultiKLineRedisManager()
    openintmanager = MultiOpenIntRedisManager()
    wsmanager = MultiKLineWSManager()
    brokermanager = BinanceBrokerWSManager()
    brokerapimanager = BinanceBrokerAPIManager()
    dynparammanager = DynParamRedisManager()
    indcsvmanager = IndCsvManager()
    riskmanager = RiskRedisManager()
    platindmanager = PlatIndRedisManager()
    controlmanager = RunControlRedisManager()

    with (
        taskmanager,
        klinelivemanager,
        openintmanager,
        wsmanager,
        brokermanager,
        brokerapimanager,
        dynparammanager,
        indcsvmanager,
        riskmanager,
        platindmanager,
        controlmanager,
    ):
        try:
            # 策略参数
            task_id = 1
            symbol = "ethusdt"
            simulate = True
            strategy = YugeStrategy

            # 获取策略默认参数
            context_params = strategy.get_default_data_params(symbol=symbol)

            # 修改参数
            for d in context_params:
                d["is_live"] = not simulate
                d["start_date"] = "2025-08-01"
                d["end_date"] = "2025-09-01"

            stra_params = {
                "simulate": simulate,
            }

            # 创建cerebro
            cerebro = Cerebro2(
                cheat_on_open=True, stdstats=True, exactbars=0, preload=False
            )

            # 添加分析器
            cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name="trades")
            cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name="sharpe")
            cerebro.addanalyzer(bt.analyzers.DrawDown, _name="drawdown")
            cerebro.addanalyzer(bt2.analyzers.AdvancedTradeAnalyzer, _name="advanced")

            # 创建broker
            broker = BackBroker2(cashbalance=10000)
            cerebro.setbroker(broker=broker)

            # 创建数据源
            for data_params in context_params:
                dataname = data_params.pop("dataname")
                resampledata = data_params.pop("resampledata", None)
                if resampledata:
                    data = bt2.ResampleDataFeed(
                        origin_data=cerebro.datasbyname[resampledata],
                        dataname=dataname,
                        **data_params,
                    )
                else:
                    data = OneWSRedisDataFeed(dataname=dataname, **data_params)
                cerebro.adddata(data)

            # 添加策略
            cerebro.addstrategy(strategy=strategy, **stra_params)

            # 设置仓位控制
            cerebro.addsizer(NoneSize)

            # 运行策略
            print("开始运行策略...")
            with DBTaskStatusContext(task_id=task_id, conn_db=False):
                results = cerebro.run()

            strategy_instance = results[0]
            
            print("策略运行完成，开始生成统计报告...")

            # 生成统计报告
            report = strategy_instance.report()
            
            # 打印摘要到控制台
            print("\n" + "="*80)
            print("策略统计报告摘要")
            print("="*80)
            print_report_summary(report)
            
            # 保存报告到文件
            output_manager = ReportOutputManager(output_dir="strategy_reports")
            
            # 保存所有格式的报告
            saved_files = output_manager.save_report(
                report=report,
                strategy_name=strategy.__name__,
                symbol=symbol,
                format='all'
            )
            
            print(f"\n报告已保存到以下文件:")
            for file_path in saved_files:
                print(f"  - {file_path}")
            
            # 额外保存Excel格式
            excel_path = f"strategy_reports/{strategy.__name__}_{symbol}_report.xlsx"
            excel_file = export_to_excel(report, excel_path)
            if excel_file:
                print(f"  - {excel_file}")
            
            # 显示一些关键指标
            print(f"\n关键指标:")
            perf = report.get('performance_metrics', {})
            trade = report.get('trade_analysis', {})
            risk = report.get('risk_metrics', {})
            
            print(f"总收益率: {perf.get('total_return_pct', 0):.2f}%")
            print(f"总交易次数: {trade.get('total_trades', 0)}")
            print(f"胜率: {trade.get('win_rate_pct', 0):.2f}%")
            print(f"盈利因子: {trade.get('profit_factor', 0):.2f}")
            print(f"最大回撤: {risk.get('max_drawdown_pct', 0):.2f}%")
            print(f"夏普比率: {perf.get('sharpe_ratio', 0):.3f}")
            
            return strategy_instance, report

        except Exception as e:
            print(f"运行出错: {e}")
            raise e


def analyze_backtrader_analyzers(strategy_instance):
    """分析backtrader内置分析器的结果"""
    print("\n" + "="*80)
    print("Backtrader内置分析器结果")
    print("="*80)
    
    # 交易分析器
    if hasattr(strategy_instance.analyzers, 'trades'):
        trades_analysis = strategy_instance.analyzers.trades.get_analysis()
        print("\n交易分析器结果:")
        print(f"  总交易数: {trades_analysis.get('total', {}).get('total', 0)}")
        print(f"  盈利交易: {trades_analysis.get('won', {}).get('total', 0)}")
        print(f"  亏损交易: {trades_analysis.get('lost', {}).get('total', 0)}")
        
        pnl = trades_analysis.get('pnl', {})
        if isinstance(pnl, dict):
            net_pnl = pnl.get('net', {})
            if isinstance(net_pnl, dict):
                print(f"  净盈亏: {net_pnl.get('total', 0):.2f}")
    
    # 夏普比率
    if hasattr(strategy_instance.analyzers, 'sharpe'):
        sharpe_analysis = strategy_instance.analyzers.sharpe.get_analysis()
        print(f"\n夏普比率: {sharpe_analysis.get('sharperatio', 0):.3f}")
    
    # 回撤分析
    if hasattr(strategy_instance.analyzers, 'drawdown'):
        dd_analysis = strategy_instance.analyzers.drawdown.get_analysis()
        print(f"\n回撤分析:")
        print(f"  最大回撤: {dd_analysis.get('max', {}).get('drawdown', 0):.2f}%")
        print(f"  最大回撤持续时间: {dd_analysis.get('max', {}).get('len', 0)} bars")
    
    # 高级分析器
    if hasattr(strategy_instance.analyzers, 'advanced'):
        advanced_analysis = strategy_instance.analyzers.advanced.get_analysis()
        print(f"\n高级分析器结果:")
        
        trades_info = advanced_analysis.get('trades', {})
        print(f"  总交易数: {trades_info.get('total', 0)}")
        print(f"  胜率: {trades_info.get('win_rate', 0)*100:.2f}%")
        print(f"  盈利因子: {trades_info.get('profit_factor', 0):.2f}")
        print(f"  最大连续盈利: {trades_info.get('consecutive_wins', 0)}")
        print(f"  最大连续亏损: {trades_info.get('consecutive_losses', 0)}")
        
        perf_info = advanced_analysis.get('performance', {})
        print(f"  夏普比率: {perf_info.get('sharpe_ratio', 0):.3f}")
        print(f"  索提诺比率: {perf_info.get('sortino_ratio', 0):.3f}")
        print(f"  卡尔玛比率: {perf_info.get('calmar_ratio', 0):.3f}")


if __name__ == "__main__":
    # 运行示例
    strategy_instance, report = run_strategy_with_report()
    
    # 分析backtrader分析器结果
    analyze_backtrader_analyzers(strategy_instance)
    
    print(f"\n示例运行完成！")
    print(f"您可以查看生成的报告文件了解详细的策略表现。")
