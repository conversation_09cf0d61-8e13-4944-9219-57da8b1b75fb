# 配置读取类

import yaml
import os


class ConfigMeta(type):
    _config = {}

    def __getattr__(cls, name):
        try:
            return cls._config[name]
        except KeyError:
            raise AttributeError(f"Config has no attribute '{name}'")

    def __getitem__(cls, key):
        return cls._config[key]


class ManagerConfig(metaclass=ConfigMeta):
    """
    读取config目录下的manager.yaml文件, 获取全局参数
    """

    if os.path.exists("./config/manager.yaml"):
        with open("./config/manager.yaml", "r") as file:
            _config = yaml.safe_load(file)


class WorkerConfig(metaclass=ConfigMeta):
    """
    读取config目录下的worker.yaml文件, 获取全局参数
    """

    if os.path.exists("./config/worker.yaml"):
        with open("./config/worker.yaml", "r") as file:
            _config = yaml.safe_load(file)
