"""
统计报告输出工具
"""

import json
import csv
import pandas as pd
from datetime import datetime
from pathlib import Path


class ReportOutputManager:
    """统计报告输出管理器"""

    def __init__(self, output_dir="reports"):
        """
        初始化输出管理器
        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

    def save_report(self, report, strategy_name, symbol, format="all", timestamp=None):
        """
        保存统计报告
        Args:
            report: 报告字典
            strategy_name: 策略名称
            symbol: 交易品种
            format: 输出格式 ('json', 'csv', 'html', 'txt', 'all')
            timestamp: 时间戳，如果为None则使用当前时间
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        base_filename = f"{strategy_name}_{symbol}_{timestamp}"

        if format == "all":
            formats = ["json", "csv", "html", "txt"]
        else:
            formats = [format]

        saved_files = []

        for fmt in formats:
            if fmt == "json":
                filepath = self._save_json_report(report, base_filename)
            elif fmt == "csv":
                filepath = self._save_csv_report(report, base_filename)
            elif fmt == "html":
                filepath = self._save_html_report(report, base_filename)
            elif fmt == "txt":
                filepath = self._save_txt_report(report, base_filename)
            else:
                continue

            saved_files.append(filepath)

        return saved_files

    def _save_json_report(self, report, base_filename):
        """保存JSON格式报告"""
        filepath = self.output_dir / f"{base_filename}.json"

        # 处理不能JSON序列化的对象
        json_report = self._prepare_for_json(report)

        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(json_report, f, indent=2, ensure_ascii=False, default=str)

        return str(filepath)

    def _save_csv_report(self, report, base_filename):
        """保存CSV格式报告"""
        # 创建多个CSV文件
        csv_dir = self.output_dir / f"{base_filename}_csv"
        csv_dir.mkdir(exist_ok=True)

        saved_files = []

        # 基本信息
        if "basic_info" in report:
            filepath = csv_dir / "basic_info.csv"
            self._dict_to_csv(report["basic_info"], filepath)
            saved_files.append(filepath)

        # 性能指标
        if "performance_metrics" in report:
            filepath = csv_dir / "performance_metrics.csv"
            self._dict_to_csv(report["performance_metrics"], filepath)
            saved_files.append(filepath)

        # 交易分析
        if "trade_analysis" in report:
            filepath = csv_dir / "trade_analysis.csv"
            self._dict_to_csv(report["trade_analysis"], filepath)
            saved_files.append(filepath)

        # 风险指标
        if "risk_metrics" in report:
            filepath = csv_dir / "risk_metrics.csv"
            self._dict_to_csv(report["risk_metrics"], filepath)
            saved_files.append(filepath)

        # 详细交易记录
        if "detailed_trades" in report and report["detailed_trades"]:
            filepath = csv_dir / "detailed_trades.csv"
            df = pd.DataFrame(report["detailed_trades"])
            df.to_csv(filepath, index=False, encoding="utf-8")
            saved_files.append(filepath)

        # 资金曲线
        if "equity_curve" in report and report["equity_curve"]:
            filepath = csv_dir / "equity_curve.csv"
            df = pd.DataFrame(report["equity_curve"])
            df.to_csv(filepath, index=False, encoding="utf-8")
            saved_files.append(filepath)

        return csv_dir

    def _save_html_report(self, report, base_filename):
        """保存HTML格式报告"""
        filepath = self.output_dir / f"{base_filename}.html"

        # 判断是多品种汇总报告还是单品种报告
        has_overview = "overview" in report
        has_performance = "performance_comparison" in report
        is_multi_symbol = has_overview and has_performance

        if is_multi_symbol:
            print("📊 使用多品种HTML生成器")
            html_content = self._generate_multi_symbol_html_report(report)
        else:
            print("📄 使用单品种HTML生成器")
            html_content = self._generate_html_report(report)

        with open(filepath, "w", encoding="utf-8") as f:
            f.write(html_content)

        return str(filepath)

    def _save_txt_report(self, report, base_filename):
        """保存文本格式报告"""
        filepath = self.output_dir / f"{base_filename}.txt"

        with open(filepath, "w", encoding="utf-8") as f:
            # 写入摘要
            if "summary" in report:
                f.write(report["summary"])
                f.write("\n\n")

            # 写入详细信息
            f.write("=" * 80 + "\n")
            f.write("详细统计报告\n")
            f.write("=" * 80 + "\n\n")

            for section_name, section_data in report.items():
                if section_name == "summary":
                    continue

                f.write(f"{section_name.upper()}\n")
                f.write("-" * 40 + "\n")

                if isinstance(section_data, dict):
                    for key, value in section_data.items():
                        f.write(f"{key}: {value}\n")
                elif isinstance(section_data, list):
                    f.write(f"共 {len(section_data)} 条记录\n")
                else:
                    f.write(f"{section_data}\n")

                f.write("\n")

        return str(filepath)

    def _dict_to_csv(self, data_dict, filepath):
        """将字典保存为CSV"""
        with open(filepath, "w", newline="", encoding="utf-8") as f:
            writer = csv.writer(f)
            writer.writerow(["指标", "数值"])
            for key, value in data_dict.items():
                writer.writerow([key, value])

    def _prepare_for_json(self, obj):
        """准备JSON序列化"""
        if isinstance(obj, dict):
            return {k: self._prepare_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._prepare_for_json(item) for item in obj]
        elif hasattr(obj, "isoformat"):  # datetime对象
            return obj.isoformat()
        elif isinstance(obj, (int, float, str, bool)) or obj is None:
            return obj
        else:
            return str(obj)

    def _generate_html_report(self, report):
        """生成HTML报告"""
        html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略统计报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .section h2 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 10px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .summary { background-color: #f9f9f9; padding: 15px; border-radius: 5px; white-space: pre-line; }
        .positive { color: green; }
        .negative { color: red; }
    </style>
</head>
<body>
"""

        # 添加标题
        basic_info = report.get("basic_info", {})
        html += f"""
    <div class="header">
        <h1>策略统计报告</h1>
        <p><strong>策略名称:</strong> {basic_info.get('strategy_name', 'Unknown')}</p>
        <p><strong>交易品种:</strong> {basic_info.get('symbol', 'Unknown')}</p>
        <p><strong>报告生成时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
"""

        # 添加摘要
        if "summary" in report:
            html += f"""
    <div class="section">
        <h2>报告摘要</h2>
        <div class="summary">{report['summary']}</div>
    </div>
"""

        # 添加各个部分
        sections = [
            ("basic_info", "基本信息"),
            ("performance_metrics", "性能指标"),
            ("trade_analysis", "交易分析"),
            ("risk_metrics", "风险指标"),
            ("position_analysis", "持仓分析"),
            ("time_analysis", "时间分析"),
        ]

        for section_key, section_title in sections:
            if section_key in report and report[section_key]:
                html += f"""
    <div class="section">
        <h2>{section_title}</h2>
        <table>
            <tr><th>指标</th><th>数值</th></tr>
"""
                for key, value in report[section_key].items():
                    # 格式化数值
                    if isinstance(value, float):
                        if abs(value) < 0.01:
                            formatted_value = f"{value:.6f}"
                        else:
                            formatted_value = f"{value:.2f}"
                    else:
                        formatted_value = str(value)

                    # 添加颜色
                    css_class = ""
                    if isinstance(value, (int, float)):
                        if (
                            value > 0
                            and "return" in key.lower()
                            or "pnl" in key.lower()
                        ):
                            css_class = 'class="positive"'
                        elif value < 0:
                            css_class = 'class="negative"'

                    html += f"            <tr><td>{key}</td><td {css_class}>{formatted_value}</td></tr>\n"

                html += "        </table>\n    </div>\n"

        html += """
</body>
</html>
"""
        return html

    def export_to_excel(self, report, filepath):
        """导出报告到Excel文件"""
        try:
            with pd.ExcelWriter(filepath, engine="openpyxl") as writer:
                # 基本信息
                if "basic_info" in report:
                    df = pd.DataFrame(
                        list(report["basic_info"].items()), columns=["指标", "数值"]
                    )
                    df.to_excel(writer, sheet_name="基本信息", index=False)

                # 性能指标
                if "performance_metrics" in report:
                    df = pd.DataFrame(
                        list(report["performance_metrics"].items()),
                        columns=["指标", "数值"],
                    )
                    df.to_excel(writer, sheet_name="性能指标", index=False)

                # 交易分析
                if "trade_analysis" in report:
                    df = pd.DataFrame(
                        list(report["trade_analysis"].items()), columns=["指标", "数值"]
                    )
                    df.to_excel(writer, sheet_name="交易分析", index=False)

                # 风险指标
                if "risk_metrics" in report:
                    df = pd.DataFrame(
                        list(report["risk_metrics"].items()), columns=["指标", "数值"]
                    )
                    df.to_excel(writer, sheet_name="风险指标", index=False)

                # 详细交易记录
                if "detailed_trades" in report and report["detailed_trades"]:
                    df = pd.DataFrame(report["detailed_trades"])
                    df.to_excel(writer, sheet_name="详细交易", index=False)

                # 资金曲线
                if "equity_curve" in report and report["equity_curve"]:
                    df = pd.DataFrame(report["equity_curve"])
                    df.to_excel(writer, sheet_name="资金曲线", index=False)

            return str(filepath)
        except Exception as e:
            print(f"导出Excel文件失败: {e}")
            return None

    def _generate_multi_symbol_html_report(self, report):
        """生成多品种汇总HTML报告"""
        overview = report.get("overview", {})

        # 基本HTML结构
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多品种回测汇总报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  color: white; padding: 30px; border-radius: 10px; text-align: center; }}
        .section {{ margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }}
        .section h2 {{ color: #333; border-bottom: 3px solid #667eea; padding-bottom: 10px; margin-top: 0; }}
        table {{ border-collapse: collapse; width: 100%; margin: 15px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 12px; text-align: center; }}
        th {{ background-color: #f8f9fa; font-weight: bold; }}
        .overview-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 20px; margin: 20px 0; }}
        .overview-card {{ background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }}
        .overview-card h3 {{ margin: 0 0 10px 0; color: #667eea; }}
        .overview-card .value {{ font-size: 24px; font-weight: bold; color: #333; }}
        .positive {{ color: #28a745; font-weight: bold; }}
        .negative {{ color: #dc3545; font-weight: bold; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 多品种回测汇总报告</h1>
        <p>分析时间: {overview.get('analysis_date', 'Unknown')}</p>
    </div>

    <div class="section">
        <h2>📊 概览信息</h2>
        <div class="overview-grid">
            <div class="overview-card">
                <h3>回测品种</h3>
                <div class="value">{overview.get('total_symbols', 0)}</div>
            </div>
            <div class="overview-card">
                <h3>总交易次数</h3>
                <div class="value">{overview.get('total_trades', 0)}</div>
            </div>
            <div class="overview-card">
                <h3>平均胜率</h3>
                <div class="value">{overview.get('average_win_rate', 0):.2f}%</div>
            </div>
            <div class="overview-card">
                <h3>总盈亏</h3>
                <div class="value {'positive' if overview.get('total_return', 0) > 0 else 'negative'}">{overview.get('total_return', 0):,.2f}</div>
            </div>
        </div>
        <p><strong>回测品种:</strong> {', '.join([s.upper() for s in overview.get('symbols', [])])}</p>
    </div>
"""

        # 添加性能对比表格
        performance = report.get("performance_comparison", {})
        if performance:
            html += """
    <div class="section">
        <h2>📈 性能对比</h2>
        <table>
            <thead>
                <tr>
                    <th>品种</th>
                    <th>总收益率(%)</th>
                    <th>夏普比率</th>
                    <th>索提诺比率</th>
                    <th>卡玛比率</th>
                </tr>
            </thead>
            <tbody>
"""
            for symbol, metrics in performance.items():
                total_return = metrics.get("total_return_pct", 0)
                sharpe = metrics.get("sharpe_ratio", 0)
                sortino = metrics.get("sortino_ratio", 0)
                calmar = metrics.get("calmar_ratio", 0)
                return_class = "positive" if total_return > 0 else "negative"

                html += f"""
                <tr>
                    <td><strong>{symbol.upper()}</strong></td>
                    <td class="{return_class}">{total_return:.2f}%</td>
                    <td>{sharpe:.3f}</td>
                    <td>{sortino:.3f}</td>
                    <td>{calmar:.3f}</td>
                </tr>
"""
            html += """
            </tbody>
        </table>
    </div>
"""

        # 添加综合排名
        ranking = report.get("ranking", {})
        if ranking and "composite_score" in ranking:
            html += """
    <div class="section">
        <h2>🏆 综合评分排名</h2>
        <table>
            <thead>
                <tr>
                    <th>排名</th>
                    <th>品种</th>
                    <th>综合评分</th>
                </tr>
            </thead>
            <tbody>
"""
            composite_ranking = ranking["composite_score"]["ranking"]
            for rank, symbol, score in composite_ranking:
                html += f"""
                <tr>
                    <td><strong>#{rank}</strong></td>
                    <td><strong>{symbol.upper()}</strong></td>
                    <td style="color: #667eea; font-weight: bold;">{score:.2f}</td>
                </tr>
"""
            html += """
            </tbody>
        </table>
        <p style="margin-top: 15px; color: #666; font-size: 14px;">
            <strong>评分说明:</strong> 综合评分基于收益率(30%) + 夏普比率(30%) + 胜率(20%) - 最大回撤(20%)
        </p>
    </div>
"""

        # 结束HTML
        html += f"""
    <div style="text-align: center; margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <p style="color: #666; margin: 0;">
            📊 报告生成时间: {overview.get('analysis_date', 'Unknown')} | 🚀 PyTrader多品种回测系统
        </p>
    </div>
</body>
</html>
"""

        return html


def print_report_summary(report):
    """打印报告摘要到控制台"""
    if "summary" in report:
        print(report["summary"])
    else:
        print("报告摘要不可用")
