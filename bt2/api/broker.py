# API封装管理器
import time
from logging import getLogger
from ..context import DBTaskStatusContext
from datetime import datetime
from .manager import BaseAPIManager

logger = getLogger(__name__)


class BrokerAPIManager(BaseAPIManager):
    """
    broker api 管理器
    """

    ConfigKey = "BROKER_API"

    @classmethod
    def set_account(
        cls, apipass, apikey, apisecret, platform, account_id, *args, **kwargs
    ):
        """设置账户信息"""
        cls(
            headers={
                "apipass": apipass,
                "apisecret": apisecret,
                "apikey": apikey,
                "platform": platform,
                "accountId": str(account_id),
            }
        )

    @classmethod
    def get_cashbalance(cls):
        # 获取现金余额
        account_resp = cls.request_get("/bn/account/balance")
        for data in account_resp["data"]:
            for detail in data["details"]:
                if detail["ccy"] == "USDT":  # 只获取美金
                    return float(detail["cashBal"])

    @classmethod
    def get_contract_to_coin(cls, datas):
        # 获取张币转换值
        contract_to_coin_dict = {}
        for data in datas:
            resp = cls.request_get(
                "/bn/public/convertContractCoin",
                params={
                    "type": 2,
                    "instId": data.get_symbol().upper(),
                    "sz": 1,
                },
            )
            contract_to_coin_dict[data] = float(resp["data"][0]["sz"])
        # logger.info('查询完成')
        return contract_to_coin_dict

    @classmethod
    def get_positions(cls):
        # 获取仓位信息, 单向持仓
        pos_resp = cls.request_get("/bn/account/positions")
        pos_resp = pos_resp
        pos_dict = dict()
        for pos_info in pos_resp["data"]:
            # 转换为普通品种名称
            symbol = pos_info["instId"].lower()  # okex命名
            side = pos_info["posSide"]
            size = float(pos_info["pos"])
            if side == "short":
                size *= -1
            price = float(pos_info["avgPx"])
            if symbol in pos_dict:
                logger.warning("品种重复, 请检查账户是否为双向持仓: %s", pos_info)
            pos_dict[symbol] = {"size": size, "price": price}
        return pos_dict

    @classmethod
    def get_levers(cls):
        # 获取杠杆信息
        for i in range(20):
            try:
                pos_resp = cls.request_get("/bn/account/positions")
                lever_dict = dict()
                for pos_info in pos_resp["data"]:
                    # 转换为普通品种名称
                    symbol = pos_info["instId"].lower()  # okex命名
                    lever = float(pos_info["lever"])
                    if lever <= 0:
                        raise ValueError("杠杆获取失败, 重试, 当前次数: %s", i + 1)
                    lever_dict[symbol] = lever
                return lever_dict
            except ValueError:
                pass
        raise ValueError("杠杆获取失败")

    @classmethod
    def submit_orders(cls, orders):
        # 提交订单
        logger.info("提交订单")
        for order in orders:
            order_param = cls.order2param(order)
            # 多平台参数
            body = {
                "clOrdId": order_param["order_num"],
                "taskId": order_param["task_id"],
                "symbol": order_param["symbol"].lower(),  # okex规则小写
                "tdMode": "cross",
                "side": order_param["side"].lower(),
                "posSide": order_param["position_side"].lower(),
                "ordType": order_param["order_type"].lower(),
                "quantity": order_param["order_quantity"],
                "price": None,
            }
            res = cls.request_post("plat/order/send", data=body)
            logger.info(f"订单api返回结果: {res}")
            cls.check_order_code(res)
        logger.info(f"提交订单完成")
        return True

    @classmethod
    def order2param(cls, order):
        """根据订单对象生成订单参数"""
        return {
            "platform": order.data.get_platform(),
            "task_id": DBTaskStatusContext.get_current_task_id(),
            "order_num": order.ref,
            "symbol": order.data.get_symbol(),
            "side": "BUY" if order.isbuy() else "SELL",
            "position_side": "LONG" if order.islong() else "SHORT",
            "order_quantity": abs(order.size),  # TODO
            "trade_quantity": None,
            "order_price": order.price,
            "trade_price": None,
            "order_ts": int(datetime.now().timestamp() * 1000),
            "trade_ts": None,
            "order_type": order.getordername(),
            "state": "pending",
        }

    @classmethod
    def query_order(cls, order):
        raise NotImplementedError("Subclasses must implement query_order method")

    @classmethod
    def check_order_code(cls, data):
        pass


class BinanceBrokerAPIManager(BrokerAPIManager):
    """
    币安 broker api 管理器
    """

    @classmethod
    def get_cashbalance(cls):
        # 获取现金余额, 包括USDT和BNB
        usdt, bnb = 0, 0
        account_resp = cls.request_post("/bn/user/binance")
        for detail in account_resp["data"]:
            if detail["asset"] == "USDT":
                usdt = float(detail["balance"])
            if detail["asset"] == "BNB":
                bnb = float(detail["balance"])
        return usdt, bnb

    @classmethod
    def get_positions(cls):
        # 获取仓位信息, 单向持仓
        pos_resp = cls.request_post("bn/order/position")
        pos_resp = pos_resp
        pos_dict = dict()
        for pos_info in pos_resp["data"]:
            # 转换为普通品种名称
            symbol = pos_info["symbol"].upper()  # binance命名
            side = pos_info["positionSide"]
            size = float(pos_info["positionAmt"])
            assert side in ["LONG", "SHORT"], f"unknown side {side}"
            # if side == 'SHORT':
            #     size *= -1
            price = float(pos_info["entryPrice"])
            if symbol in pos_dict:
                logger.warning("品种重复, 请检查账户是否为双向持仓: %s", pos_info)
                # raise ValueError('品种重复, 请检查账户是否为双向持仓')
            pos_dict[symbol] = {"size": size, "price": price}
        return pos_dict

    @classmethod
    def get_levers(cls, symbols=[]):
        # 获取杠杆信息
        lever_dict = dict()
        for symbol in symbols:
            pos_resp = cls.request_post("bn/order/getLeverage", data={"symbol": symbol})
            for pos_info in pos_resp["data"]:
                # 转换为普通品种名称
                symbol = pos_info["symbol"].upper()  # binance命名
                lever_dict[symbol] = float(pos_info["leverage"])
        return lever_dict

    @classmethod
    def cancel_order(cls, order):
        # 撤销订单
        order_param = cls.order2param(order)
        body = {
            "symbol": order_param["symbol"],
            "origClientOrderId": order_param["order_num"],
        }
        resp = cls.request_post("bn/order/cancelOne", data=body)
        try:
            cls.check_cancel_code(resp)
        except ValueError as e:
            return False
        logger.info(f"撤销订单完成, 返回结果 {resp}")
        return True

    @classmethod
    def query_order(cls, order):
        # 查询订单
        order_param = cls.order2param(order)
        body = {
            "symbol": order_param["symbol"],
            "origClientOrderId": order_param["order_num"],
        }
        resp = cls.request_post("bn/order/getOrder", data=body)
        logger.info(f"查询订单完成, 返回结果 {resp}")
        return resp["data"]

    @classmethod
    def query_pending(cls, symbol):
        # 查询数据源全部挂单
        body = {
            "symbol": symbol,
        }
        resp = cls.request_post("/bn/order/pending", data=body)
        logger.info(f"查询订单完成, 返回结果 {resp}")
        return resp["data"]

    @classmethod
    def query_hist_orders(cls, symbol, limit=5):
        # 查询数据源历史订单
        body = {
            "symbol": symbol,
            "limit": limit,
        }
        resp = cls.request_post("/bn/order/getAllOrders", data=body)
        logger.info(f"查询订单完成, 返回结果 {resp}")
        return resp["data"]

    @classmethod
    def submit_orders(cls, orders):
        """
        提交订单
        Args:
            force (bool): 强制交易成功, 如果失败则一直尝试重试
        """
        logger.info("提交订单")
        for order in orders:
            try:
                order_param = cls.order2param(order)
                # 多平台参数
                body = {
                    "clOrdId": order_param["order_num"],
                    "taskId": order_param["task_id"],
                    "symbol": order_param["symbol"].upper(),  # 币安规则
                    "tdMode": "cross",  # 固定全仓
                    "side": order_param["side"].lower(),
                    "posSide": order_param["position_side"].lower(),
                    "ordType": order_param["order_type"].lower(),
                    "quantity": order_param["order_quantity"],
                    "price": order_param["order_price"],
                }
                body.update(order_param["info"])
                ## 特殊处理强制执行参数 TODO 待优化
                force = body.pop("force", False)
                while True:
                    try:
                        resp = cls.request_post("plat/order/send", data=body)
                        if not cls.check_order_code(resp):
                            order.reject()
                        break
                    except Exception as e:
                        logger.warning(f"订单委托失败, 考虑重新下单, 异常: {e}")
                        # logger.exception(e)
                        if force:
                            time.sleep(0.5)  # 休眠 0.5s
                            continue
                        else:
                            raise e
            except Exception as e:
                logger.warning("发送订单失败, body: %s", body)
                order.reject()
                # raise e
            logger.info(f"订单返回结果 {resp}")
        logger.info(f"提交订单完成")
        return True

    @classmethod
    def order2param(cls, order):
        """根据订单对象生成订单参数"""
        data = {
            "platform": order.data.get_platform(),
            "task_id": DBTaskStatusContext.get_current_task_id(),
            "order_num": order.ref,
            "symbol": order.data.get_symbol(),
            "side": "BUY" if order.isbuy() else "SELL",
            "position_side": "LONG" if order.islong() else "SHORT",
            "order_quantity": abs(float(order.size)) if order.size else order.size,
            "trade_quantity": None,
            "order_price": order.price,
            "trade_price": None,
            "order_ts": int(datetime.now().timestamp() * 1000),
            "trade_ts": None,
            "order_type": order.getordername(),
        }
        data["info"] = dict(order.info)
        return data

    @classmethod
    def cancel_all_orders(cls, datas):
        """取消所有订单"""
        for data in datas:
            cls.request_post("bn/order/cancel", data={"symbol": data.get_symbol()})

    @classmethod
    def check_order_code(cls, message: dict):
        """
        检查订单返回结果的code是否正确,
        正确代码返回 True
        可预见的错误代码返回False
        未知代码抛出异常
        """
        if message is None:
            raise ValueError("订单返回结果为空")
        code = str(message["code"])
        if code == "0":  # 正常
            return True
        if code == "-2019":  # 保证金不足
            logger.warning("保证金不足, 代码值 %s, 信息 %s", code, message["message"])
            return False
        if code == "-2022":  # 仓位已经被平
            logger.info("仓位已平")
            return False
        logger.warning("订单状态不正常, 代码值 %s, 信息 %s", code, message["message"])
        raise ValueError(f"order code error {code}")

    @classmethod
    def check_cancel_code(cls, message: dict):
        """检查撤销订单返回结果的code是否正确"""
        code = str(message["code"])
        if code == "0" or code == "-2011":  # 正常
            return
        logger.warning("撤销订单失败, 代码值 %s, 信息 %s", code, message["message"])
        raise ValueError(f"order code error {code}")


class RiskAPIManager:
    """
    风控api管理器
    """

    uri = ""
    phone = ""
    passwd = ""

    def __init__(self, uri="", phone="", passwd=""):
        self.__class__.uri = uri
        self.__class__.phone = phone
        self.__class__.passwd = passwd

    def __enter__(self):
        return

    def __exit__(self, exc_type, exc_value, traceback):
        return
