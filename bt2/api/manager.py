# API封装管理器
from urllib.parse import urljoin
import requests
from logging import getLogger
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from ..manager import ManagerBase

logger = getLogger(__name__)


class BaseAPIManager(ManagerBase):
    """
    api 管理器
    """

    ConfigKey = "BaseAPIManager"

    params = dict(
        uri=None,
        headers=None,
        auth=None,
        retry=5,  # 重试次数
        backoff=1,  # 重试间隔
        timeout=(5, 10),  # 超时时间 (连接, 读取)
    )

    @classmethod
    def initcls(cls):
        if cls.p.auth is not None:
            cls.p.auth = tuple(cls.p.auth)
        retries = Retry(
            total=cls.p.retry,  # 总重试次数
            connect=cls.p.retry,  # 用于新的连接尝试的最大重试次数
            read=cls.p.retry,  # 由于读取而导致的重试次数
            redirect=cls.p.retry,  # 重定向的最大重试次数
            status=cls.p.retry,  # 状态码导致的重试次数
            backoff_factor=cls.p.backoff,  # 指数退避策略的回避因子
            status_forcelist=[429, 500, 502, 503, 504],  # 指定哪些状态码需要重试
            allowed_methods=frozenset(["GET", "POST"]),  # 指定对哪些方法进行重试
            raise_on_status=False,  # 即使遇到重试状态码也不主动抛出异常
            respect_retry_after_header=True,  # 尊重 'Retry-After' 头部提供的退避建议
        )
        adapter = HTTPAdapter(max_retries=retries)
        http = requests.Session()
        http.mount("http://", adapter=adapter)
        http.mount("https://", adapter=adapter)
        cls.http = http  # 保存配置好的Session

    @classmethod
    def request(cls, method, suffix_url, json_format=True, **kwargs):
        """
        发起请求, 统一处理异常
        Args:
            method (str): GET/POST/PUT/DELETE
            suffix_url (str): api后缀
            json (bool): 是否返回json格式
        """
        try:
            logger.info(f"{suffix_url} {method} {cls.p.headers} {kwargs}")
            response = cls.http.request(
                method,
                urljoin(cls.p.uri, suffix_url),
                headers=cls.p.headers,
                timeout=cls.p.timeout,
                auth=cls.p.auth,
                **kwargs,
            )
            response.raise_for_status()
            logger.info(f"{suffix_url}耗时: {response.elapsed}")
            logger.info(f"{suffix_url}响应: {response.text}")
            return response.json() if json_format else response
        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP错误: {e}")
        except requests.exceptions.ConnectionError as e:
            logger.error(f"连接错误: {e}")
        except requests.exceptions.Timeout as e:
            logger.error(f"请求超时: {e}")
        except requests.exceptions.RequestException as e:
            logger.error(f"请求异常: {e}")
        except Exception as e:
            logger.error(f"其他异常: {e}")

    @classmethod
    def request_get(cls, suffix_url, **kwargs):
        return cls.request("GET", suffix_url, **kwargs)

    @classmethod
    def request_post(cls, suffix_url, **kwargs):
        return cls.request("POST", suffix_url, **kwargs)
