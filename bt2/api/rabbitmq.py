from logging import getLogger
from .manager import BaseAPIManager

logger = getLogger(__name__)


class RabbitMQAPIManager(BaseAPIManager):
    """
    rabbitmq api 管理器
    """

    ConfigKey = "RABBITMQ_API"

    params = dict(
        idle=0,
    )

    @classmethod
    def get_comsumer_nums(cls):
        resp = cls.request_get("/api/queues/%2F/stra_task")
        return len(resp["consumer_details"])
