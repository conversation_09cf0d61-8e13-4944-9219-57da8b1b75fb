#!/bin/bash
ACCOUNT_ID=3
FILTER=mtmshortslowfilter.py
echo "restart [id:$ACCOUNT_ID] filter $FILTER"
echo '停止初筛...'
tmux send-keys -t filter C-c
echo 'done'
sleep 1

echo '日志统计...'
curl "http://127.0.0.1:8000/status/generate-csv/"
echo 'done'
sleep 1

echo '重启服务...'
systemctl restart bt2-task-runner.service
echo 'done'
sleep 1

echo '平仓仓位...请先在客户端检查有无仓位'
# curl "http://xxxx/"
echo 'done'
sleep 1

echo '重置数据库状态...'
mysql -h 43.163.231.45 -u quant-trade -pfhj666888 -b quant-trade -e "update f_task set run_state='init' where account_id=$ACCOUNT_ID;"
echo 'done'
sleep 1

echo '更新代码...'
tmux send-keys -t git 'cd /www/wwwroot/bt2' C-m
tmux send-keys -t git 'git pull' C-m
sleep 1
tmux send-keys -t git 'jiaoyin' C-m
sleep 1
tmux send-keys -t git '1345zbnm' C-m
echo 'done'
sleep 1

echo '备份日志...'
cd /var/log && mv bt2 _bt2_$(date +"%Y%m%d_%H%M%S")
echo 'done'
sleep 1

echo '启动初筛...'
tmux send-keys -t filter "python $FILTER" C-m
echo 'done'