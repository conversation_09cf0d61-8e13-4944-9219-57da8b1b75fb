## 自定义策略类型

import backtrader as bt
from logging import getLogger
from .context import DBTaskStatusContext
from .trade import EventTrade
import copy
import itertools


class MetaStorageStrategy(bt.MetaStrategy):
    """用于指标存储的策略元类"""

    def dopreinit(cls, _obj, *args, **kwargs):
        _obj, args, kwargs = super().dopreinit(_obj, *args, **kwargs)
        # 添加storages
        _obj.storages = list()
        # 添加策略条件
        _obj._entrys = list()
        _obj._exits = list()
        _obj._opens = list()
        _obj._closes = list()
        _obj._stops = list()
        _obj._appends = list()
        _obj._set_logger()
        return _obj, args, kwargs


class StorageStrategy(bt.Strategy, metaclass=MetaStorageStrategy):
    """用于存储的策略类
        新增类属性_storages, 用于存放存储器
    Attrs:
        _storages(list): 存储器列表
    """

    def _addstorage(self, stcls, *stargs, **stkwargs):
        self.storages.append(stcls(self, *stargs, **stkwargs))

    def _set_logger(self):
        # 设置自己的logger
        ### 取任务id+第一个数据品种作为log名
        task_name = DBTaskStatusContext.get_current_task_name()
        if task_name:
            logger = getLogger("bt2.task")
        else:
            logger = getLogger(__name__)
        self.logger = logger
        self.logger.info("设置策略logger %s", __name__)

    def _start(self):
        self._periodset()

        for analyzer in itertools.chain(self.analyzers, self._slave_analyzers):
            analyzer._start()

        for obs in self.observers:
            if not isinstance(obs, list):
                obs = [obs]  # support of multi-data observers

            for o in obs:
                o._start()

        # change operators to stage 2
        self._stage2()

        self._dlens = [len(data) for data in self.datas]

        self._minperstatus = bt.MAXINT  # start in prenext

        # self._set_logger()

        self.start()

    def _stop(self):
        super()._stop()
        # 存储stop
        for storage in self.storages:
            storage._stop()

    def _addnotification(self, order, quicknotify=False):
        """
        修改trader的操作
        去掉quicknotify相关逻辑
        """
        self._orderspending.append(order)

        if not order.executed.size:
            # 没有执行仓位则直接返回
            return

        # 判断data, 暂时无用
        tradedata = order.data._compensate
        if tradedata is None:
            tradedata = order.data

        datatrades = self._trades[tradedata][order.tradeid]
        if not datatrades:
            trade = EventTrade(
                data=tradedata, tradeid=order.tradeid, historyon=self._tradehistoryon
            )
            datatrades.append(trade)
        else:
            trade = datatrades[-1]

        for exbit in order.executed.iterpending():
            if exbit is None:
                break

            if exbit.closed:
                trade.update(
                    order,
                    exbit.closed,
                    exbit.price,
                    exbit.closedvalue,
                    exbit.closedcomm,
                    exbit.pnl,
                    comminfo=order.comminfo,
                )

                # 增加开仓后的价格最大最小值
                trade.update_highest(self.observers.livedrawdown.get_prev_highest())
                trade.update_lowest(self.observers.livedrawdown.get_prev_lowest())

                if trade.isclosed:
                    self._tradespending.append(copy.copy(trade))

            # Update it if needed
            if exbit.opened:
                if trade.isclosed:
                    trade = EventTrade(
                        data=tradedata,
                        tradeid=order.tradeid,
                        historyon=self._tradehistoryon,
                    )
                    datatrades.append(trade)

                trade.update(
                    order,
                    exbit.opened,
                    exbit.price,
                    exbit.openedvalue,
                    exbit.openedcomm,
                    exbit.pnl,
                    comminfo=order.comminfo,
                )

                # This extra check covers the case in which different tradeid
                # orders have put the position down to 0 and the next order
                # "opens" a position but "closes" the trade
                if trade.isclosed:
                    self._tradespending.append(copy.copy(trade))

            if trade.justopened:
                self._tradespending.append(copy.copy(trade))

    def buy(
        self,
        data=None,
        size=None,
        price=None,
        plimit=None,
        exectype=None,
        valid=None,
        tradeid=0,
        oco=None,
        trailamount=None,
        trailpercent=None,
        parent=None,
        transmit=True,
        **kwargs,
    ):
        """
        保持原有逻辑, 主要是应对实时broker的修改.
        当size为None且参数中有varValue时, 依然发送订单, 此时订单仓位通过网关控制
        size 不在这里取绝对值, 逻辑转交给broker
        """
        if isinstance(data, str):
            data = self.getdatabyname(data)

        data = data if data is not None else self.datas[0]
        size = size if size is not None else self.getsizing(data, isbuy=True)

        if size != 0:
            return self.broker.buy(
                self,
                data,
                size=size,
                price=price,
                plimit=plimit,
                exectype=exectype,
                valid=valid,
                tradeid=tradeid,
                oco=oco,
                trailamount=trailamount,
                trailpercent=trailpercent,
                parent=parent,
                transmit=transmit,
                **kwargs,
            )

        return None

    def sell(
        self,
        data=None,
        size=None,
        price=None,
        plimit=None,
        exectype=None,
        valid=None,
        tradeid=0,
        oco=None,
        trailamount=None,
        trailpercent=None,
        parent=None,
        transmit=True,
        **kwargs,
    ):

        if isinstance(data, str):
            data = self.getdatabyname(data)

        data = data if data is not None else self.datas[0]
        size = size if size is not None else self.getsizing(data, isbuy=False)

        if size != 0:
            return self.broker.sell(
                self,
                data,
                size=size,
                price=price,
                plimit=plimit,
                exectype=exectype,
                valid=valid,
                tradeid=tradeid,
                oco=oco,
                trailamount=trailamount,
                trailpercent=trailpercent,
                parent=parent,
                transmit=transmit,
                **kwargs,
            )

        return None

    def close(
        self,
        data=None,
        size=None,
        price=None,
        plimit=None,
        exectype=None,
        valid=None,
        tradeid=0,
        oco=None,
        trailamount=None,
        trailpercent=None,
        parent=None,
        transmit=True,
        **kwargs,
    ):
        """平仓的size不做判断, 交给broker做计算"""
        if isinstance(data, str):
            data = self.getdatabyname(data)

        data = data if data is not None else self.datas[0]

        return self.broker.close(
            self,
            data,
            size=size,
            price=price,
            plimit=plimit,
            exectype=exectype,
            valid=valid,
            tradeid=tradeid,
            oco=oco,
            trailamount=trailamount,
            trailpercent=trailpercent,
            parent=parent,
            transmit=transmit,
            **kwargs,
        )

    def cancel_all_orders(self):
        # 撤销所有订单
        return self.broker.cancel_all_orders(self.datas)

    def close_all_positions(self):
        # 平掉所有仓位
        # self.cancel_all_orders()
        return self.broker.close_all_positions(self)

    def cancel_no_wait(self, order):
        # 立即撤销订单直到返回结果
        return self.broker.cancel_no_wait(order)

    @classmethod
    def get_name(cls):
        """获取名称"""
        if cls.alias:
            if isinstance(cls.alias, str):
                return cls.alias
            return cls.alias[0]
        return cls.__name__

    @classmethod
    def all_sub_strategies(cls):
        """获取所有策略"""
        subclasses = cls.__subclasses__()
        for subclass in subclasses:
            # 递归调用以包括当前子类的子类
            subclasses.extend(subclass.all_sub_strategies())
        return subclasses

    @classmethod
    def get_default_params(cls):
        return dict(cls.params._getitems())

    @classmethod
    def get_strategy_by_name(cls, name):
        """通过名字获取策略"""
        for stra in cls.all_sub_strategies():
            if name == stra.get_name():
                return stra
        raise KeyError(f"no strategy named {name}")

    def _next(self):
        super(bt.Strategy, self)._next()

        minperstatus = self._getminperstatus()
        self._next_analyzers(minperstatus)
        self._next_observers(minperstatus)
        # 增加对存储的执行
        self._next_storages(minperstatus)

        self.clear()

    def _next_storages(self, minperstatus, once=False):
        for storage in self.storages:
            storage._next()

    def get_plotindicators(self):
        """获取策略实例中可作图的指标"""
        plotinds = []
        for ind in self.getindicators():
            plotinfo = None
            if hasattr(ind, "get_plotinfo"):  # 自定义指标类型
                plotinfo = ind.get_plotinfo()
            if plotinfo and plotinfo["plotname"]:
                plotinds.append(ind)
        return plotinds

    def get_conditions(self):
        """获取策略实例中的所有条件"""
        return []

    @classmethod
    def get_pseudo_instance(cls):
        """获取一个虚拟实例, 用于获取实例化后的参数"""
        from .cerebro import PseudoCerebro
        from .datafeed import PseudoDataFeed

        cerebro = PseudoCerebro()
        cerebro.adddata(PseudoDataFeed())
        cerebro.addstrategy(cls)
        return cerebro.get_stra_instance()[0]

    @classmethod
    def get_stra_plotindicators(cls):
        """获取策略可作图的指标"""
        plotinds = []
        inst = cls.get_pseudo_instance()
        for ind in inst.getindicators():
            plotinfo = None
            if hasattr(ind, "get_plotinfo"):  # 自定义指标类型
                plotinfo = ind.get_plotinfo()
            if plotinfo and plotinfo["plotname"]:
                plotinds.append(ind)
        return plotinds

    @classmethod
    def all_indicator_plotinfo(cls):
        """获取所有指标的做图参数, 只返回设置了plotname的指标"""
        res = {}
        for ind in cls.get_stra_plotindicators():
            plotinfo = ind.get_plotinfo()
            if plotinfo["plotname"] in res:
                raise KeyError(f'{plotinfo["plotname"]} has been set')
            res[plotinfo["plotname"]] = plotinfo
        return res


class EntryExitStrategy(StorageStrategy):
    """
    进出规则策略, 通过设定进入条件和退出条件执行策略
    进入条件默认用And, 退出条件默认用Or
    """

    alias = ("EntryExitStrategy",)

    LONG, BOTH, SHORT = 1, 0, -1

    params = (
        ("side", BOTH),
        ("simulate", False),
    )

    @classmethod
    def get_default_data_params(cls, symbol="btcusdt"):
        """默认数据源"""
        return {}

    @classmethod
    def get_default_account_name(cls):
        """默认账户"""
        return "BackAccount"

    @classmethod
    def get_default_task_name(cls, symbol="btcusdt"):
        """默认任务名称"""
        return "_".join([cls.alias[0], symbol.lower()])

    @classmethod
    def get_default_task_params(cls, symbol="btcusdt"):
        """生成默认任务参数"""
        return {
            "task_name": cls.get_default_task_name(symbol),
            "strategy_name": cls.get_name(),
            # 'account_name': cls.get_default_account_name(),
            "context": cls.get_default_data_params(symbol),
        }

    def get_all_conditions(self):
        return (
            self._entrys
            + self._exits
            + self._opens
            + self._closes
            + self._stops
            + self._appends
        )

    def add_entry_cond(self, entrycls, *args, **kwargs):
        entry = entrycls(*args, side=self.p.side, simulate=self.p.simulate, **kwargs)
        self._entrys.append(entry)
        return entry

    def add_exit_cond(self, exitcls, *args, **kwargs):
        exit = exitcls(*args, side=self.p.side, simulate=self.p.simulate, **kwargs)
        self._exits.append(exit)
        return exit

    def add_open_cond(self, opencls, *args, **kwargs):
        _open = opencls(*args, side=self.p.side, simulate=self.p.simulate, **kwargs)
        self._opens.append(_open)
        return _open

    def add_close_cond(self, closecls, *args, **kwargs):
        close = closecls(*args, side=self.p.side, simulate=self.p.simulate, **kwargs)
        self._closes.append(close)
        return close

    def add_stop_cond(self, stopcls, *args, **kwargs):
        stop = stopcls(*args, side=self.p.side, simulate=self.p.simulate, **kwargs)
        self._stops.append(stop)
        return stop

    def add_append_cond(self, appendcls, *args, **kwargs):
        append = appendcls(*args, side=self.p.side, simulate=self.p.simulate, **kwargs)
        self._appends.append(append)
        return append

    def set_cond_dynparam(self, cond):
        """设置condition参数为动态参数"""
        self.broker.set_ind_dynparams(cond)

    def get_conditions(self):
        return self._entrys + self._exits + self._opens + self._closes + self._stops

    def preinit(self):
        pass

    def postinit(self):
        pass

    def __init__(self):
        self.preinit()
        self.init()
        self.postinit()

    def init(self):
        pass

    def prenext(self):
        self.preheartbeat()
        # 如果数据源已经是实时数据源, 则报警并停止任务
        if self.data.is_live_status():
            self.logger.error("历史数据源长度不足, 任务停止")
            return self.env.runstop()
        # 检查停止条件
        if self.next_stop():
            self.logger.warning("prenext阶段任务停止")
            return self.env.runstop()

    def next(self):
        # 检查任务是否退出
        if self.next_stop():
            return self.env.runstop()
        # 心跳log
        self.heartbeat()
        # 是否追单
        self.next_append()
        ## TODO 增加耗时日志打印
        # 判断是否进入
        if self.next_entry():
            return self.submit_open_order()
        # 判断是否退出
        exit = self.next_exit()
        if exit:
            return self.submit_close_order(exit)

    def next_append(self):
        # 判断追单条件
        for append in self._appends:
            if append[0]:
                self.logger.info(
                    f"trigger_append, dt: {bt.num2date(self.data.datetime[0])}"
                )

    def next_entry(self):
        # 判断进入条件是否全部成立
        for entry in self._entrys:
            if not entry[0]:
                return False
        dt = bt.num2date(self.data.datetime[0])
        symbol = self.data.get_symbol()
        log = (
            f"trigger_entry, close: {self.data.close[0]}, volume: {self.data.volume[0]}, dt: {dt}, symbol: {symbol}, "
            + ", ".join(entry.entry_log_string() for entry in self._entrys)
        )
        self.logger.info(log)
        return True

    def next_exit(self):
        # 判断退出条件是否成立
        # 任一退出成立则返回True
        for exit in self._exits:
            if exit[0]:
                dt = bt.num2date(self.data.datetime[0])
                symbol = self.data.get_symbol()
                log = exit.exit_log_string(dt=dt, symbol=symbol)
                self.logger.info(log)
                return exit
        return False

    def submit_open_order(self):
        # 开仓
        # 先检查进入条件是否有开仓, 无开仓则用通用开仓
        for entry in self._entrys:
            if entry.submit_open_order():
                self.logger.info(entry.open_log_string())
                return True
        for _open in self._opens:
            if _open.submit_open_order():
                self.logger.info(_open.open_log_string())
                return True
        self.logger.warning("No suitable open method")
        return False

    def submit_close_order(self, exit):
        # 平仓
        if exit.submit_close_order():
            self.logger.info(exit.close_log_string())
            return True
        for close in self._closes:
            if close.submit_close_order(trigger=exit):
                self.logger.info(close.close_log_string())
                return True
        self.logger.warning("No suitable close method")
        # self.env.runstop()
        # return False

    def next_stop(self):
        for stop in self._stops:
            if stop[0]:
                self.logger.info("trigger_stop_condition: %s", stop.alias[0])
                return True
        return False

    def preheartbeat(self):
        pass

    def heartbeat(self):
        pass

    def bplot(self, **kwargs):
        """
        bokeh作图
        """
        from .plot import BokehStrategyPlotter
        from bokeh.plotting import show

        plotter = BokehStrategyPlotter(**kwargs)
        plots = plotter.plot(self)
        show(plots)

    def report(self):
        """
        生成策略统计报告
        Returns:
            dict: 包含各种统计指标的报告字典
        """
        from .analyzers.report import StrategyReportGenerator

        generator = StrategyReportGenerator(self)
        return generator.generate_report()
