import backtrader as bt
from .resamplefilters import Replayer2
from logging import getLogger
from .utils import (
    num2date,
    freq2timeframe,
    ts2num,
    str2num,
    freq2delta,
    num2ts,
    freq2getendts,
    freq2getstartts,
)

import datetime
import threading

logger = getLogger(__name__)


class DataBase2(bt.feed.DataBase):
    """
    在原有数据源基类上增加必要的属性和方法
    包含了平台 , 交易品种, 交易类别, 频率
    当前时间 curr_dt, bar结束时间 closedt
    Args:
        platform (str); 平台
        symbol (str): 品种
        freq (str): 频率
        days_ago(int): 提前天数, 按照当前时间往前推指定天数
        start_date (str): 开始时间
        end_date (str|None): 结束时间
        is_live (bool): 是否接受实时数据
        replay (bool): 是否回放数据
    Attributes:
        curr_dt (float): 当前数据传输时间
    Lines:
        closedt (float): 当前数据bar关闭时间
        openint (int): 持仓量
        turnover (float): 交易额
    """

    lines = (
        "closedt",
        "openint",
        "turnover",
    )
    params = (
        ("platform", "binance"),
        ("inst_type", "futures"),
        ("symbol", "btcusdt"),
        ("freq", "1m"),
        ("days_ago", None),
        ("start_date", None),
        ("end_date", None),
        ("is_live", True),
        ("replay", False),
    )

    def __init__(self):
        super().__init__()
        self.p.timeframe = freq2timeframe(self.p.freq)["timeframe"]
        self.p.compression = freq2timeframe(self.p.freq)["compression"]

        if self.p.days_ago is not None:  # 指定提前天数
            today = datetime.datetime.combine(datetime.date.today(), datetime.time(8))
            self.fromdate = bt.date2num(
                today - datetime.timedelta(days=self.p.days_ago)
            )
            self.todate = float("inf")
            # self.todate = today + datetime.timedelta(days=1)
        else:  # 指定具体日期
            if self.p.start_date is not None:
                self.fromdate = str2num(self.p.start_date)
            else:
                self.fromdate = float("-inf")
            if self.p.end_date is not None:
                self.todate = str2num(self.p.end_date)
            else:
                self.todate = float("inf")

        self.curr_dt = None  # 当前时间

        self._edge = False  # 当前tick是否为bar的最后一个tick

        self.replaying = self.p.replay

    @property
    def openint(self):
        return self._openint

    def islive(self):
        return self.p.is_live

    def get_symbol(self):
        return self.p.symbol

    def get_platform(self):
        return self.p.platform

    def get_trade_info(self):
        # 获取交易品种信息, 平台, 种类, 品种
        return (self.p.platform, self.p.inst_type, self.get_symbol())

    def get_curr_dt(self):
        # 返回当前时间
        if len(self) < 1:
            return None
        return self.curr_dt if self.curr_dt else self.lines.datetime[0]

    def on_edge(self):
        # 是否为bar的最后一个tick
        return self._edge

    def _tick_fill(self, force=False):
        if self._laststatus == self.LIVE and self.replaying:
            # If nothing filled the tick_xxx attributes, the bar is the tick
            # tick实时数据源只会更新close, 用close更新当前tick
            tick_close = self.close[0]
            prev_tick_close = getattr(self, "tick_close", None)
            for lalias in self.getlinealiases():
                if lalias == "datetime":
                    continue
                if lalias in ["open", "close", "high", "low"]:
                    setattr(self, "tick_" + lalias, tick_close)
                if lalias == "volume":
                    setattr(self, "tick_" + lalias, self.volume[0])
                if lalias == "turnover":
                    setattr(self, "tick_" + lalias, self.turnover[0])
                # 如果存在上一个tick, 则根据两个tick更新tick_ohlc
                if prev_tick_close != None:
                    self.tick_open = prev_tick_close
                    self.tick_high = max(self.tick_open, self.tick_close)
                    self.tick_low = min(self.tick_open, self.tick_close)
        else:
            super()._tick_fill(force=True)

    def replay(self, **kwargs):
        self.addfilter(Replayer2, **kwargs)

    def qbuffer(self, savemem=0, replaying=False):
        # 当数据为live数据时, 长度需+1
        # 当数据为历史数据时, 可能会因为同步时间进行rewind, 此时数据长度也需要+1来满足
        # 长度至少大于等于2, 否则回退数据后不存在数据
        # 因此extrasize直接设置为1
        extrasize = 1
        for line in self.lines:
            line.qbuffer(savemem=savemem, extrasize=extrasize)

    def haslivedata(self):
        return self._laststatus == self.LIVE

    def rewind(self, size=1, force=False):
        if not force:
            super().rewind(size=size)
        else:
            for line in self.lines:
                line.backward(size=size, force=True)
                line.lencount -= size

    def next(self, datamaster=None, ticks=True):
        # 如果时QBuffer, len(self)会随数据增加, 而buflen()则不会
        # 因此原有条件不能再QBuffer条件下成立, 需要新增一个补充条件
        # 根据datetime的坐标和buflen进行判断
        if (
            len(self) >= self.buflen()
            and self.datetime.idx + 1 >= self.datetime.buflen()
        ):
            if ticks:
                self._tick_nullify()

            # not preloaded - request next bar
            ret = self.load()
            if not ret:
                # if load cannot produce bars - forward the result
                logger.info("停止接收数据, 最后接收数据: %s", ret)
                if ret is False:
                    # 停止后续数据读取
                    self.history_mode = self.live_mode = False
                return ret

            if datamaster is None:
                # bar is there and no master ... return load's result
                if ticks:
                    self._tick_fill()
                return ret
        else:
            self.advance(ticks=ticks)

        # a bar is "loaded" or was preloaded - index has been moved to it
        if datamaster is not None:
            # there is a time reference to check against
            if self.lines.datetime[0] > datamaster.lines.datetime[0]:
                # can't deliver new bar, too early, go back
                self.rewind()
                return False
            else:
                if ticks:
                    self._tick_fill()

        else:
            if ticks:
                self._tick_fill()

        # tell the world there is a bar (either the new or the previous
        return True

    def get_closedt(self):
        # 获取收盘时间
        # 如果为replay模式, 收盘时间取开盘时间
        if len(self.closedt) < 1:
            return None
        if self.replaying:
            return self.datetime[0]
        return self.closedt[0]


class HistoricAndLiveDataFeed(DataBase2):
    """
    获取历史数据和实时数据的数据源
    当 is_live 参数为True时, 才会获取实时数据, 否则只获取历史数据
    replay=True且is_live=True时, 实时数据会在每个数据点进行数据投放, 否则只会在当前bar完成时进行投放
    不接受实时数据时, 数据回放没有意义
    当 start_date 参数为None时, 只会获取实时数据
    当 end_date 参数为None时, 才会获取实时数据, 否则只获取历史数据
    2024-03-06 新增:
    实时数据盘口深度, 只保存最新获取值(不一定是最新值)
    Args:
        platform (str); 平台
        symbol (str): 品种
        freq (str): 频率
        start_date (str): 开始时间
        end_date (str|None): 结束时间
        is_live (bool): 是否接受实时数据
        sub_depth (bool): 是否获取盘口信息, 只有实时数据才会生效
    Attributes:
        HistoricLoader (DataLoader): 历史数据载入对象
        LiveLoader (DataLoader): 实时数据载入对象

    Methods:
        history_loaded() 历史数据载入完毕后调用
        start(): 数据源初始化执行内容
        _load() -> bool: 参考bt.DataBase._load 用法
        stop(): 数据源停止执行内容
        islive() -> bool: 参考bt.DataBase.islive 用法
    """

    params = (
        ("platform", "binance"),
        ("inst_type", "futures"),
        ("symbol", "btcusdt"),
        ("freq", "1m"),
        ("days_ago", None),
        ("start_date", None),
        ("end_date", None),
        ("is_live", True),
        ("sub_depth", False),
        ("replay", True),
        ("validate_data", True),
    )
    HistoricLoader: None
    LiveLoader: None

    def __init__(self):
        super().__init__()

        self.history_mode = bool(self.p.start_date or self.p.days_ago)  # 历史数据模式
        self.live_mode = bool(self.p.is_live)  # 实时数据模式

        self.histloader = self._init_histloader()
        if self.p.is_live:
            self.liveloader = self._init_liveloader()
        self.depth = None  # 盘口信息

    @classmethod
    def date2params(cls, date):
        """参数转化为参数字符串"""
        if isinstance(date, str):
            return date
        if date == float("inf") or date == float("-inf"):
            return None
        if isinstance(date, float):
            return str(num2date(date))
        if isinstance(date, int):
            return str(ts2num(date))
        raise KeyError("datetime %s invalid", date)

    def _init_histloader(self):
        return self.HistoricLoader(
            platform=self.p.platform,
            inst_type=self.p.inst_type,
            symbol=self.p.symbol,
            freq=self.p.freq,
            start_date=self.date2params(self.fromdate),
            end_date=self.date2params(self.todate),
        )

    def _init_liveloader(self):
        return self.LiveLoader(
            platform=self.p.platform,
            inst_type=self.p.inst_type,
            symbol=self.p.symbol,
            freq=self.p.freq,
            sub_depth=self.p.sub_depth,
        )

    def start(self):
        # 在 start 方法中启动 Redis 订阅器线程
        super().start()
        if self.p.is_live:
            self.liveloader.start()
        self.histloader.start()
        if self.history_mode:
            self._laststatus = self.CONNECTED
        else:
            self._laststatus = self.LIVE

        # 完成部分start_finish中的工作, 主要为设置tz相关参数
        # Get the output timezone (if any)
        self._tz = self._gettz()
        # Lines have already been create, set the tz
        self.lines.datetime._settz(self._tz)

        # This should probably be also called from an override-able method
        self._tzinput = bt.utils.date.Localizer(self._gettzinput())
        self._started = True

        logger.info(
            f"datafeed start---- model --- {self._getstatusname(self._laststatus)}"
        )

    def preload(self):
        # 提前加载历史数据
        while self.history_mode:
            self.load()
            pass

        # self._last()
        # self.home()

    def _load(self):
        try:
            if self.history_mode:  # 加载历史数据
                data = self.histloader.next()
                if not data:  # 历史数据载入完毕
                    logger.info(
                        f"{self.p.platform} {self.p.symbol} {self.p.freq} 历史数据加载完毕"
                    )
                    logger.info(
                        f"{self.p.platform} {self.p.symbol} {self.p.freq} 历史数据长度 {len(self.datetime)}"
                    )
                    self.history_mode = False

            if not self.history_mode and self.live_mode:  # 加载实时数据
                self._laststatus = self.LIVE
                data = self.liveloader.next()

            if not self.history_mode and not self.live_mode:  # 全部数据加载完毕关闭
                self._laststatus = self.DISCONNECTED
                logger.info(
                    f"{self.p.platform} {self.p.symbol} {self.p.freq} 数据源关闭"
                )
                return False

            # 检查数据缺失检查
            if self.p.validate_data and self.data_is_lost(data):
                logger.warning(
                    f"{self.p.platform} {self.p.symbol} {self.p.freq} 数据缺失"
                )
                self.reload_hist_data(data)
                return None

            # 实时数据replay时, 对当前bar对数据修改
            if (
                self._laststatus == self.LIVE
                and len(self.lines.datetime) >= 2
                and ts2num(data["t"]) == self.lines.datetime[-1]
                and self.replaying
            ):
                self.backwards(force=True)
            # 数据填充
            self.lines.datetime[0] = ts2num(data["t"])
            self.lines.open[0] = data["o"]
            self.lines.high[0] = data["h"]
            self.lines.low[0] = data["l"]
            self.lines.close[0] = data["c"]
            self.lines.volume[0] = data["v"]
            self.lines.turnover[0] = data["q"]
            self.lines.openint[0] = data["oi"]
            self.lines.closedt[0] = ts2num(data["T"])

            # 当前时间
            self.curr_dt = ts2num(data["lt"])

            # edge
            self._edge = data["edge"]

            if not self._edge and not self.replaying:
                return None  # 丢掉当前数据

            self._tick_fill()

            return True

        except Exception as e:
            logger.exception(e)
            self._laststatus == self.DISCONNECTED
            # 异常直接抛出
            raise e
            # return False

    def stop(self):
        if self.p.is_live:
            self.liveloader.stop()
            if isinstance(self.liveloader, threading.Thread):
                self.liveloader.join()  # 多线程进行同步
            logger.info("liveloader stopped")
        self.histloader.stop()
        logger.info("histloader stopped")

    def get_depth_buy(self, n=1):
        # 返回买n 价格, 数量, 默认买1, 没有盘口信息则返回False
        if not self.depth:
            return False
        buy_list = self.depth["b"]
        if len(buy_list) < n:
            logger.info("盘口深度不足")
            return False
        return buy_list[n - 1]

    def get_depth_sell(self, n=1):
        # 返回卖n 价格, 数量, 默认卖1, 没有盘口信息则返回False
        if not self.depth:
            return False
        sell_list = self.depth["a"]
        if len(sell_list) < n:
            logger.info("盘口深度不足")
            return False
        return sell_list[n - 1]

    def data_is_lost(self, data):
        """数据是否缺失"""
        if len(self.lines.datetime) < 2:
            return False
        last_ts = num2ts(self.lines.datetime[-1])
        data_ts = data["t"]
        delta = data_ts - last_ts
        freq_delta = freq2delta(self.p.freq)
        return delta > freq_delta

    def reload_hist_data(self, data):
        """补充历史数据"""
        self.history_mode = True
        last_ts = num2ts(self.lines.datetime[-1])
        data_ts = data["t"]
        logger.warning(
            "补充时间段: %s - %s ",
            datetime.datetime.fromtimestamp(last_ts // 1000),
            datetime.datetime.fromtimestamp(data_ts // 1000),
        )
        raise ValueError(
            f"{self.p.platform} {self.p.symbol} {self.p.freq} 时间源异常: {datetime.datetime.fromtimestamp(last_ts//1000)} - {datetime.datetime.fromtimestamp(data_ts//1000)}"
        )
        # self.histloader.reload_data(last_ts, data_ts)

    def is_live_status(self):
        """是否为实时数据读取状态"""
        return self._laststatus == self.LIVE

    def __repr__(self):
        info = {
            "symbol": self.p.symbol,
            "freq": self.p.freq,
            "fromdate": self.fromdate,
            "todate": self.todate,
            "is_live": self.p.is_live,
            "status": self._laststatus,
        }
        string = ", ".join(f"{k}={v}" for k, v in info.items())
        return f"DataFeed({string})"


class PseudoDataFeed(bt.DataBase):
    """
    虚拟数据源, 模拟数据源输入
    """

    pass


class ResampleDataFeed(DataBase2):
    """
    对现有数据源进行重采样
    Args:
        origin_data: 基准数据源
    """

    params = (
        ("origin_data", None),
        ("replay", True),
    )

    def __init__(self):
        super().__init__()

        self.odata = self.p.origin_data
        # 开始时间获取
        self.get_start_dt = freq2getstartts(self.p.freq)
        # 结束时间获取
        self.get_end_dt = freq2getendts(self.p.freq)
        # 上一个bar的edge状态
        self._last_odata_edge = False
        # 累积交易量
        self._accum_vols = [0]

    def _load(self):
        try:
            if self.odata._laststatus == self.DISCONNECTED:
                return False

            # 当前时间
            self.curr_dt = self.odata.get_curr_dt()
            curr_ts = num2ts(self.curr_dt)
            start_ts = self.get_start_dt(curr_ts)
            end_ts = self.get_end_dt(curr_ts)

            start_dt = ts2num(start_ts)
            close_dt = self.odata.get_closedt() if self.replaying else ts2num(end_ts)

            if len(self.lines.datetime) < 2 or (
                self.lines.datetime[-1] < start_dt and self._last_odata_edge
            ):
                # 长度不够时直接赋值
                # 当前开盘时间大于上一个开盘时间, 且上一个bar为edge, 则视为新的bar开始
                open = self.odata.open[0]
                high = self.odata.high[0]
                low = self.odata.low[0]
                close = self.odata.close[0]
                volume = self.odata.volume[0]
                self._accum_vols = [self.odata.volume[0]]  #  重置累积交易量
            else:
                # 当前开盘时间等于上一个开盘时间, 则进行累计计算
                start_dt = self.lines.datetime[-1]
                open = self.lines.open[-1]
                high = max(self.lines.high[-1], self.odata.high[0])
                low = min(self.lines.low[-1], self.odata.low[0])
                close = self.lines.close[-1]
                if not self._last_odata_edge:
                    # 如果参考数据的上一个bar为非结束状态, 则需要去掉
                    self._accum_vols.pop()
                self._accum_vols.append(self.odata.volume[0])
                volume = sum(self._accum_vols)
                self.backwards(force=True)

            # 数据填充
            self.lines.datetime[0] = start_dt
            self.lines.open[0] = open
            self.lines.high[0] = high
            self.lines.low[0] = low
            self.lines.close[0] = close
            self.lines.volume[0] = volume
            self.lines.closedt[0] = close_dt

            self._edge = self.odata.on_edge()

            # 参考数据的edge, 用于判断收盘时间保持一致
            self._last_odata_edge = self.odata.on_edge()

            # 数据时效性
            self._laststatus = self.odata._laststatus

            if not self._edge and not self.replaying:
                # 先前进再丢掉当前数据, 防止丢失当前计算值
                self.forward()
                return None

            self._tick_fill()

            return True

        except Exception as e:
            logger.exception(e)
            self._laststatus == self.DISCONNECTED
            return False

    def get_closedt(self):
        # 获取收盘时间
        if len(self.closedt) < 1:
            return None
        return self.closedt[0]
