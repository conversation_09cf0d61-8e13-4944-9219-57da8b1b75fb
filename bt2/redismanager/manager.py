import threading
import redis
import json
import datetime
import time
from redis.exceptions import ConnectionError


from ..manager import ManagerBase

from logging import getLogger


logger = getLogger(__name__)


class RedisManager(ManagerBase):
    """
    redis管理器
    """

    params = {
        "host": None,
        "port": None,
        "password": None,
        "db": None,
        "retry": 5,
    }

    @classmethod
    def initcls(cls):
        pass

    def __enter__(self):
        cls = self.__class__
        cls.pool = redis.ConnectionPool(
            host=cls.p.host,
            port=cls.p.port,
            password=cls.p.password,
            db=cls.p.db,
            socket_connect_timeout=30,
            retry_on_timeout=True,
            health_check_interval=5,
        )
        cls.redis = redis.Redis(
            connection_pool=cls.pool,
        )
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        cls = self.__class__
        cls.pool.disconnect()

    @classmethod
    def get_pubsub(cls):
        return cls.redis.pubsub()


class KLineRedisManager(RedisManager):
    """k线redis管理器"""

    pass


class ChannelRedisManager(RedisManager):
    """
    channel redis管理器,
    将参数以hash格式存放在指定频道中
    """

    ConfigKey = "CHANNEL_REDIS"

    params = {"channel": "DynParams", "keys": None}

    @classmethod
    def set_key(cls, dyn_key):
        """设置key值"""
        if not isinstance(dyn_key, list):
            cls(keys=[str(dyn_key)])
        else:
            cls(keys=dyn_key)

    @classmethod
    def _set_value(cls, pip, key, value):
        """设置值, 执行事务"""
        try:
            pip.hset(cls.p.channel, str(key), json.dumps(value))
            logger.debug("pip set key %s: %s", key, value)
        except Exception as e:  # 捕获所有异常进行观察
            logger.warning("redis set value error")
            logger.exception(e)

    @classmethod
    def set_value(cls, key, value):
        """设置值"""
        cls.redis.hset(cls.p.channel, str(key), json.dumps(value))
        logger.debug("set key %s: %s", key, value)

    @classmethod
    def update_value(cls, key, value):
        """更新值"""
        try:
            with cls.redis.pipeline() as pipe:
                while True:
                    try:
                        # 监视键，确保在事务期间没有其他客户端修改它
                        pipe.watch(cls.p.channel)

                        # 获取当前值
                        current_value = cls.get_one_value(key)
                        # 更新值
                        current_value.update(value)
                        # 启动事务
                        pipe.multi()
                        # 设置新值
                        cls._set_value(pipe, key, current_value)
                        # 执行事务
                        pipe.execute()

                        logger.debug("update key %s: %s", key, current_value)
                        break
                    except redis.WatchError:
                        # 如果监视的键在事务期间被修改了，则重试
                        logger.warning("key has been modified, retry")
                        continue
        except Exception as e:
            logger.warning("redis update value error")
            logger.exception(e)

    @classmethod
    def remove_value_key(cls, key, value_key):
        """删除值内部的某个key"""
        try:
            with cls.redis.pipeline() as pipe:
                while True:
                    try:
                        # 监视键，确保在事务期间没有其他客户端修改它
                        pipe.watch(cls.p.channel)
                        # 获取当前值
                        current_value = cls.get_one_value(key)
                        # 更新值
                        current_value.pop(value_key, None)
                        # 启动事务
                        pipe.multi()
                        # 设置新值
                        cls._set_value(pipe, key, current_value)
                        # 执行事务
                        pipe.execute()

                        logger.debug("remove_value_key %s: %s", key, value_key)
                        break
                    except redis.WatchError:
                        # 如果监视的键在事务期间被修改了，则重试
                        logger.warning("key has been modified, retry")
                        continue
        except Exception as e:
            logger.warning("redis remove value error")
            logger.exception(e)

    @classmethod
    def delete_value(cls, key):
        """删除值"""
        if cls.redis.exists(key):
            cls.redis.delete(key)
            logger.info("delete key %s", key)

    @classmethod
    def get_value(cls, key=None) -> dict:
        """获取key对应的值, 返回字典类型值
        如果key为列表, 则依次取值, 优先级从高到低
        """
        if key != None:
            if not isinstance(key, list):
                keys = [str(key)]
            else:
                keys = key
        else:
            if isinstance(cls.p.keys, str):
                keys = [cls.p.keys]
            else:
                keys = cls.p.keys
        params = {}
        for k in keys[::-1]:
            params.update(cls.get_one_value(k))
        return params

    @classmethod
    def get_one_value(cls, key):
        try:
            value = cls.redis.hget(cls.p.channel, key)
            if value == None:  # 字段不存在
                return {}
            return json.loads(value.decode())
        except Exception as e:
            logger.warning("redis get_one_value error: %s", e)
            return {}

    @classmethod
    def check_connection(cls):
        """检查连接状态"""
        pubsub = cls.get_pubsub()
        try:
            pubsub.ping()
        except redis.ConnectionError as e:
            logger.error("Ping failed: Connection lost")
            return True
        return True


class DynParamRedisManager(ChannelRedisManager):
    """
    动态参数redis管理器,
    将动态参数以hash格式存放在指定频道中
    """

    ConfigKey = "DYN_REDIS"

    params = {
        "channel": "DynParams",
    }


class RunControlRedisManager(ChannelRedisManager):
    """
    运行控制管理器
    """

    ConfigKey = "DYN_REDIS"

    params = {
        "channel": "RunControl",
    }

    @classmethod
    def get_control(cls, key=None):
        """获取control"""
        params = cls.get_value(key=key)
        return params.get("control", None)

    @classmethod
    def set_control(cls, key, control):
        """设置control, 附上时间戳"""
        dt = int(time.time() * 1000)
        cls.update_value(key, {"control": control, "updatetime": dt})
        res = cls.get_control(key=key)
        logger.info("设置 control %s: %s, 设置结果: %s", key, control, res)


class OneRedisManager(ManagerBase):
    """
    redis管理器, 只存在一个redis对象
    """

    params = {
        "host": None,
        "port": None,
        "password": None,
        "db": None,
        "retry": 5,
    }

    CHANNEL_QUEUE = {}  # 队列字典
    _pubsub = None
    _thread = None

    @classmethod
    def initcls(cls):
        pass

    def __enter__(self):
        logger.info("创建redis线程对象")
        cls = self.__class__
        cls.pool = redis.ConnectionPool(
            host=cls.p.host,
            port=cls.p.port,
            password=cls.p.password,
            db=cls.p.db,
            socket_connect_timeout=30,
            retry_on_timeout=True,
            health_check_interval=5,
        )
        cls.redis = redis.Redis(
            connection_pool=cls.pool,
        )

    def __exit__(self, exc_type, exc_value, traceback):
        cls = self.__class__
        if cls._pubsub:
            cls._pubsub.close()
            cls._thread.join()
            cls.CHANNEL_QUEUE = {}
            cls._pubsub = None
            cls._thread = None

    @classmethod
    def get_pubsub(cls):
        if cls._pubsub:
            return cls._pubsub
        # 不存在对象时, 获取对象并创建线程
        cls._pubsub = cls.redis.pubsub()
        cls._thread = threading.Thread(target=cls.wait_for_messages)
        cls._thread.daemon = True  # 设置为守护线程，确保主程序退出时线程也会退出
        return cls._pubsub

    @classmethod
    def subscribe(cls, channel, data_queue):
        pubsub = cls.get_pubsub()
        if channel in cls.CHANNEL_QUEUE:
            raise KeyError(f"{channel} 已经存在redis现有队列, 检查数据源是否重复")
        cls.CHANNEL_QUEUE[channel] = data_queue
        pubsub.subscribe(channel)
        return pubsub

    @classmethod
    def start(cls):
        # 检查并启动线程
        if not cls._thread.is_alive():
            cls._thread.start()

    @classmethod
    def stop(cls):
        if cls._pubsub:
            cls._pubsub.close()
            cls._thread.join()

    @classmethod
    def thread_alive(cls):
        """检查线程是否存活"""
        return cls._thread.is_alive()

    @classmethod
    def wait_for_messages(cls):
        NotImplemented

    @classmethod
    def check_connection(cls):
        """检查连接状态"""
        if not cls.thread_alive():
            logger.error("redis thread is dead, check logs for more information")
            return False
        pubsub = cls.get_pubsub()
        try:
            pubsub.ping()
        except redis.ConnectionError as e:
            logger.error("Ping failed: Connection lost")
            return True
        return True


class MultiKLineRedisManager(OneRedisManager):
    """kline redis 管理器, 可订阅多个频道"""

    ConfigKey = "LIVE_REDIS"

    # 处理信息循环
    @classmethod
    def wait_for_messages(cls):
        res = cls.get_pubsub().listen()
        _retry = 0
        while _retry < cls.p.retry:
            try:
                for message in res:
                    _retry = 0  # 重置重试次数
                    if message["type"] == "subscribe":
                        continue
                    if message["type"] == "unsubscribe":
                        return
                    if message["type"] == "message":
                        channel = message["channel"].decode()
                        data_queue = cls.CHANNEL_QUEUE[channel]
                        data = json.loads(message["data"])
                        if "tick" in channel:
                            d = data
                            d["t"] = d["lt"]
                            d["o"] = float(d["p"])
                            d["h"] = float(d["p"])
                            d["l"] = float(d["p"])
                            d["c"] = float(d["p"])
                            d["v"] = float(d["q"])
                            d["lt"] = d["lt"]  # 当前时间
                            d["oi"] = float(d["oi"])
                            d["edge"] = True
                        else:
                            d = data["d"]
                            d["t"] = d["t"]
                            d["o"] = float(d["o"])
                            d["h"] = float(d["h"])
                            d["l"] = float(d["l"])
                            d["c"] = float(d["c"])
                            d["v"] = float(d["v"])
                            d["q"] = float(d["q"])
                            d["lt"] = data["lt"]  # 当前时间
                            d["oi"] = float(d["oi"])
                            d["edge"] = d["x"]
                        data_queue.put(d)
                        logger.debug("redis data received: %s", d)
                        continue
            except ValueError as e:
                if "I/O operation on closed file." in str(e):
                    logger.info(f"multi kline redis pubsub closed")
                    return
            except ConnectionError:
                logger.warning("kline redis disconnected, try to reconnect")
                _retry += 1
                time.sleep(5)
            except Exception as e:
                logger.error("未知异常")
                logger.exception(e)
                raise e
        logger.error("实时数据 redis 重试次数达到上限")
        return


class MultiOpenIntRedisManager(OneRedisManager):
    """持仓量 redis 管理器, 可订阅多个频道"""

    ConfigKey = "LIVE_REDIS"

    # 处理信息循环
    @classmethod
    def wait_for_messages(cls):
        res = cls.get_pubsub().listen()
        _retry = 0
        while _retry < cls.p.retry:
            try:
                for message in res:
                    _retry = 0  # 重置重试次数
                    if message["type"] == "subscribe":
                        continue
                    if message["type"] == "unsubscribe":
                        return
                    if message["type"] == "message":
                        channel = message["channel"].decode()
                        obj = cls.CHANNEL_QUEUE[channel]
                        data = json.loads(message["data"])
                        obj["openint"] = data["data"]
                        logger.debug("redis data received: %s", data)
                        continue
            except ValueError as e:
                if "I/O operation on closed file." in str(e):
                    logger.info(f"multi openint redis pubsub closed")
                    return
            except ConnectionError:
                logger.warning("openint redis disconnected, try to reconnect")
                _retry += 1
                time.sleep(5)
            except Exception as e:
                logger.error("未知异常")
                logger.exception(e)
                raise e
        logger.error("实时持仓数据 redis 重试次数达到上限")
        return


class PlatIndRedisManager(RedisManager):
    """
    平台指标管理器
    """

    ConfigKey = "PLATIND_REDIS"

    """动态参数redis管理器"""

    @classmethod
    def get_current_index_all(cls, key, tolerance_seconds=60):
        """获取当前时刻取值
        对时间戳进行校验
        """
        value = cls.redis.hgetall(key)
        if not value:
            return {}
        value = {k.decode(): json.loads(v.decode()) for k, v in value.items()}
        ts = value.pop("updateTime")
        dt = datetime.datetime.fromtimestamp(ts / 1000)
        if tolerance_seconds <= 0:  # 不做校验
            return value
        if abs((datetime.datetime.now() - dt).total_seconds()) < tolerance_seconds:
            return value
        else:
            logger.warning("指标数据 %s 未及时更新, 更新时间: %s", key, dt)
            return {}

    @classmethod
    def get_current_index(cls, key, symbol, tolerance_seconds=60):
        """获取当前时刻取值, 取单个币种
        对时间戳进行校验
        """
        value = cls.redis.hget(key, symbol)
        if not value:
            return {}
        value = {k: v for k, v in json.loads(value.decode()).items()}
        ts = value.pop("updateTime")
        dt = datetime.datetime.fromtimestamp(ts / 1000)
        if tolerance_seconds <= 0:  # 不做校验
            return value
        if abs((datetime.datetime.now() - dt).total_seconds()) < tolerance_seconds:
            return value
        else:
            logger.warning("指标数据 %s 未及时更新, 更新时间: %s", key, dt)
            return {}


class RiskRedisManager(OneRedisManager):
    """
    风控管理器
    """

    ConfigKey = "RISK_REDIS"

    # 处理信息循环
    @classmethod
    def wait_for_messages(cls):
        pubsub = cls.get_pubsub()
        _retry = 0
        while _retry < cls.p.retry:
            try:
                res = pubsub.listen()
                for message in res:
                    _retry = 0  # 重置重试次数
                    if message["type"] == "unsubscribe":
                        break
                    if message["type"] == "message":
                        channel = message["channel"].decode()
                        event = cls.CHANNEL_QUEUE[channel]
                        data = message["data"].decode()
                        if data == "stop":
                            event.stop()
                            logger.info("risk stop: %s", data)
                        if data == "warning":
                            event.warn()
                            logger.warning("risk warning: %s", data)
                        if data == "normal":
                            event.normal()
                            logger.debug("risk normal: %s", data)
                        continue
            except ValueError as e:
                if "I/O operation on closed file." in str(e):
                    logger.info(f"risk redis pubsub closed")
                    return
            except ConnectionError:
                logger.warning("risk redis disconnected, try to reconnect")
                _retry += 1
                time.sleep(5)
            except Exception as e:
                logger.error("nuknown Exception catched")
                logger.exception(e)
                raise e
        logger.error("风控 redis 重试次数达到上限")
        return
