# 指标存储管理对象

from ..manager import ManagerBase
from ..context import DBTaskStatusContext
from logging import getLogger
import os


logger = getLogger(__name__)


class IndCsvManager(ManagerBase):
    """
    指标存储csv管理器
    """

    ConfigKey = "INDICATOR_CSV"

    params = (("filedir", ""),)

    @classmethod
    def initcls(cls):
        directory = cls.p.filedir
        cls._create_dir(directory)

    @classmethod
    def get_task_file_path(cls, filename: str, task_name=""):
        """获取任务文件路径"""
        if task_name:
            task_name = str(task_name)
        else:
            task_name = str(DBTaskStatusContext.get_current_task_name())
        filepath = os.path.join(cls.p.filedir, task_name, filename)
        directory = os.path.dirname(filepath)
        cls._create_dir(directory)
        return filepath

    @classmethod
    def _create_dir(cls, directory):
        try:
            if not os.path.exists(directory):
                logger.info("创建指标存储文件夹 %s", directory)
                os.makedirs(directory)
        except Exception as e:
            logger.error("创建指标存储文件夹 %s 失败")
            raise e
