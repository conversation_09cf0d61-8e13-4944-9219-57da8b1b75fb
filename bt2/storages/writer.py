import os
import csv
import json
from logging import getLogger


logger = getLogger(__name__)


class CSVWriter:
    """
    csv读写对象, 用于存储指标数值
    提供接口便于操作
    """

    def __init__(self, filepath: str, mode="a", header=[], date=None):
        if not filepath.endswith(".csv"):
            # 增加文件后缀
            filepath += ".csv"
        self.filepath = filepath
        self.header = header
        self.open_file(mode=mode, date=date)

    def buffer_size(self):
        return len(self.buffer)

    def writeline(self, data):
        self.buffer.append(data)

    def writelines(self, datas):
        self.buffer.extend(datas)

    def flush(self):
        logger.info("准备写入数据长度: %s", self.buffer_size())
        self.writer.writerows(self.buffer)
        self.file.flush()
        self.buffer = []
        logger.info("写入数据完毕")

    def close(self):
        self.flush()
        self.file.close()

    def rotate_file(self, date):
        """归档旧文件, 创建新文件"""
        self.close()
        self.open_file(mode="w", date=date)

    def open_file(self, mode, date=None):
        """打开文件"""
        if date:
            # 增加日期文件夹
            filedir, file = os.path.split(self.filepath)
            filedatedir = os.path.join(filedir, str(date))
            if not os.path.exists(filedatedir):
                os.makedirs(filedatedir)
            self.filedatepath = os.path.join(filedatedir, file)
        else:
            self.filedatepath = self.filepath
        self.file = open(self.filedatepath, mode=mode)
        self.writer = csv.writer(self.file)
        self.buffer = []
        if (
            not os.path.exists(self.filedatepath)
            or os.stat(self.filedatepath).st_size == 0
        ):
            # 文件不存在或没有内容时写入表头
            self.buffer.append(self.header)
            self.flush()


class JSONWriter:
    """JSON读写对象, 用于存储指标基础信息"""

    def __init__(self, filepath: str, info: dict):
        if not filepath.endswith(".json"):
            filepath += ".json"
        with open(filepath, mode="w") as fp:
            json.dump(info, fp=fp)
