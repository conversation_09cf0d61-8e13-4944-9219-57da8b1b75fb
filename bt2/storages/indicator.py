### 指标用存储器示例

import backtrader as bt
from ..storage import Storage
from logging import getLogger
from ..context import DBTaskStatusContext

from .manager import IndCsvManager
from concurrent.futures import ThreadPoolExecutor
from .writer import CSVWriter, JSONWriter
import datetime
import pandas as pd


logger = getLogger(__name__)


class LivePlotIndicatorCSVStorage(Storage):
    """
    指标存储器, 储存为CSV文件格式
    只追加数据, 不负责检查已有数据的情况
    只存实时数据, 不存历史数据
    每一个tick都存, 且不覆盖
    """

    # 缓存最大长度
    CACHE_MAX_LENGTH = 120
    WRITE_MODE = "w"
    APPEND_MODE = "a"

    def __init__(
        self, strategy: bt.Strategy, redump=False, only_true=True, save_on_edge=True
    ):
        # 策略引用
        self.strategy = strategy
        # 任务id
        self.task_id = DBTaskStatusContext.get_current_task_id()
        # 当前日期, 用于切割文件
        self.curr_date = datetime.datetime.now().date()
        # 是否只存有效数据
        self.only_true = only_true
        # 是否只在当前bar结束时储存
        self.save_on_edge = save_on_edge
        # 存储数据对象引用
        self.plot_ind = {}
        # 存储数据源引用
        self.data = strategy.data
        # 获取指标
        for ind in strategy.get_plotindicators():
            plotinfo = ind.get_plotinfo()
            key = plotinfo["plotname"]
            if key in self.plot_ind:
                raise KeyError(f"指标名重复 {key}")
            self.plot_ind[key] = ind
        if redump:
            # 清空历史数据
            mode = self.WRITE_MODE
        else:
            mode = self.APPEND_MODE
        # 创建指标存储文件对象
        self.writer_dict = {}
        for key, ind in self.plot_ind.items():
            fields = list(ind.getlinealiases())
            header = ["timestamp"] + fields
            writer = CSVWriter(
                IndCsvManager.get_task_file_path(key),
                mode=mode,
                header=header,
                date=self.curr_date,
            )
            self.writer_dict[key] = writer
            # 保存指标信息
            ind_info = {
                "task_id": self.task_id,
                "stra_attr": key,
                "ind_cls": ind.__class__.__name__,
                "fields": fields,
            }
            JSONWriter(IndCsvManager.get_task_file_path(key), ind_info)
        # 创建单线程池, 确保顺序执行
        self._executor = ThreadPoolExecutor(max_workers=1)

    def next(self):
        # 只保存实时数据
        if not self.data.is_live_status():
            return
        self.check_curr_date()
        curr_ts = int(bt.num2date(self.data.get_curr_dt()).timestamp() * 1000)
        on_edge = self.data.on_edge()
        for key, ind in self.plot_ind.items():
            data = [curr_ts]
            for field in ind.getlinealiases():
                data.append(float(getattr(ind.lines, field)[0]))
            if not all(data[1:]) and self.only_true:
                # 所有值为0且只保存有效值时跳过保存
                continue
            writer = self.writer_dict[key]
            writer.writeline(data)
            # 写入数据
            if writer.buffer_size() > self.CACHE_MAX_LENGTH or (
                on_edge and self.save_on_edge
            ):
                self._executor.submit(writer.flush)

    def _stop(self):
        for writer in self.writer_dict.values():
            writer.close()
        self._executor.shutdown(wait=True)

    def check_curr_date(self):
        """检查日期并切割文件"""
        curr_date = bt.num2date(self.data.get_curr_dt()).date()
        if curr_date != self.curr_date:
            for writer in self.writer_dict.values():
                writer.rotate_file(curr_date)
        self.curr_date = curr_date


class ConditionIndicatorCSVStorage(Storage):
    """
    条件指标存储器, 储存为CSV文件格式
    每一个tick都存
    """

    # 缓存最大长度
    CACHE_MAX_LENGTH = 1000
    WRITE_MODE = "w"
    APPEND_MODE = "a"

    def __init__(
        self,
        strategy: bt.Strategy,
        redump=False,
        only_true=True,
        save_on_edge=True,
        simulate=False,
    ):
        self.simulate = simulate
        # 策略引用
        self.strategy = strategy
        # 任务id
        self.task_id = DBTaskStatusContext.get_current_task_id()
        # 当前日期, 用于切割文件
        self.curr_date = datetime.datetime.now().date()
        # 是否只存有效数据
        self.only_true = only_true
        # 是否只在当前bar结束时储存
        self.save_on_edge = save_on_edge
        # 存储数据对象引用
        self.plot_ind = {}
        # 存储数据源引用
        self.data = strategy.data
        # 获取指标
        for cond in strategy.get_conditions():
            self.plot_ind[cond.get_name()] = cond
            for key, ind in cond.get_indicator_dict().items():
                plotinfo = ind.get_plotinfo()
                if plotinfo["plot"]:
                    plot_key = ".".join([cond.get_name(), key])
                    if plot_key in self.plot_ind:
                        raise KeyError(f"指标名重复 {plot_key}")
                    self.plot_ind[plot_key] = ind
        if redump or self.simulate:
            # 清空历史数据
            mode = self.WRITE_MODE
        else:
            mode = self.APPEND_MODE
        # 创建指标存储文件对象
        self.writer_dict = {}
        for key, ind in self.plot_ind.items():
            fields = list(ind.getlinealiases())
            header = ["timestamp"] + fields
            writer = CSVWriter(
                IndCsvManager.get_task_file_path(key), mode=mode, header=header
            )
            self.writer_dict[key] = writer
            # 保存指标信息
            ind_info = {
                "task_id": self.task_id,
                "stra_attr": key,
                "ind_cls": ind.__class__.__name__,
                "fields": fields,
            }
            ind_info.update(ind.get_plotinfo())
            JSONWriter(IndCsvManager.get_task_file_path(key), ind_info)
        # 创建单线程池, 确保顺序执行
        self._executor = ThreadPoolExecutor(max_workers=1)

    def next(self):
        # 只保存实时数据
        if self.simulate or self.data.is_live_status():
            curr_ts = int(bt.num2date(self.data.get_curr_dt()).timestamp() * 1000)
            on_edge = self.data.on_edge()
            for key, ind in self.plot_ind.items():
                data = [curr_ts]
                for field in ind.getlinealiases():
                    data.append(float(getattr(ind.lines, field)[0]))
                if not all(data[1:]) and self.only_true:
                    # 所有值为0或nan且只保存有效值时跳过保存
                    continue
                writer = self.writer_dict[key]
                writer.writeline(data)
                # 写入数据
                if writer.buffer_size() > self.CACHE_MAX_LENGTH or (
                    (on_edge and self.save_on_edge) and not self.simulate
                ):
                    self._executor.submit(writer.flush)

    def _stop(self):
        for writer in self.writer_dict.values():
            writer.close()
        self._executor.shutdown(wait=True)

    def to_reports(self):
        reports = {}
        for ind, writer in self.writer_dict.items():
            file_path = writer.filepath
            reports[ind] = pd.read_csv(file_path)
        return reports


class LiveConditionIndicatorCSVStorage(Storage):
    """
    条件指标存储器, 储存为CSV文件格式
    只追加数据, 不负责检查已有数据的情况
    只存实时数据, 不存历史数据
    每一个tick都存, 且不覆盖
    """

    # 缓存最大长度
    CACHE_MAX_LENGTH = 120
    WRITE_MODE = "w"
    APPEND_MODE = "a"

    def __init__(
        self, strategy: bt.Strategy, redump=False, only_true=True, save_on_edge=True
    ):
        # 策略引用
        self.strategy = strategy
        # 任务id
        self.task_id = DBTaskStatusContext.get_current_task_id()
        # 当前日期, 用于切割文件
        self.curr_date = datetime.datetime.now().date()
        # 是否只存有效数据
        self.only_true = only_true
        # 是否只在当前bar结束时储存
        self.save_on_edge = save_on_edge
        # 存储数据对象引用
        self.plot_ind = {}
        # 存储数据源引用
        self.data = strategy.data
        # 获取指标
        for cond in strategy.get_conditions():
            self.plot_ind[cond.get_name()] = cond
            for key, ind in cond.get_indicator_dict().items():
                plotinfo = ind.get_plotinfo()
                if plotinfo["plot"]:
                    plot_key = ".".join([cond.get_name(), key])
                    if plot_key in self.plot_ind:
                        raise KeyError(f"指标名重复 {plot_key}")
                    self.plot_ind[plot_key] = ind
        if redump:
            # 清空历史数据
            mode = self.WRITE_MODE
        else:
            mode = self.APPEND_MODE
        # 创建指标存储文件对象
        self.writer_dict = {}
        for key, ind in self.plot_ind.items():
            fields = list(ind.getlinealiases())
            header = ["timestamp"] + fields
            writer = CSVWriter(
                IndCsvManager.get_task_file_path(key),
                mode=mode,
                header=header,
                date=self.curr_date,
            )
            self.writer_dict[key] = writer
            # 保存指标信息
            ind_info = {
                "task_id": self.task_id,
                "stra_attr": key,
                "ind_cls": ind.__class__.__name__,
                "fields": fields,
            }
            ind_info.update(ind.get_plotinfo())
            JSONWriter(IndCsvManager.get_task_file_path(key), ind_info)
        # 创建单线程池, 确保顺序执行
        self._executor = ThreadPoolExecutor(max_workers=1)

    def next(self):
        # 只保存实时数据
        if not self.data.is_live_status():
            return
        self.check_curr_date()
        curr_ts = int(bt.num2date(self.data.get_curr_dt()).timestamp() * 1000)
        on_edge = self.data.on_edge()
        for key, ind in self.plot_ind.items():
            data = [curr_ts]
            for field in ind.getlinealiases():
                data.append(float(getattr(ind.lines, field)[0]))
            if not all(data[1:]) and self.only_true:
                # 所有值为0且只保存有效值时跳过保存
                continue
            writer = self.writer_dict[key]
            writer.writeline(data)
            # 写入数据
            if writer.buffer_size() > self.CACHE_MAX_LENGTH or (
                on_edge and self.save_on_edge
            ):
                self._executor.submit(writer.flush)

    def _stop(self):
        for writer in self.writer_dict.values():
            writer.close()
        self._executor.shutdown(wait=True)

    def check_curr_date(self):
        """检查日期并切割文件"""
        curr_date = bt.num2date(self.data.get_curr_dt()).date()
        if curr_date != self.curr_date:
            for writer in self.writer_dict.values():
                writer.rotate_file(curr_date)
        self.curr_date = curr_date
