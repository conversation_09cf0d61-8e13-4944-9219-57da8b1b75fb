### 交易存储器

import backtrader as bt
from ..storage import Storage
from logging import getLogger
from ..context import DBTaskStatusContext
from ..trade import EventTrade

from .manager import IndCsvManager
from concurrent.futures import ThreadPoolExecutor
from .writer import CSVWriter
import pandas as pd


logger = getLogger(__name__)


class TradeCSVStorage(Storage):
    """
    交易存储器, 储存为CSV文件格式
    只追加数据, 不负责检查已有数据的情况
    """

    # 缓存最大长度
    CACHE_MAX_LENGTH = 1000
    WRITE_MODE = "w"
    APPEND_MODE = "a"

    TradeCls = EventTrade

    def __init__(self, strategy: bt.Strategy, redump=False, simulate=False):
        # 执行模式
        self.simulate = simulate
        # 策略引用
        self.strategy = strategy
        self.data = strategy.data
        # 任务id
        self.task_id = DBTaskStatusContext.get_current_task_id()

        if redump or self.simulate:
            # 清空历史数据
            mode = self.WRITE_MODE
        else:
            mode = self.APPEND_MODE
        # 创建交易存储文件对象
        self.writer = CSVWriter(
            IndCsvManager.get_task_file_path("trade"),
            mode=mode,
            header=self.TradeCls.csv_header,
        )
        # 创建单线程池, 确保顺序执行
        self._executor = ThreadPoolExecutor(max_workers=1)

    def next(self):
        # 只保存实时数据
        if not self.simulate and not self.data.is_live_status():
            return
        if self.strategy._tradespending:
            for trade in self.strategy._tradespending:
                if trade.isclosed:
                    data = trade.to_data()
                    self.writer.writeline(data)
                    # 写入数据
            if not self.simulate or self.writer.buffer_size() > self.CACHE_MAX_LENGTH:
                self._executor.submit(self.writer.flush)

    def _stop(self):
        self.writer.close()
        self._executor.shutdown(wait=True)

    def to_report(self):
        file_path = self.writer.filepath
        report = pd.read_csv(file_path)
        return report
