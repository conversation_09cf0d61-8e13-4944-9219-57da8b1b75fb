### 资金线存储器

import backtrader as bt
from ..storage import Storage
from logging import getLogger
from concurrent.futures import ThreadPoolExecutor
from .writer import CSVWriter
from .manager import IndCsvManager
import pandas as pd


logger = getLogger(__name__)


class EquityCSVStorage(Storage):
    """
    实时资金线存储器, 用于保存资金线
    当载入历史数据时, 缓存长度超过最大缓存长度则会上传一次数据
    """

    # 缓存最大长度
    CACHE_MAX_LENGTH = 120
    WRITE_MODE = "w"
    APPEND_MODE = "a"

    def __init__(
        self,
        strategy: bt.Strategy,
        redump=False,
        only_true=True,
        save_on_edge=True,
        simulate=False,
    ):
        self.simulate = simulate
        # 策略名称
        self.strategy = strategy
        # 是否只存有效数据
        self.only_true = only_true
        # 是否只在当前bar结束时储存
        self.save_on_edge = save_on_edge
        if redump or self.simulate:
            # 清空历史数据
            mode = self.WRITE_MODE
        else:
            mode = self.APPEND_MODE
        self.writer = CSVWriter(
            IndCsvManager.get_task_file_path("equity"),
            mode=mode,
            header=["timestamp", "equity"],
        )
        # 存储数据源引用
        self.data = strategy.data
        # 创建单线程池, 确保顺序执行
        self._executor = ThreadPoolExecutor(max_workers=1)

    def next(self):
        # 只保存实时数据
        if self.simulate or self.data.is_live_status():
            curr_ts = int(bt.num2date(self.data.get_curr_dt()).timestamp() * 1000)
            on_edge = self.data.on_edge()
            self.writer.writeline([curr_ts, self.strategy.broker.getvalue()])
            if self.writer.buffer_size() > self.CACHE_MAX_LENGTH or (
                (on_edge and self.save_on_edge) and not self.simulate
            ):
                self._executor.submit(self.writer.flush)

    def _stop(self):
        self.writer.close()
        self._executor.shutdown(wait=True)

    def to_report(self):
        file_path = self.writer.filepath
        report = pd.read_csv(file_path)
        return report

    def to_plot(self):
        pass


class LiveEquityCSVStorage(Storage):
    """
    实时资金线存储器, 用于保存资金线
    当载入历史数据时, 缓存长度超过最大缓存长度则会上传一次数据
    """

    # 缓存最大长度
    CACHE_MAX_LENGTH = 120
    WRITE_MODE = "w"
    APPEND_MODE = "a"

    def __init__(
        self, strategy: bt.Strategy, redump=False, only_true=True, save_on_edge=True
    ):
        # 策略名称
        self.strategy = strategy
        # 是否只存有效数据
        self.only_true = only_true
        # 是否只在当前bar结束时储存
        self.save_on_edge = save_on_edge
        if redump:
            # 清空历史数据
            mode = self.WRITE_MODE
        else:
            mode = self.APPEND_MODE
        self.writer = CSVWriter(
            IndCsvManager.get_task_file_path("equity"),
            mode=mode,
            header=["timestamp", "equity"],
        )
        # 存储数据源引用
        self.data = strategy.data
        # 创建单线程池, 确保顺序执行
        self._executor = ThreadPoolExecutor(max_workers=1)

    def next(self):
        # 只保存实时数据
        if not self.data.is_live_status():
            return
        curr_ts = int(bt.num2date(self.data.get_curr_dt()).timestamp() * 1000)
        on_edge = self.data.on_edge()
        self.writer.writeline([curr_ts, self.strategy.broker.getvalue()])
        if self.writer.buffer_size() > self.CACHE_MAX_LENGTH or (
            on_edge and self.save_on_edge
        ):
            self._executor.submit(self.writer.flush)

    def _stop(self):
        self.writer.close()
        self._executor.shutdown(wait=True)
