## 控制中心
import itertools
import backtrader as bt
from backtrader import (
    num2date,
    tzparse,
    integer_types,
    OptReturn,
)
from .observers import (
    LiveDrawDown,
    HoldingPeriod,
)
from logging import getLogger


logger = getLogger(__name__)


class Cerebro2(bt.Cerebro):
    """
        在原有的cerebro基础上修改
        1. 增加指标存储器功能
        2. 针对自定义数据源进行逻辑修改. 逻辑文档见飞书(https://ji9xzzjt0z.feishu.cn/wiki/CWxowQ2WLiZ7y2ky1UncVVLfn1d)

    Args:
        store_notify: 存储通知开关, 开启后则会在存储器运行完成一次时发送订阅信息, 包含数据源最新发送时间
        live_mode: 实时模式

    Methods:
        addstorage(bt2.storage.Storage, *args, **kwargs): 对策略增加指标存储器
    """

    params = (
        ("store_notify", False),
        ("live_mode", True),
    )

    def __init__(self):
        super().__init__()
        self.storages = list()

    def addstorage(self, storage, *args, **kwargs):
        # 添加指标存储器
        self.storages.append([storage, args, kwargs])
        return len(self.storages) - 1

    def runstrategies(self, iterstrat, predata=False):
        """
        Internal method invoked by ``run``` to run a set of strategies
        原有逻辑添加指标存储器部分
        """
        self._init_stcount()

        self.runningstrats = runstrats = list()

        # 启动store, 暂时没用
        for store in self.stores:
            store.start()

        if self.p.cheat_on_open and self.p.broker_coo:
            # try to activate in broker
            if hasattr(self._broker, "set_coo"):
                self._broker.set_coo(True)

        if self._fhistory is not None:
            self._broker.set_fund_history(self._fhistory)

        for orders, onotify in self._ohistory:
            self._broker.add_order_history(orders, onotify)

        # 关联broker和datas
        if hasattr(self._broker, "datas"):
            self._broker.init_datas(self.datas)

        self._broker.start()

        for feed in self.feeds:
            feed.start()

        if self.writers_csv:
            wheaders = list()
            for data in self.datas:
                if data.csv:
                    wheaders.extend(data.getwriterheaders())

            for writer in self.runwriters:
                if writer.p.csv:
                    writer.addheaders(wheaders)

        # self._plotfillers = [list() for d in self.datas]
        # self._plotfillers2 = [list() for d in self.datas]

        if not predata:
            for data in self.datas:
                data.reset()
                if self._exactbars < 1:  # datas can be full length
                    data.extend(size=self.params.lookahead)
                data._start()
                if self._dopreload:
                    data.preload()

        for stratcls, sargs, skwargs in iterstrat:
            sargs = self.datas + list(sargs)
            try:
                strat = stratcls(*sargs, **skwargs)
            except bt.errors.StrategySkipError:
                continue  # do not add strategy to the mix

            if self.p.oldsync:
                strat._oldsync = True  # tell strategy to use old clock update
            if self.p.tradehistory:
                strat.set_tradehistory()
            runstrats.append(strat)

        tz = self.p.tz
        if isinstance(tz, integer_types):
            tz = self.datas[tz]._tz
        else:
            tz = tzparse(tz)

        if runstrats:
            # loop separated for clarity
            defaultsizer = self.sizers.get(None, (None, None, None))
            for idx, strat in enumerate(runstrats):
                # add drawdown
                # add holdingperiod
                strat._addobserver(False, LiveDrawDown)
                strat._addobserver(False, HoldingPeriod)

                if self.p.stdstats:
                    strat._addobserver(False, bt.observers.Broker)
                    if self.p.oldbuysell:
                        strat._addobserver(True, bt.observers.BuySell)
                    else:
                        strat._addobserver(True, bt.observers.BuySell, barplot=True)

                    # if self.p.oldtrades or len(self.datas) == 1:
                    #     strat._addobserver(False, bt.observers.Trades)
                    # else:
                    #     strat._addobserver(False, bt.observers.DataTrades)
                    strat._addobserver(False, bt.observers.Trades)

                for multi, obscls, obsargs, obskwargs in self.observers:
                    strat._addobserver(multi, obscls, *obsargs, **obskwargs)

                for indcls, indargs, indkwargs in self.indicators:
                    strat._addindicator(indcls, *indargs, **indkwargs)

                for ancls, anargs, ankwargs in self.analyzers:
                    strat._addanalyzer(ancls, *anargs, **ankwargs)

                # add indicator storage
                for stcls, stargs, stkwargs in self.storages:
                    strat._addstorage(stcls, *stargs, **stkwargs)
                # # 是否通知存储
                # if self.p.store_notify:
                #     strat._addstorage(bt2.storages.NotifyStorage)

                sizer, sargs, skwargs = self.sizers.get(idx, defaultsizer)
                if sizer is not None:
                    strat._addsizer(sizer, *sargs, **skwargs)

                strat._settz(tz)
                strat._start()

                for writer in self.runwriters:
                    if writer.p.csv:
                        writer.addheaders(strat.getwriterheaders())

            if not predata:
                for strat in runstrats:
                    strat.qbuffer(self._exactbars, replaying=self._doreplay)

            for writer in self.runwriters:
                writer.start()

            # Prepare timers
            self._timers = []
            self._timerscheat = []
            for timer in self._pretimers:
                # preprocess tzdata if needed
                timer.start(self.datas[0])

                if timer.params.cheat:
                    self._timerscheat.append(timer)
                else:
                    self._timers.append(timer)

            if self._dopreload and self._dorunonce:
                if self.p.oldsync:
                    self._runonce_old(runstrats)
                else:
                    self._runonce(runstrats)
            else:
                if self.p.oldsync:
                    self._runnext_old(runstrats)
                else:
                    try:
                        self._runnext(runstrats)
                    except Exception as e:
                        # 策略发生无法预期的异常时, 启动策略停止逻辑
                        for strat in runstrats:
                            strat.logger.exception("策略运行发生异常", exc_info=True)
                            strat._stop()
                        raise e

            for strat in runstrats:
                strat.logger.info("策略运行停止")
                strat._stop()

        self._broker.stop()

        if not predata:
            for data in self.datas:
                data.stop()

        for feed in self.feeds:
            feed.stop()

        for store in self.stores:
            store.stop()

        self.stop_writers(runstrats)

        if self._dooptimize and self.p.optreturn:
            # Results can be optimized
            results = list()
            for strat in runstrats:
                for a in strat.analyzers:
                    a.strategy = None
                    a._parent = None
                    for attrname in dir(a):
                        if attrname.startswith("data"):
                            setattr(a, attrname, None)

                oreturn = OptReturn(
                    strat.params, analyzers=strat.analyzers, strategycls=type(strat)
                )
                results.append(oreturn)

            return results

        return runstrats

    def _runnext(self, runstrats):
        """
        自定义运行逻辑, 在原有逻辑上做修改
        主要修改数据源的数据同步问题
        """
        datas = sorted(self.datas, key=lambda x: (x._timeframe, x._compression))
        datas1 = datas[1:]
        data0 = datas[0]
        d0ret = True

        while d0ret or d0ret is None:  # 当返回数据为True或None时, 继续读取数据
            # newqcheck 跳过
            lastret = False
            # 检查通知
            self._storenotify()
            if self._event_stop:  # stop if requested
                return
            self._datanotify()
            if self._event_stop:  # stop if requested
                return
            drets = []
            for d in datas:
                drets.append(d.next(ticks=False))  # 读取数据

            # d0ret为当前总结果
            # 当返回结果中有True或None, 则总结果为True
            # 否则总结果为False
            d0ret = any((dret for dret in drets))
            if not d0ret and any((dret is None for dret in drets)):
                d0ret = None
            if d0ret:  # 存在有效数据
                ## 处理不同情况下的数据源对齐问题
                # 判断数据源是否为实时数据源
                dlives = [data._laststatus == data.LIVE for data in datas]
                # 判断存活的数据源
                dalives = [data._laststatus != data.DISCONNECTED for data in datas]

                if all(dlives) and all(dalives):  # 全为实时数据源
                    ## 实时数据规则:
                    ## 当来一个有效数据时, 需要先保证数据发送时间一致, 防止数据错位
                    ## 数据发送时间一致后, 取所有数据的最新值
                    logger.debug("---live----")
                    # 实时数据的时间基准需要考虑数据发送时间, 在实时数据中起到对齐作用
                    curr_dts = []  # 数据的发送时间
                    for d in datas:
                        curr_dts.append(d.get_curr_dt())
                    # 数据发送时间为tick时间, 如果数据发送时间不一致则需要获取到最新的时间
                    curr_dt0 = max(curr_dts)  # 获取最新数据发送时间
                    while (
                        not all(curr_dt == curr_dt0 for curr_dt in curr_dts) and d0ret
                    ):
                        for i, curr_dt in enumerate(curr_dts):
                            if curr_dt < curr_dt0:
                                # 获取下一个数据
                                ## 获取新的数据后, 各种变量需要更新, d0ret可能转为False
                                ret = datas[i].next(ticks=False)
                                curr_dts[i] = datas[i].get_curr_dt()  # 更新发送时间
                            elif curr_dt > curr_dt0:
                                logger.warning(f"数据源{i}数据缺失: {curr_dt0}")
                                curr_dt0 = curr_dt  # 更新最新值
                            else:
                                pass
                    # 主数据源序号
                    dmaster_i = curr_dts.index(curr_dt0)

                elif all(dlive is False for dlive in dlives) and all(
                    dalives
                ):  # 全为历史数据源
                    ## 历史数据规则
                    ## 历史数据不会返回None, 除非数据已经读取完毕
                    ## 根据收盘时间找到最早收盘数据, 大于最早时间的数据需要回退一次
                    ### TODO: 如果数据源为replay模式, 则收盘时间取开盘时间
                    logger.debug("---history----")
                    close_dts = []  # 收盘时间数组
                    for d in datas:
                        # try:
                        #     close_dts.append(d.closedt[0])
                        # except IndexError:
                        #     close_dts.append(None)
                        close_dts.append(d.get_closedt())

                    if any(dt is None for dt in close_dts):
                        # 存在无效数据
                        d0ret = None
                        # 主数据源默认为0
                        dmaster_i = 0
                    else:
                        close_dt0 = min(close_dts)
                        for i, cdt in enumerate(close_dts):
                            if cdt > close_dt0:
                                datas[i].rewind(force=True)

                        # 主数据源序号
                        dmaster_i = close_dts.index(close_dt0)

                elif all(dalives):  # 有部分历史数据源
                    logger.debug("---part history----")
                    for d in datas:
                        if d._laststatus == d.LIVE and d.replaying:
                            d.rewind(force=True)

                else:  # 一部分数据源已经关闭
                    logger.debug("--- close ----")
                    # 存活的数据源调整长度
                    # 存在关闭的数据源, 说明获取的是历史数据. 此时存活的数据只能是频率较高的历史数据, 直接backwards
                    for data in datas:
                        if data._laststatus != data.DISCONNECTED:
                            data.backwards()
                    d0ret = False

                # 获取调整后的最新datetime
                dts = []
                for d in datas:
                    try:
                        dts.append(d.datetime[0])
                    except IndexError:
                        dts.append(None)

                # 有无效数据时跳出后续执行
                if any(dt is None for dt in dts) and d0ret != False:
                    d0ret = None
                else:
                    dmaster = datas[dmaster_i]
                    dt0 = dmaster.datetime[0]
                    self._dtmaster = dmaster.num2date(dt0)
                    self._udtmaster = num2date(dt0)

            if d0ret is None:
                # meant for things like live feeds which may not produce a bar
                # at the moment but need the loop to run for notifications and
                # getting resample and others to produce timely bars
                for data in datas:
                    data._check()

            if d0ret is False:
                lastret = data0._last()
                for data in datas1:
                    lastret += data._last(datamaster=data0)

                if not lastret:
                    # Only go extra round if something was changed by "lasts"
                    break

            # Datas may have generated a new notification after next
            self._datanotify()
            if self._event_stop:  # stop if requested
                return

            # 如果数据源长度有小于1的,说明存在数据源没有有效数据, 此时跳过后续执行
            if any(len(data) < 1 for data in datas) and d0ret:
                logger.debug("数据源缺少有效数据, 跳过此次计算")
                d0ret = None

            if d0ret or lastret:  # if any bar, check timers before broker
                self._check_timers(runstrats, dt0, cheat=True)
                if self.p.cheat_on_open:
                    for strat in runstrats:
                        strat._next_open()
                        if self._event_stop:  # stop if requested
                            return

            self._brokernotify()
            if self._event_stop:  # stop if requested
                return

            if d0ret or lastret:  # bars produced by data or filters
                self._check_timers(runstrats, dt0, cheat=False)
                for strat in runstrats:
                    strat._next()
                    if self._event_stop:  # stop if requested
                        return

                    self._next_writers(runstrats)

        # Last notification chance before stopping
        self._datanotify()
        if self._event_stop:  # stop if requested
            return
        self._storenotify()
        if self._event_stop:  # stop if requested
            return


class PseudoCerebro(bt.Cerebro):
    """
    虚拟引擎, 用于生成策略实例
    """

    def addstrategy(self, strategy, *args, **kwargs):
        # 只允许添加一个策略
        self.stras = list()
        return super().addstrategy(strategy, *args, **kwargs)

    def get_stra_instance(self):
        self._init_stcount()
        iterstrats = itertools.product(*self.strats)
        strats = []
        for iterstrat in iterstrats:
            for stratcls, sargs, skwargs in iterstrat:
                sargs = self.datas + list(sargs)
                try:
                    strat = stratcls(*sargs, **skwargs)
                    strats.append(strat)
                except bt.errors.StrategySkipError:
                    continue  # do not add strategy to the mix
        return strats
