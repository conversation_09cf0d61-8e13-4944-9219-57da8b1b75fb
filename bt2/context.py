from .mysql import TaskService
from logging import getLogger


logger = getLogger(__name__)


class DBTaskStatusContext:
    """
    任务运行上下文管理器
    通过`task_id`参数设置当前任务的任务id

    Args:
        task_id (int): 任务id
        conn_db (bool): 是否连接数据库
    """

    _task_id = None
    _conn_db = True

    def __init__(self, task_id=None, conn_db=True):
        DBTaskStatusContext._task_id = task_id
        DBTaskStatusContext._conn_db = conn_db

    def __enter__(self):
        # 任务标记开始
        if DBTaskStatusContext._conn_db:
            TaskService.run_task(task_id=DBTaskStatusContext._task_id)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if DBTaskStatusContext._conn_db:
            if exc_type is None:
                # 任务标记结束
                TaskService.finish_task(task_id=self.get_current_task_id())
            else:
                task = TaskService.query_by_id(task_id=self.get_current_task_id())
                if TaskService.task_is_alive(task):
                    logger.warning("task interrupt, old state: %s", task.run_state)
                    TaskService.interrupt_task(task_id=self.get_current_task_id())

    @classmethod
    def get_current_task_id(cls):
        # 获取当前任务 ID
        return cls._task_id

    @classmethod
    def get_current_task_name(cls):
        if cls._conn_db:
            # 获取当前任务的name
            task = TaskService.query_by_id(cls._task_id)
            if task:
                return task.task_name
        return cls._task_id
