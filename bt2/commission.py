from backtrader import MetaParams


class CommInfo2(metaclass=MetaParams):
    """
    保证金, 手续费计算规则
    暂不计算利息
    """

    params = (
        ("commission", 0.0003),
        ("leverage", 20),
    )

    @property
    def leverage(self):
        return self.p.leverage

    def __init__(self):
        super().__init__()

    def get_leverage(self):
        """获取杠杆"""
        return self.p.leverage

    def getsize(self, price, parValue):
        """通过订单价值计算仓位"""
        return parValue / self.p.leverage / price

    def getmargin(self, size, price):
        """
        计算保证金
        """
        return abs(size) * price / self.p.leverage

    def getcommission(self, size, price):
        """
        计算手续费
        """
        return abs(size) * self.p.commission * price

    def profitandloss(self, size, price, newprice):
        """
        计算盈亏
        一般平仓时的size方向相反, 注意size的方向
        例如平多仓时, size为负, 此时需要对size * -1
        """
        return size * (newprice - price)

    def getvaluesize(self, size, price):
        """
        计算仓位价值
        """
        return abs(size) * price / self.p.leverage
