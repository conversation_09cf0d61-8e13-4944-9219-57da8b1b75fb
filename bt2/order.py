# 实时order

from backtrader import (
    OrderData,
    AutoOrderedDict,
    date2num,
    num2date,
)
from backtrader.metabase import ParamsBase
from .utils import num2ts, num2date
from logging import getLogger
import datetime
import uuid
from copy import copy
import threading

logger = getLogger(__name__)


class OrderRefIterator:
    # 订单id生成器
    def __next__(self):
        return str(uuid.uuid4()).replace("-", "")


class EventOrderBase(ParamsBase):
    """
    订单基类, 包含订单事件锁
    参照backtrader.OrderBase 进行修改
    删除无用参数
    增加持仓方向
    平台支持双向持仓时, 订单需要明确标注开平仓方向
    sell的size强制为负数
    查找属性时会先查找__dict__, 然后查找params属性
    Params:
        side (str): 开平仓方向, LONG, SHORT
    """

    params = (
        ("owner", None),
        ("data", None),
        ("exectype", None),
        ("size", None),
        ("price", None),
        ("side", None),
        ("valid", None),
        ("tradeid", 0),
        ("simulated", False),
    )

    LONG, SHORT = 1, -1
    Sides = ["LONG", "SHORT"]

    (Market, Close, Limit, Stop, StopLimit, StopTrail, StopTrailLimit, Historical) = (
        range(8)
    )
    ExecTypes = [
        "Market",
        "Close",
        "Limit",
        "Stop",
        "StopLimit",
        "StopTrail",
        "StopTrailLimit",
        "Historical",
    ]

    OrdTypes = ["Buy", "Sell"]
    Buy, Sell = 1, -1

    (
        Created,
        Submitted,
        Accepted,
        Partial,
        Completed,
        Canceled,
        Expired,
        Margin,
        Rejected,
    ) = range(9)

    Cancelled = Canceled  # alias

    Status = [
        "Created",
        "Submitted",
        "Accepted",
        "Partial",
        "Completed",
        "Canceled",
        "Expired",
        "Margin",
        "Rejected",
    ]

    refbasis = OrderRefIterator()  # for a unique identifier per order

    csv_header = [
        "ref",
        "symbol",
        "created_dt",
        "executed_dt",
        "ord_type",
        "status",
        "side",
        "size",
        "created_price",
        "executed_price",
        "exec_type",
        "margin",
        "trigger",
        "pnl",
    ]

    def __getattr__(self, name):
        # Return attr from params if not found in order
        return getattr(self.params, name)

    def __setattribute__(self, name, value):
        if hasattr(self.params, name):
            setattr(self.params, name, value)
        else:
            super().__setattribute__(name, value)

    def to_data(self):
        return [
            self.ref,
            self.data.get_symbol(),
            self.getcreateddt(),
            self.getexecuteddt(),
            self.ordtypename(),
            self.getstatusname(),
            self.getsidename(),
            self.executed.size,
            self.created.price,
            self.executed.price,
            self.getordername(),
            self.executed.margin,
            self.info.trigger,
            self.executed.pnl,
        ]

    def getcreateddt(self):
        """获取创建时间"""
        return (
            num2date(self.created.dt)
            if self.created.dt != None and self.created.dt >= 0
            else None
        )

    def getexecuteddt(self):
        """获取执行时间"""
        return (
            num2date(self.executed.dt)
            if self.executed.dt != None and self.created.dt >= 0
            else None
        )

    def __str__(self):
        tojoin = list()
        tojoin.append("Ref: {}".format(self.ref))
        tojoin.append("Symbol: {}".format(self.data.get_symbol()))
        tojoin.append("Status: {}".format(self.getstatusname()))
        tojoin.append("ExecType: {}".format(self.getordername()))
        tojoin.append("OrdType: {}".format(self.ordtypename()))
        tojoin.append("Side: {}".format(self.getsidename()))
        tojoin.append("Price: {}".format(self.price))
        tojoin.append("Size: {}".format(self.size))
        tojoin.append("Info: {}".format(self.info))
        tojoin.append("CommInfo: {}".format(self.comminfo))
        tojoin.append("Margin: {}".format(self.executed.margin))
        tojoin.append("Alive: {}".format(self.alive()))
        tojoin.append("CreateTime: {}".format(num2date(self.created.dt)))
        return ", ".join(tojoin)

    def __init__(self):
        self.ref = next(self.refbasis)
        self.broker = None
        self.info = AutoOrderedDict({"trigger": None})
        self.comminfo = None

        self._active = True
        self.status = self.Created

        if self.exectype is None:
            self.exectype = self.Market
        elif isinstance(self.exectype, str):
            self.exectype = self.ExecTypes.index(self.exectype)
        else:
            pass

        if self.side is None:
            self.side = self.LONG
        elif isinstance(self.side, str):
            if self.p.side.upper() == "LONG":
                self.side = self.LONG
            if self.p.side.upper() == "SHORT":
                self.side = self.SHORT
        else:
            pass

        if self.isbuy() and self.size:
            self.size = abs(self.size)  # 买单为正数
        elif not self.isbuy() and self.size:
            self.size = abs(self.size) * (-1)  # 卖单为负数
        else:
            pass

        # Set a reference price if price is not set using
        # the close price
        if len(self.data) == 0:  # 初始化时的订单
            pclose = 0
            dcreated = 0
        else:
            pclose = self.data.close[0]
            dcreated = self.data.get_curr_dt()
        ### 如果没给价格, 可能是盘口价或者市价, 默认为None
        price = self.p.price
        # price = pclose if not self.price else self.price

        self.created = OrderData(
            dt=dcreated, size=self.size, price=price, pclose=pclose
        )

        self.executed = OrderData(remsize=self.size)
        self.position = 0

        if isinstance(self.valid, datetime.date):  # TODO 细化valid逻辑
            self.valid = date2num(self.valid)

        # 非活动状态事件锁, 只有当订单停止活动后才会打开锁
        self.inactive_event = threading.Event()
        self.inactive_event.clear()

    def clone(self):
        # status, triggered and executed are the only moving parts in order
        # status and triggered are covered by copy
        # executed has to be replaced with an intelligent clone of itself
        obj = copy(self)
        obj.executed = self.executed.clone()
        return obj  # status could change in next to completed

    def getstatusname(self, status=None):
        """Returns the name for a given status or the one of the order"""
        return self.Status[self.status if status is None else status]

    def getordername(self, exectype=None):
        """Returns the name for a given exectype or the one of the order"""
        return self.ExecTypes[self.exectype if exectype is None else exectype]

    def getsidename(self, side=None):
        """Returns side name"""
        if self.side == -1:
            return "SHORT"
        else:
            return "LONG"

    @classmethod
    def ExecType(cls, exectype):
        return getattr(cls, exectype)

    def ordtypename(self, ordtype=None):
        """Returns the name for a given ordtype or the one of the order"""
        if self.ordtype == -1:
            return "SELL"
        else:
            return "BUY"

    def active(self):
        return self._active

    def activate(self):
        self._active = True

    def alive(self):
        """Returns True if the order is in a status in which it can still be
        executed
        """
        return self.status in [
            self.Created,
            self.Submitted,
            self.Partial,
            self.Accepted,
        ]

    def addcomminfo(self, comminfo):
        """Stores a CommInfo scheme associated with the asset"""
        self.comminfo = comminfo

    def addinfo(self, **kwargs):
        """Add the keys, values of kwargs to the internal info dictionary to
        hold custom information in the order
        """
        for key, val in kwargs.items():
            self.info[key] = val

    def __eq__(self, other):
        return other is not None and self.ref == other.ref

    def __ne__(self, other):
        return self.ref != other.ref

    def isbuy(self):
        """Returns True if the order is a Buy order"""
        return self.ordtype == self.Buy

    def issell(self):
        """Returns True if the order is a Sell order"""
        return self.ordtype == self.Sell

    is_buy = isbuy
    is_sell = issell

    def setposition(self, position):
        """Receives the current position for the asset and stotres it"""
        self.position = position

    def submit(self, broker=None):
        """Marks an order as submitted and stores the broker to which it was
        submitted"""
        self.status = self.Submitted
        self.broker = broker
        self.plen = len(self.data)

    def accept(self, broker=None):
        """Marks an order as accepted"""
        self.status = self.Accepted
        self.broker = broker

    def brokerstatus(self):
        """Tries to retrieve the status from the broker in which the order is.

        Defaults to last known status if no broker is associated"""
        if self.broker:
            return self.broker.orderstatus(self)

        return self.status

    def reject(self, broker=None):
        """Marks an order as rejected"""
        if self.status == self.Rejected:
            return False

        self.status = self.Rejected
        self.broker = broker
        self.executed.dt = self.data.get_curr_dt()
        return True

    def cancel(self, dt=None):
        """Marks an order as cancelled
        Args:
            dt (float): 结束时间, 未设置则取数据源当前时间
        """
        if self.status == self.Canceled:
            return True
        self.status = self.Canceled
        self.executed.dt = self.data.get_curr_dt() if not dt else dt
        self.inactive_event.set()
        return True

    def margin(self):
        """Marks an order as having met a margin call"""
        self.status = self.Margin
        self.executed.dt = self.data.get_curr_dt()

    def completed(self):
        """Marks an order as completely filled"""
        self.status = self.Completed
        self.inactive_event.set()
        return True

    def partial(self):
        """Marks an order as partially filled"""
        self.status = self.Partial

    def execute(
        self,
        dt,
        size,
        price,
        closed,
        closedvalue,
        closedcomm,
        opened,
        openedvalue,
        openedcomm,
        margin,
        pnl,
        psize,
        pprice,
    ):
        """Receives data execution input and stores it"""
        if not size:
            return

        self.executed.add(
            dt,
            size,
            price,
            closed,
            closedvalue,
            closedcomm,
            opened,
            openedvalue,
            openedcomm,
            pnl,
            psize,
            pprice,
        )

        self.executed.margin = margin

        if self.executed.remsize:
            self.status = self.Partial
        else:
            self.status = self.Completed

    def reset_executed(self):
        """重置子订单信息"""
        self.executed = OrderData(remsize=self.created.size)

    def expire(self):
        """Marks an order as expired. Returns True if it worked"""
        if self.exectype == LiveOrder.Market:
            return False  # will be executed yes or yes
        self.status = self.Expired
        self.inactive_event.set()
        return True

    def islong(self):
        return self.side == self.LONG

    def isshort(self):
        return self.side == self.SHORT

    is_long = islong
    is_short = isshort

    def is_open_order(self):
        """开仓订单"""
        return (self.islong() and self.isbuy()) or (self.isshort() and self.issell())

    def is_close_order(self):
        """平仓订单"""
        return not self.is_open_order()

    def is_close_long_order(self):
        """平多仓订单"""
        return self.is_close_order() and self.is_long()

    def is_close_short_order(self):
        """平空仓单"""
        return self.is_close_order() and self.is_short()

    def is_timeout(self):
        # 是否超时
        return self.alive() and self.valid and self.valid < self.data.get_curr_dt()

    def is_completed(self):
        return self.status == self.Completed

    def is_partial(self):
        return self.status == self.Partial

    def is_accepted(self):
        return self.status == self.Accepted

    def is_executed(self):
        # 是否执行
        return (
            self.is_completed()
            or self.is_partial()
            or (self.is_expired and self.executed.size != 0)
        )

    def is_expired(self):
        # 是否过期
        return self.status == self.Expired

    def is_pending(self):
        # 是否处于委托状态
        return self.status in [self.Accepted, self.Partial]

    def is_rejected(self):
        # 是否被拒绝
        return self.status == self.Rejected

    def is_canceled(self):
        # 是否被取消
        return self.status == self.Canceled

    def get_margin(self):
        return self.executed.margin


class EventOrder(EventOrderBase):
    """
    订单类型
    """

    pass


class EventBuyOrder(EventOrder):
    ordtype = EventOrder.Buy


class EventSellOrder(EventOrder):
    ordtype = EventOrder.Sell


class LiveOrder(EventOrderBase):
    """
    实时broker调用的Order类型
    支持双向开仓
    增加实时参数, 订单数据时间
    Params:
        ignore_dt (bool): 是否忽略下单时间和数据源时间差
        dtlaglimit (int): 数据源时间戳和当前时间戳的最大差值, 默认1000ms, 仅当ignore_dt为False时有效
    """

    params = (
        ("ignore_dt", False),
        ("dtlaglimit", 5000),
    )

    def __init__(self):
        super().__init__()
        if len(self.data) == 0:  # 初始化时的订单
            delta_ts = 0
        else:
            now_ts = datetime.datetime.now()
            delta_ts = now_ts.timestamp() * 1000 - num2ts(self.data.get_curr_dt())
        if not self.p.ignore_dt and delta_ts > self.dtlaglimit:
            logger.warning(
                "订单延迟过久. 时间差值: %s ms, 当前时间: %s, 数据源时间: %s",
                delta_ts,
                now_ts,
                num2date(self.data.get_curr_dt()),
            )
            self.status = self.Expired

    def execute(
        self,
        dt,
        size,
        price,
        closed,
        closedvalue,
        closedcomm,
        opened,
        openedvalue,
        openedcomm,
        margin,
        pnl,
        psize,
        pprice,
    ):
        """Receives data execution input and stores it"""
        if not size:
            return

        self.executed.add(
            dt,
            size,
            price,
            closed,
            closedvalue,
            closedcomm,
            opened,
            openedvalue,
            openedcomm,
            pnl,
            psize,
            pprice,
        )

        self.executed.margin = margin

        ### 交给controller控制状态
        # if self.executed.remsize:
        #     self.status = self.Partial
        # else:
        #     self.status = self.Completed

    def trailadjust(self, price):
        if self.trailamount:
            pamount = self.trailamount
        elif self.trailpercent:
            pamount = price * self.trailpercent
        else:
            pamount = 0.0

        # Stop sell is below (-), stop buy is above, move only if needed
        if self.isbuy():
            price += pamount
            if price < self.created.price:
                self.created.price = price
                if self.exectype == LiveOrder.StopTrailLimit:
                    self.created.pricelimit = price - self._limitoffset
        else:
            price -= pamount
            if price > self.created.price:
                self.created.price = price
                if self.exectype == LiveOrder.StopTrailLimit:
                    # limitoffset is negative when pricelimit was greater
                    # the - allows increasing the price limit if stop increases
                    self.created.pricelimit = price - self._limitoffset


class LiveBuyOrder(LiveOrder):
    ordtype = LiveOrder.Buy


class LiveSellOrder(LiveOrder):
    ordtype = LiveOrder.Sell
