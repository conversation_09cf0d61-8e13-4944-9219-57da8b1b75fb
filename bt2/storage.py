## 存储器
import backtrader as bt


class Storage(metaclass=bt.MetaParams):
    """存储器
        存储器初始化时接收一个策略, 并在策略执行next之后调用自身的next
    Method:
        __init__(bt.Strategy, *args, **kwargs): 利用策略和其他参数初始化指标存储器
        next: 执行逻辑, 执行顺序在strategy.next之后
    """

    def __init__(self, strategy: bt.Strategy, *args, **kwargs):
        # 存储器初始化, 第一个参数为策略
        raise NotImplementedError("Subclasses must implement __init__ method")

    def next(self):
        # 策略调用next之后的指标存储逻辑
        raise NotImplementedError("Subclasses must implement next method")

    def _stop(self):
        pass

    def _next(self):
        return self.next()

    def _prenext(self):
        return self._next()

    def _nextstart(self):
        return self._next()
