## 提供订单的相关接口

from ..ws import BrokerWSManager, BinanceBrokerWSManager
from ..redismanager import (
    DynParamRedisManager,
    RiskRedisManager,
    RunControlRedisManager,
)
from ..utils import ts2num
from ..api import BrokerAPIManager, BinanceBrokerAPIManager
from logging import getLogger
from ..context import DBTaskStatusContext
from .orderdata import BinanceOrderData
from ..order import LiveBuyOrder, LiveSellOrder
import weakref
import threading


logger = getLogger(__name__)


class RiskEvent:
    """
    风控对象
    """

    def __init__(self, account_id=None):
        self._status = 2
        self.account_id = account_id

    def stop(self):
        if self._status != 0:
            logger.warning("触发风控")
            self._status = 0

    def warn(self):
        if self._status != 1:
            logger.warning("风控警告")
            self._status = 1

    def normal(self):
        if self._status != 2:
            logger.warning("风控解除")
            self._status = 2

    def is_normal(self):
        return self._status == 2

    def is_stop(self):
        return self._status == 0


class LiveBrokerControllerBase:
    """
    实时broker控制器, 负责提供对订单, 仓位, 资金的查询和提交,
    同时broker的收益等计算也通过controller, 主要是为了保证
    cotroller采用额外线程进行数据更新时数据的一致性.

    一些初始化以及数据计算的方法是通过broker调用执行,
    还有的方法是需要通过注册websocket方法后通过事件触发执行.

    当订单更新后, 需要等待账户和仓位更新, 才明确broker数据更新完成

    ## 2024-4-10
    可通过订阅redis更新动态参数
    """

    WSManager = BrokerWSManager
    APIManager = BrokerAPIManager
    RiskManager = RiskRedisManager
    DynParamManager = DynParamRedisManager
    RunControlManager = RunControlRedisManager

    def __init__(
        self, broker, account_info={}, dyn_key="default", datas=[], risk_control=True
    ):
        # 线程锁
        self.lock = threading.Lock()
        # 设置账户
        self.WSManager.set_account(**account_info)
        # 设置账户监控品种
        self.WSManager.set_symbols(
            symbols=[data.get_symbol().upper() for data in datas]
        )
        self.APIManager.set_account(**account_info)
        # 设置动态参数
        self.DynParamManager.set_key(dyn_key=dyn_key)
        # 设置控制参数
        self.RunControlManager.set_key(dyn_key=dyn_key)
        # 存放的broker对象
        self._broker = weakref.ref(broker)
        # 存放的websocket
        self._ws = weakref.ref(self.WSManager.get_ws(self))  # 开启订阅
        # 风控对象
        self._risk_event = RiskEvent(account_info["account_id"])
        # 风控flag
        self._risk_control = risk_control
        # 心跳停止flag
        self._stop_heartbeat_event = threading.Event()
        # 心跳发送线程
        self._heartbeat_thread = None
        # 检查连接状态
        self.check_ws_connection()

    def init_broker(self):
        # 初始化broker
        # 由于数据源尚未获取数据, 只能获取现金余额和仓位, 无法计算浮盈
        # 因此value和pnl属性暂时无意义
        with self.lock:
            # 初始化现金余额
            logger.info("初始化余额")
            self.init_cashbalance()
            # 初始化仓位
            logger.info("初始化仓位")
            self.init_positions()
            # 初始化杠杆
            logger.info("初始化杠杆")
            self.init_levers()
            # 初始化挂单
            logger.info("初始化挂单")
            self.init_pending()
            # 初始化其他参数
            self.update_cash_pnl_value()

        # 开始心跳发送
        self.start_heartbeat()
        # 开始风控监控
        if self._risk_control:
            self.init_risk_control()

    def stop(self):
        # 停止心跳发送
        self.stop_heartbeat()
        # 停止风控监控
        self.stop_riskcontrol()

    def init_cashbalance(self):
        cashbalance = self.APIManager.get_cashbalance()
        self._broker().set_cashbalance(cashbalance)

    def init_positions(self):
        positions = self.APIManager.get_positions()
        self._broker().set_positions(positions)

    def init_levers(self):
        # 只获取数据源相关的杠杆
        symbols = [data.get_symbol() for data in self._broker().datas]
        levers = self.APIManager.get_levers(symbols)
        self._broker().set_levers(levers)

    def init_pending(self):
        # 初始化挂单
        for data in self._broker().get_unique_datas():
            orddatas = self.APIManager.query_pending(data.get_symbol())
            for orddata in orddatas:
                ordinfo = BinanceOrderData.from_query(orddata)
                self.add_pending_from_query(ordinfo, data)

    def init_contract_to_coin(self):
        c2c = self.APIManager.get_contract_to_coin(self._broker().datas)
        self._broker().set_contract_to_coin(c2c)

    def check_ws_connection(self):
        # 检查ws连接情况
        assert self._ws().sock.connected, f"{self} websocket连接失败"

    def update_dyn_params(self):
        # 更新动态参数
        params = self.DynParamManager.get_value()
        self._broker()._dyn_params.update(params)

    def update_control_params(self):
        # 更新控制参数
        control = self.RunControlManager.get_control()
        if control:
            self._broker().set_state(control)

    def update_cash_pnl_value(self):
        """更新现金, 浮盈, 价值"""
        cashbalance = self._broker().get_cashbalance()
        positions = self._broker().get_positions()
        levers = self._broker().get_levers()
        c2c = self._broker().get_contract_to_coin()
        pnl = 0
        cash = cashbalance
        for data, pos in positions.items():
            if len(data) == 0:  # 还未有数据, 不作计算
                continue
            abssize = abs(pos.size)
            pnl += (data.tick_close - pos.price) * pos.size
            lever = levers[data]
            if lever:
                cash -= pos.price * abssize * c2c[data] / levers[data]
            else:
                pass
        cash += pnl
        value = cashbalance + pnl
        self._broker().set_cash_pnl_value(cash, pnl, value)

    def submit_order(self, order):
        # 提交订单
        if self.APIManager.submit_orders([order]):
            return order
        else:
            return None

    ## 事件触发的方法 update_account, update_positions, update_orders
    def update_account(self, data):
        """更新账户"""
        raise NotImplementedError("Subclasses must implement update_account method")

    def update_positions(self, data):
        """更新仓位"""
        raise NotImplementedError("Subclasses must implement update_positions method")

    def update_orders(self, data):
        """更新订单交易状态"""
        raise NotImplementedError("Subclasses must implement update_orders method")

    def update_lever(self, data):
        """调整杠杆"""
        raise NotImplementedError("Subclasses must implement update_lever method")

    ######

    def check_connection(self):
        # 检查连接是否存活, 并尝试重连
        raise NotImplementedError("Subclasses must implement check_connection method")

    def cancel_order(self, order):
        self.APIManager.cancel_order(order)

    def start_heartbeat(self):
        """创建新线程开启心跳发送"""
        self._stop_heartbeat_event.clear()
        self._heartbeat_thread = threading.Thread(target=self._send_run_state)
        self._heartbeat_thread.daemon = True  # 设置为守护线程
        self._heartbeat_thread.start()

    def stop_heartbeat(self):
        """终止线程心跳发送"""
        self._stop_heartbeat_event.set()
        self._heartbeat_thread.join()

    def _send_run_state(self):
        """发送运行状态作为心跳"""
        logger.info("start heartbeat")
        task_id = DBTaskStatusContext.get_current_task_id()
        while True:
            wait = self._stop_heartbeat_event.wait(timeout=10)  # 等待超时
            state = self._broker()._runstate
            heartbeat = state.get_heartbeat()
            # 添加仓位状态
            is_open = self._broker().is_open_position()
            sym_size = self._broker().get_size()
            heartbeat["open"] = is_open
            heartbeat["sym_size"] = sym_size
            self.DynParamManager.update_value(key=task_id, value=heartbeat)
            if not wait:  # 未停止
                continue
            else:  # 停止
                break
        logger.info("stop heartbeat")

    def init_risk_control(self):
        """初始化风控"""
        logger.info("risk control init")
        self.RiskManager.subscribe(
            f"risk_status_account_{self._risk_event.account_id}", self._risk_event
        )
        self.RiskManager.start()

    def stop_riskcontrol(self):
        if self._risk_control:
            self.RiskManager.stop()

    def risked(self):
        """风控触发"""
        return self._risk_event.is_stop()

    def query_for_update_order(self, order):
        ### 查询并更新order的状态
        resp = self.APIManager.query_order(order)
        self.update_query_order(order, resp)

    def update_query_order(self, order, query):
        """通过查询更新订单"""
        raise NotImplementedError("Subclasses must implement update_query_order method")

    def add_pending_from_query(self, ordinfo, data):
        """
        根据查询生成新的订单, 如果订单不存在, 则加入pending队列
        """
        raise NotImplementedError(
            "Subclasses must implement add_pending_from_query method"
        )

    def append_hist_orders(self, limit=5):
        """
        追加历史订单
        """


class BinanceLiveBrokerController(LiveBrokerControllerBase):
    """
    币安broker控制器
    """

    WSManager = BinanceBrokerWSManager
    APIManager = BinanceBrokerAPIManager

    def update_broker(self):
        # 手动更新broker, 通常是websocket发生断开的时候执行
        self.update_pending_orders()
        self.init_cashbalance()
        self.init_positions()
        self.init_levers()
        self.update_cash_pnl_value()

    def init_pending(self):
        # 初始化挂单
        for data in self._broker().get_unique_datas():
            orddatas = self.APIManager.query_pending(data.get_symbol())
            for orddata in orddatas:
                ordinfo = BinanceOrderData.from_query(orddata)
                self.add_pending_from_query(ordinfo, data)

    def update_pending_orders(self):
        # 更新订单对象
        for order in self._broker().pending:
            resp = self.APIManager.query_order(order)
            self.update_query_order(order, resp)
        # 更新未保存在pending中的订单并放入pending中
        for data in self._broker().get_unique_datas():
            orddatas = self.APIManager.query_pending(data.get_symbol())
            for orddata in orddatas:
                ordinfo = BinanceOrderData.from_query(orddata)
                self.add_pending_from_query(ordinfo, data)

    def update_query_order(self, order, query):
        """通过查询更新订单"""
        if not query:  # 订单信息不存在, 视作未交易失效
            logger.info(f"订单{order.ref}已过期或失效")
            order.expire()
            return
        ordinfo = BinanceOrderData.from_query(query)
        self.update_order_by_ordinfo(order, ordinfo)

    ## 事件触发的方法
    def update_account(self, data):
        """更新账户"""
        try:
            logger.info(f"更新 account: {data}")
            for detail in data["a"]["B"]:
                if detail["a"] == "USDT":  # 只处理美元仓位
                    self._broker().set_cashbalance(float(detail["wb"]))
                    break
            self.update_cash_pnl_value()
        except Exception as e:
            logger.exception(e)

    def update_lever(self, data):
        """调整杠杆"""
        try:
            lever_info = data["ac"]
            lever = {lever_info["s"]: lever_info["l"]}
            self._broker().update_lever(lever)
        except Exception as e:
            logger.exception(e)

    def update_positions(self, data):
        """更新仓位"""
        try:
            logger.info(f"更新 positions: {data}")
            for detail in data["a"]["P"]:
                symbol = detail["s"].upper()  # binance 规则
                broker_datas = self._broker().datas
                # 找到对应的品种
                symbol_data = None
                for d in broker_datas:
                    if d.get_symbol().upper() == symbol:
                        symbol_data = d
                        break  # 只看第一个
                if symbol_data is None:  # 非数据源
                    logger.info(f"{symbol} 不存在对应数据源")
                    continue
                size = float(detail["pa"])
                price = float(detail["ep"]) if detail["ep"] else 0
                self._broker().positions[symbol_data].set(size, price)
                if len(symbol_data.datetime) > 0:
                    self._broker().positions[symbol_data].updt = symbol_data.datetime[0]
            self.update_cash_pnl_value()
        except Exception as e:
            logger.exception(e)

    def update_orders(self, data):
        """更新订单交易状态"""
        try:
            logger.info(f"更新 orders: {data}")
            orderpending = self._broker().pending
            ordinfo = BinanceOrderData.from_fstream(data)
            order_num = ordinfo.ref
            order = None
            for o in orderpending:
                if o.ref == order_num:
                    order = o
            if order == None:
                logger.debug("无对应订单 %s", order_num)
                return
            self.update_order_by_ordinfo(order, ordinfo)
        except Exception as e:
            logger.exception(e)

    def update_order_by_ordinfo(self, order, ordinfo: BinanceOrderData):
        """
        根据订单信息更新订单状态
        """
        if order.ref != ordinfo.ref:
            logger.warning(f"订单信息和订单不一致: {order.ref}: {ordinfo.ref}")
            return
        logger.info(f"更新 order: {ordinfo}")
        order.created.size = ordinfo.created_size
        order.created.price = ordinfo.created_price
        if ordinfo.status == order.Accepted:  # 等待成交, 需要根据返回信息进行调整
            order.accept()
            # 数量初始化
            order.executed.remsize = order.created.size
            # 价格初始化
            order.executed.price = order.created.price
            return
        if ordinfo.status == order.Canceled:  # 撤单
            order.cancel(ts2num(ordinfo.dt))
            return
        ## 更新订单执行情况
        logger.info("更新一次order executed---")
        # 判断手续费方向
        # bnb结算， 手续费用默认配置计算
        comm = order.comminfo.getcommission(
            ordinfo.executed_size, ordinfo.executed_price
        )
        position = self._broker().getposition(order.data)
        if ordinfo.is_open():
            # 开仓
            openedcomm, closedcomm = comm, 0
            openedvalue, closedvalue = ordinfo.executed_value, 0
        else:
            # 平仓
            openedcomm, closedcomm = 0, comm
            openedvalue, closedvalue = 0, ordinfo.executed_value
        ## 如果订单信息为单次子订单, 则直接执行execute
        ## 如果订单信息为合并后的信息, 则需要对订单清空子订单后, 放入一个虚拟子订单
        ## 推送过程是仓位先更新, 订单后更新
        psize = position.size
        pprice = position.price
        if ordinfo.is_bit():
            opened = ordinfo.executed_size * ordinfo.is_open()
            closed = ordinfo.executed_size * (not ordinfo.is_open())
        else:
            order.reset_executed()
            opened = position.size * ordinfo.is_open()
            closed = position.size * (not ordinfo.is_open())
        if ordinfo.executed_size:  # 有执行仓位时更新
            order.execute(
                dt=ts2num(ordinfo.dt),
                size=ordinfo.executed_size,
                price=ordinfo.executed_price,
                margin=0,
                closed=closed,
                closedvalue=closedvalue,
                closedcomm=openedcomm,
                opened=opened,
                openedvalue=openedvalue,
                openedcomm=closedcomm,
                pnl=ordinfo.pnl,
                psize=psize,
                pprice=pprice,
            )
        if ordinfo.status == order.Partial:  # 部分成交
            order.partial()
            return
        if ordinfo.status == order.Completed:
            order.completed()  # 完成订单状态
            return
        if ordinfo.status == order.Expired:  # 订单失效, IOC订单会在部分成交后立即失效
            order.expire()
            return
        logger.warning(f"未识别的状态, 检查逻辑: {ordinfo}")

    def init_cashbalance(self):
        cashbalance, bnb = self.APIManager.get_cashbalance()
        self._broker().set_cashbalance(cashbalance)
        self._broker().set_bnb(bnb)

    def update_cash_pnl_value(self):
        """更新现金, 浮盈, 价值"""
        cashbalance = self._broker().get_cashbalance()
        positions = self._broker().get_positions()
        levers = self._broker().get_levers()
        pnl = 0
        cash = cashbalance
        for data, pos in positions.items():
            if len(data) == 0:  # 还未有数据, 不作计算
                continue
            abssize = abs(pos.size)
            lever = levers[data]
            tick_close = getattr(data, "tick_close", data.close)
            pnl += (tick_close - pos.price) * pos.size
            if lever:
                cash -= pos.price * abssize / lever
            else:
                pass
        cash += pnl
        value = cashbalance + pnl
        self._broker().set_cash_pnl_value(cash, pnl, value)

    def cancel_all_orders(self, datas=None):
        # 撤销所有订单
        if not datas:
            datas = self._broker().datas
        self.APIManager.cancel_all_orders(datas)

    def check_connection(self):
        # 检查websocket连接是否存活, 并尝试重连
        if not self.WSManager.check_connection():
            logger.warning("账户websocket连接异常, 重新获取webosocket")
            self._ws = weakref.ref(self.WSManager.get_ws(self))
            # 更新信息
            self.update_broker()

        # 检查风控redis连接是否存活
        if not self.RiskManager.check_connection():
            logger.warning("风控redis连接异常")
            raise Exception("风控redis连接异常")

    def gen_order_from_ordinfo(self, ordinfo, data):
        """
        根据查询生成新的订单对象
        """
        owner = None
        kwargs = dict(
            owner=owner,
            data=data,
            size=ordinfo.created_size,
            price=ordinfo.created_price,
            exectype=ordinfo.exec_type,
            side=ordinfo.side,
            ignore_dt=True,
        )
        if ordinfo.is_buy():
            order = LiveBuyOrder(**kwargs)
        else:
            order = LiveSellOrder(**kwargs)
        order.ref = ordinfo.ref
        order.created.dt = ts2num(ordinfo.created_dt)
        # 添加comminfo
        order.comminfo = self._broker().comminfo[data]
        self.update_order_by_ordinfo(order, ordinfo)
        return order

    def add_pending_from_query(self, ordinfo: BinanceOrderData, data):
        """
        根据查询生成新的订单, 如果订单不存在, 则加入pending或orders队列
        """
        for o in self._broker().pending:
            if o.ref == ordinfo.ref:
                return
        order = self.gen_order_from_ordinfo(ordinfo, data)
        if order.alive():
            self._broker().pending.append(order)

    def append_hist_orders(self, data=None, limit=5):
        """
        追加历史订单
        """
        if data is not None:
            datas = [data]
        else:
            datas = self._broker().get_unique_datas()
        for data in datas:
            orderdatas = self.APIManager.query_hist_orders(
                symbol=data.get_symbol(), limit=limit
            )
            for orderdata in orderdatas:
                ordinfo = BinanceOrderData.from_query(orderdata)
                order = self.gen_order_from_ordinfo(ordinfo, data)
                if order.alive():
                    continue
                if self._broker().orders:
                    last_order = self._broker().orders[-1]
                    last_created_dt = last_order.created.dt
                    if order.created.dt > last_created_dt:
                        self._broker().orders.append(order)
                else:
                    self._broker().orders.append(order)

    def query_open_position(self, data=None):
        """
        查询仓位
        """
        pos_dict = self.APIManager.get_positions()
        symbol = data.get_symbol().upper()
        return pos_dict.get(symbol, {"size": None})["size"]
