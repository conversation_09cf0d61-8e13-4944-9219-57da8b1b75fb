import collections
from backtrader.broker import BrokerBase
from backtrader.position import Position
from backtrader import num2date
from ..order import EventOrder, EventBuyOrder, EventSellOrder
from ..commissions import BackCommInfo
from logging import getLogger


logger = getLogger(__name__)


class BackBroker2(BrokerBase):
    """
    回测代理, 自行计算滑点, 手续费等
    只支持单向持仓
    可用现金不包含浮盈
    params:
        checksubmit (bool): 提交订单前是否检查保证金
        cashbalance (float): 现金余额
        commission (float): 手续费
        lever (float): 默认杠杆
    """

    # 控制状态
    (RUN, PAUSE, STOP) = ("run", "pause", "stop")

    OrderType = EventOrder

    params = (
        ("checksubmit", True),
        ("cashbalance", 10000),
        ("commission", BackCommInfo()),
        ("coc", True),
    )

    def init(self):
        super().init()
        # 现金余额,
        # 现金余额指的未开仓时的现金, 只有开平仓时会变动, 开仓后会扣除手续费, 平仓后会扣除手续费并计算浮盈
        self.cashbalance = self.p.cashbalance
        # 起始资金
        self.startingcash = self.p.cashbalance
        # 可用现金
        self.cash = self.p.cashbalance
        # 价值
        self._value = self.p.cashbalance
        # 未兑现的浮盈
        self._unrealized = 0.0  # no open position
        # 当前所有的订单列表
        self.orders = collections.deque()  # will only be appending
        # 待处理的订单列表
        self.pending = collections.deque()  # popleft and append(right)
        # 持仓
        self.positions = collections.defaultdict(Position)
        # # 杠杆
        # self.levers = collections.defaultdict(lambda: self.p.lever)
        # 通知队列
        self.notifs = collections.deque()
        # 提交但未执行的订单队列
        self.submitted = collections.deque()
        # 数据源
        self.datas = None
        # 控制状态
        self._control = self.RUN

        if None not in self.comminfo:
            self.comminfo = dict({None: self.p.commission})

    def init_datas(self, datas):
        self.datas = datas
        # 初始化数据原
        exist_symbols = []
        for data in datas:
            self.positions[data]

    def start(self):
        pass

    def getcommissioninfo(self, data):
        if data._name in self.comminfo:
            return self.comminfo[data._name]

        return self.comminfo[None]

    def setcommission(
        self,
        commission=0.0,
        leverage=20,
        name=None,
    ):
        comm = BackCommInfo(commission=commission, leverage=leverage)
        self.comminfo[name] = comm

    def addcommissioninfo(self, comminfo, name=None):
        """Adds a ``CommissionInfo`` object that will be the default for all assets if
        ``name`` is ``None``"""
        self.comminfo[name] = comminfo

    def get_notification(self):
        # 获取最新的通知
        try:
            return self.notifs.popleft()
        except IndexError:
            pass
        return None

    def get_cash(self):
        """返回可用现金的副本"""
        return float(self.cash)

    def get_cashbalance(self):
        return self.cash + self._unrealized

    def get_positions(self):
        return self.positions.copy()

    def get_size(self, data=None):
        if data is None:
            data = self.datas[0]
        return self.get_positions()[data].size

    def get_open_price(self, data=None):
        if data is None:
            data = self.datas[0]
        return self.get_positions()[data].price

    def get_abs_size(self, data=None):
        return abs(self.get_size(data=data))

    def set_cashbalance(self, cashbalance):
        self.cashbalance = cashbalance

    def set_positions(self, positions: dict):
        # 根据仓位参数设置仓位
        for data in self.datas:
            symbol = data.get_symbol()
            info = positions.get(symbol, {"price": 0, "size": 0})
            self.positions[data].set(info["size"], info["price"])
            if len(data.datetime) > 0:
                self.positions[data].updt = data.datetime[0]

    def get_levers(self):
        return {data: self.getcommissioninfo(data).leverage for data in self.datas}

    def get_lever(self):
        return self.getcommissioninfo(self.datas[0]).leverage

    def set_levers(self, levers: dict):
        for data in self.datas:
            symbol = data.get_symbol().upper()
            self.levers[data] = levers.get(symbol, 0)

    def set_contract_to_coin(self, c2c: dict):
        self.contract_to_coin = c2c

    getcash = get_cash

    def update_cash_pnl_value(self):
        # 更新cash, pnl, value
        cashbalance = self.get_cashbalance()
        positions = self.get_positions()
        levers = self.get_levers()
        pnl = 0
        cash = cashbalance
        for data, pos in positions.items():
            if len(data) == 0:  # 还未有数据, 不作计算
                continue
            abssize = abs(pos.size)
            pnl += (data.tick_close - pos.price) * pos.size
            lever = levers[data]
            if lever:
                cash -= pos.price * abssize / lever
            else:
                pass
        cash += pnl
        value = cashbalance + pnl
        self.set_cash_pnl_value(cash, pnl, value)

    def set_cash_pnl_value(self, cash, pnl, value):
        # 设置cash, pnl, value
        self.cash = cash
        self._unrealized = pnl
        self._value = value

    # def cancel(self, order, bracket=False):
    #     try:
    #         self.pending.remove(order)
    #     except ValueError:
    #         # If the list didn't have the element we didn't cancel anything
    #         return False
    #     order.cancel()
    #     self.notify(order)
    #     return True

    def get_value(self, datas=None, mkt=False, lever=False):
        # TODO data -> value
        return float(self._value)

    getvalue = get_value

    def get_orders_open(self, safe=False):
        """Returns an iterable with the orders which are still open (either not
        executed or partially executed

        The orders returned must not be touched.

        If order manipulation is needed, set the parameter ``safe`` to True
        """
        if safe:
            os = [x.clone() for x in self.pending]
        else:
            os = [x for x in self.pending]

        return os

    def getposition(self, data):
        """Returns the current position status (a ``Position`` instance) for
        the given ``data``"""
        return self.positions[data]

    def orderstatus(self, order):
        try:
            o = self.orders.index(order)
        except ValueError:
            o = order

        return o.status

    def submit(self, order, check=True):
        # 不考虑同时提交订单的情况
        return self.transmit(order, check=check)

    def transmit(self, order, check=True):
        if check and self.p.checksubmit:
            order.submit()
            self.submitted.append(order)
            self.orders.append(order)
            self.notify(order)
        else:
            self.submit_accept(order)

        return order

    def check_submitted(self):
        cash = self.cash

        while self.submitted:
            order = self.submitted.popleft()

            # clone position
            position = self.get_positions()[order.data]

            # pseudo-execute the order to get the remaining cash after exec
            res = self._try_exec(order, cash=cash, position=position)

            if res >= 0:
                self.submit_accept(order)
                continue

            order.margin()
            self.notify(order)

    def _check_margin(self, data, cash, position, price, size):
        """检查保证金"""
        comminfo = self.getcommissioninfo(data)

        exec_pos, exec_avg_price, opened, closed = position.pseudoupdate(size, price)
        # 计算手续费
        open_comm = comminfo.getcommission(size=opened, price=price)
        close_comm = comminfo.getcommission(size=closed, price=price)
        # 计算保证金
        # 保证金 = 执行后需要的保证金 - 执行前需要的保证金
        exec_margin = comminfo.getmargin(price=exec_avg_price, size=exec_pos)
        orig_margin = comminfo.getmargin(price=position.price, size=position.size)
        margin = exec_margin - orig_margin
        # 结算盈亏
        # 只结算平仓盈亏
        pnl = comminfo.profitandloss(
            size=-1 * closed, price=position.price, newprice=price
        )

        rem_cash = cash - open_comm - close_comm - margin + pnl
        return rem_cash, open_comm, close_comm, margin, pnl

    def _try_exec(
        self,
        order,
        price=None,
        cash=None,
        position=None,
    ):
        """模拟执行订单"""
        if order.exectype == order.Market:
            # 市价单
            # 检查保证金 + 手续费是否大于现金
            # 按照创建时的价格估算
            exec_size = order.executed.remsize
            exec_price = price if price else order.data.open[0]

        if order.exectype == order.Limit:
            # 限价单
            if price:
                exec_price = price
            elif order.price != None:
                exec_price = order.price
            else:
                # 没有指定price时, 可能指定了盘口价
                order_info = order.info.copy()
                if "priceMatch" in order_info.keys():
                    exec_price = order.data.open[
                        0
                    ]  # TODO 可以通过滑点系数或者最小变动单位确定
                else:
                    return -1
            if order.size != None:
                exec_size = order.executed.remsize
            else:
                # 未指定size时, 可能指定了订单价值
                order_info = order.info.copy()
                if "parValue" in order_info:
                    exec_size = order_info.parValue / exec_price
                else:
                    return -1

        rem_cash, open_comm, close_comm, margin, pnl = self._check_margin(
            data=order.data,
            cash=cash,
            position=position,
            price=exec_price,
            size=exec_size,
        )
        return rem_cash

    def _execute(self, order):
        """
        实际执行订单
        """
        cash = self.cash
        position = self.positions[order.data]
        if order.data.datetime[0] <= order.created.dt:
            # 当前时间小于等于创建时间
            return

        if order.exectype == order.Market:
            # 市价单
            exec_size = order.executed.remsize
            exec_price = order.data.open[0]  # 市价单按照当前bar开始时的价格成交

        if order.exectype == order.Limit:
            # 限价单
            if order.price != None:
                excepted_price = order.price
            else:
                # 没有指定price时, 可能指定了盘口价
                order_info = order.info.copy()
                if "priceMatch" in order_info:
                    exec_price = excepted_price = order.data.open[
                        0
                    ]  # TODO 可以通过滑点系数或者最小变动单位确定
                else:
                    raise ValueError("order has not attribute price")
            # 根据当前tick高低价判断是否成交
            tick_high = order.data.tick_high
            tick_low = order.data.tick_low
            tick_open = order.data.tick_open
            tick_close = order.data.tick_close
            # if exec_price < tick_low or exec_price > tick_high:
            #     # 没有成交
            #     return
            if order.ordtype == order.Buy and excepted_price >= tick_low:
                # 买单时, 期望价格大于tick最低价, 视为成交
                # 执行价格: 取期望价格和开盘价的最小值
                exec_price = min(tick_open, excepted_price)
            elif order.ordtype == order.Sell and excepted_price <= tick_high:
                # 卖单时, 期望价格小于tick最高价, 视为成交
                # 执行价格: 取期望价格和开盘价的最大值
                exec_price = max(tick_open, excepted_price)
            else:
                # 未成交
                return

            if order.size != None:
                exec_size = order.executed.remsize
            else:
                # 未指定size时, 可能指定了订单价值
                order_info = order.info.copy()
                if "parValue" in order_info:
                    exec_size = order_info.parValue / exec_price
                    if order.ordtype == order.Sell:
                        exec_size = -1 * exec_size
                    order.executed.remsize = exec_size
                else:
                    raise ValueError("order has not attribute size")

        rem_cash, open_comm, close_comm, margin, pnl = self._check_margin(
            data=order.data,
            cash=cash,
            position=position,
            price=exec_price,
            size=exec_size,
        )

        if rem_cash < 0:
            order.margin()
        else:
            # 更新仓位
            size, price, opened, closed = position.update(exec_size, exec_price)
            # 执行订单
            order.execute(
                dt=order.data.datetime[0],
                size=exec_size,
                price=exec_price,
                closed=closed,
                closedvalue=exec_price * closed,
                closedcomm=close_comm,
                opened=opened,
                openedvalue=exec_price * opened,
                openedcomm=open_comm,
                margin=margin,
                pnl=pnl,
                psize=exec_size,
                pprice=exec_price,
            )
            order.addcomminfo(self.getcommissioninfo(order.data))
            if order.alive() and dict(order.info).get("timeInForce", None) == "IOC":
                # IOC 执行后撤销
                ## TODO 部分成交
                order.expire()
            # 更新现金
            self.cash = rem_cash
            # 更新价值
            self.update_value()
        self.notify(order)
        return

    def update_pnl(self):
        """
        更新浮盈
        """
        positions = self.positions
        pnl = 0  # 浮盈
        for data, pos in positions.items():
            if len(data) == 0:  # 还未有数据, 不作计算
                continue
            pnl += (data.tick_close - pos.price) * pos.size

        self._unrealized = pnl

    def update_value(self):
        """
        更新价值
        """
        # 更新浮盈
        self.update_pnl()
        positions = self.positions
        pos_value = 0  # 仓位原始价值
        for data, pos in positions.items():
            if len(data) == 0:  # 还未有数据, 不作计算
                continue
            pos_value += self.getcommissioninfo(data).getvaluesize(
                size=pos.size, price=pos.price
            )
        value = self.cash + pos_value + self._unrealized
        self._value = value

    def submit_accept(self, order):
        order.pannotated = None
        order.submit()
        order.accept()
        self.pending.append(order)
        self.notify(order)

    def buy(
        self,
        owner,
        data,
        size,
        price=None,
        plimit=None,
        exectype=None,
        valid=None,
        tradeid=0,
        oco=None,
        trailamount=None,
        trailpercent=None,
        parent=None,
        transmit=True,
        histnotify=False,
        _checksubmit=True,
        side=None,
        **kwargs,
    ):

        order = EventBuyOrder(
            owner=owner,
            data=data,
            size=size,
            price=price,
            exectype=exectype,
            valid=valid,
            tradeid=tradeid,
            side=side,
        )

        order.addinfo(**kwargs)
        return self.submit(order, check=_checksubmit)

    def sell(
        self,
        owner,
        data,
        size,
        price=None,
        plimit=None,
        exectype=None,
        valid=None,
        tradeid=0,
        oco=None,
        trailamount=None,
        trailpercent=None,
        parent=None,
        transmit=True,
        histnotify=False,
        _checksubmit=True,
        side=None,
        **kwargs,
    ):

        order = EventSellOrder(
            owner=owner,
            data=data,
            size=size,
            price=price,
            exectype=exectype,
            valid=valid,
            tradeid=tradeid,
            side=side,
        )

        order.addinfo(**kwargs)
        return self.submit(order, check=_checksubmit)

    def close(
        self,
        owner,
        data,
        size=None,
        price=None,
        plimit=None,
        exectype=None,
        valid=None,
        tradeid=0,
        oco=None,
        trailamount=None,
        trailpercent=None,
        parent=None,
        transmit=True,
        histnotify=False,
        _checksubmit=True,
        side=None,
        **kwargs,
    ):
        """对某个数据源进行平仓"""
        all_size = self.get_size(data=data)
        order_size = all_size if size is None else size
        if all_size > 0:  # 做多平仓
            order = EventSellOrder(
                owner=owner,
                data=data,
                size=order_size,
                price=price,
                exectype=exectype,
                valid=valid,
                tradeid=tradeid,
                side="long",
            )
        elif all_size < 0:  # 做空平仓
            order = EventBuyOrder(
                owner=owner,
                data=data,
                size=order_size,
                price=price,
                exectype=exectype,
                valid=valid,
                tradeid=tradeid,
                side="short",
            )
        else:
            return None
        order.addinfo(**kwargs)
        return self.submit(order, check=_checksubmit)

    def cancel(self, order):
        # 撤销某个订单
        order.cancel()

    def notify(self, order):
        self.notifs.append(order.clone())

    def next(self):
        # 更新价值
        self.update_value()

        if self.p.checksubmit:
            self.check_submitted()

        # Iterate once over all elements of the pending queue
        self.pending.append(None)
        while True:
            order = self.pending.popleft()
            if order is None:
                break
            if order.is_expired():
                self.notify(order)
            else:
                if order.alive():
                    self._execute(order)
                    if order.alive():
                        self.pending.append(order)

    def cancel_all_orders(self, datas=None):
        # 撤销所有订单
        if self.no_order():
            return
        for order in self.pending:
            if order.alive():
                order.cancel()
                self.notify(order)

    def close_all_positions(self, owner):
        # 平掉所有仓位
        positions = self.get_positions()
        for data, position in positions.items():
            logger.info(f"检查仓位: {data.get_symbol()}: {position.size}")
            pos = position.size
            if pos > 0:
                self.sell(owner=owner, data=data, size=pos, side="long")
            if pos < 0:
                self.buy(owner=owner, data=data, size=pos, side="short")
        logger.info("平仓所有品种")

    def cancel_no_wait(self, order):
        # 撤销订单, 立即返回结果
        return self.cancel(order)

    def risked(self):
        # 账户是否被风控
        return False

    def update_dyn_params(self):
        # 更新动态参数
        pass

    def set_ind_dynparams(self, ind):
        # 设置需要更新的动态参数
        pass

    @property
    def control(self):
        return self._control

    def set_control(self, state: str):
        if state not in [self.RUN, self.STOP, self.PAUSE]:
            logger.warning("%s is not valid broker state", state)
            return
        self._control = state

    def get_run_state(self):
        """获取当前控制状态"""
        return self._runstate

    def is_pause(self):
        return self._control == self.PAUSE

    def is_stop(self):
        return self._control == self.STOP

    def is_run(self):
        return self._control == self.RUN

    @property
    def dynparams(self):
        return self._dyn_params

    def check_margin(self, order):
        """检查订单保证金是否足够"""
        return True

    def is_open_position(self):
        """是否持有仓位"""
        size = 0
        for Pos in self.get_positions().values():
            size += abs(Pos.size)
        return bool(size)

    def no_open_position(self):
        """是否不持仓位"""
        return not self.is_open_position()

    def is_buy_order(self):
        """是否持有buy委托"""
        return any(order.alive() and order.isbuy() for order in self.pending)

    def is_sell_order(self):
        """是否持有sell委托"""
        return any(order.alive() and order.isbuy() for order in self.pending)

    def no_order(self):
        """无委托"""
        return all(not order.alive() for order in self.pending)

    def is_order(self):
        """有委托"""
        return any(order.alive() for order in self.pending)

    def get_latest_alive_order(self):
        """获取最近的存活订单"""
        for order in list(self.pending)[::-1]:
            if order.alive():
                return order
        logger.debug("试图查找存活订单但是不存在")
        return None

    def get_alive_orders(self):
        """获取所有存活的订单"""
        return [order for order in list(self.pending)[::-1] if order.alive()]

    def get_latest_executed_order(self, data=None):
        """获取最近的已执行的订单"""
        for order in list(self.pending)[::-1]:
            if order.is_executed():
                return order
        for order in list(self.orders)[::-1]:
            if order.is_executed():
                return order
        logger.debug("试图查找已执行订单但是不存在")
        return None

    def get_holding_seconds(self, data=None):
        """holding period"""
        try:
            if data is None:
                data = self.datas[0]
            position = self.get_positions()[data]
            updt = position.updt
            if updt and self.datas[0].datetime[0]:
                holding = num2date(self.datas[0].datetime[0]) - num2date(updt)
                return holding.total_seconds()
            return 0
        except Exception as e:
            logger.exception(e)

    def query_open_position(self, data=None):
        """api请求仓位"""
        if data is None:
            data = self.datas[0]
        return self.get_positions()[data].size
