from backtrader.broker import BrokerBase
from backtrader.position import Position
from backtrader import num2date
from ..order import LiveBuyOrder, LiveSellOrder
from .controller import (
    LiveBrokerControllerBase,
    BinanceLiveBrokerController,
)
from ..commissions import LiveCommInfo
import collections
import time
from logging import getLogger

logger = getLogger(__name__)


class RunState:
    """
    策略运行状态对象, 维护当前对象的更新时间
    """

    # 控制状态
    (RUN, PAUSE, STOP) = ("run", "pause", "stop")

    def __init__(self):
        self._state = self.RUN
        self._updatedt = int(time.time() * 1000)

    def set_state(self, state):
        """设置状态, 当状态切换时更新状态时间"""
        states = [self.RUN, self.PAUSE, self.STOP]
        try:
            state = states[states.index(state.lower())]
        except ValueError:
            return
        if self._state != state:
            self._state = state
            self._updatedt = int(time.time() * 1000)

    def is_pause(self):
        return self._state == self.PAUSE

    def is_run(self):
        return self._state == self.RUN

    def is_stop(self):
        return self._state == self.STOP

    def get_keep_state_second(self):
        """
        获取当前状态持续时间, 秒数
        """
        return int(time.time()) - self._updatedt // 1000

    def get_heartbeat(self):
        """获取心跳参数"""
        return {
            "heartbeat": int(time.time() * 1000),
            "run_state": self._state,
            "keep_second": self.get_keep_state_second(),
        }


class DymParamCollection:
    """
    参数集合对象
    内部保存不同对象的引用, 修改参数时直接修改对应引用参数对象
    """

    def __init__(self):
        self._params = {}

    def add_ind_params(self, ind):
        """添加指标参数对象. 通常为metaparam对象"""
        param = ind.p
        for key in param._getkeys():
            ind_key = ".".join([ind.get_name(), key])
            if ind_key in self._params:
                raise KeyError(f"动态参数{key}已经存在!")
            self._params[ind_key] = param

    def set_value(self, key, value):
        if key not in self._params:
            return
        valid_key = key.split(".", 1)[1]
        setattr(self._params[key], valid_key, value)


class LiveBroker(BrokerBase):
    """
    实时代理,从接口中获取最新的代理数据
    不计算滑点、手续费, 只计算订单有效性和发送订单
    只支持单向持仓
    可通过订阅redis获取动态参数
    Args:
        ignore_order_dt (bool): 忽略订单执行时间
        risk_control (bool): 接受风控信号
        account_info (dict): 账户信息
        dyn_key(str): 动态参数key值
    Attrs:
        cashbalance (float): 现金余额
    """

    params = (
        ("ignore_order_dt", False),
        ("risk_control", True),
        ("account_info", {}),
        ("dyn_key", "default"),
        ("commission", LiveCommInfo()),
    )

    # broker控制器
    CONTROLLER = LiveBrokerControllerBase

    def init(self):
        super().init()
        # 现金余额, 从服务器获取
        # 现金余额指的未开仓时的现金, 只有开平仓时会变动, 开仓后会扣除手续费, 平仓后会扣除手续费并计算浮盈
        self.cashbalance = 0.0
        # 起始资金
        self.startingcash = 0.0
        # 可用现金,通过现金余额,仓位和浮盈计算得到
        self.cash = 0.0
        # 价值
        self._value = 0.0
        # 未兑现的浮盈
        self._unrealized = 0.0  # no open position
        # 当前所有的订单列表
        self.orders = collections.deque()  # will only be appending
        # 待处理的订单列表
        self.pending = collections.deque()  # popleft and append(right)
        # 持仓
        self.positions = collections.defaultdict(Position)
        # 杠杆
        self.levers = collections.defaultdict(float)
        # 张币转换
        self.contract_to_coin = collections.defaultdict(float)
        # 通知队列
        self.notifs = collections.deque()
        # 提交但未执行的订单队列
        self.submitted = collections.deque()
        # 数据源
        self.datas = []
        # 动态参数, 用来存放修改目标参数
        self._dyn_params = {}
        # 策略参数引用对象, 保存修改对象
        self._stra_p = DymParamCollection()
        # 控制状态
        self._runstate = RunState()

    def init_datas(self, datas):
        self.datas = datas
        # 初始化数据原
        # 检查datas的品种是否相同, 相同的数据源引用同一个对象
        exist_symbols = []
        for data in datas:
            symbol = data.get_symbol()
            if symbol in exist_symbols:
                self.positions[data] = self.positions[
                    datas[exist_symbols.index(symbol)]
                ]
                exist_symbols.append(None)
            else:
                self.positions[data]
                exist_symbols.append(symbol)

    def get_unique_datas(self):
        """
        获取品种不同的数据源
        """
        exist_symbols = []
        res = []
        for data in self.datas:
            symbol = data.get_symbol()
            if symbol not in exist_symbols:
                exist_symbols.append(symbol)
                res.append(data)
        return res

    def start(self):
        # 初始化控制器, 包括启动ws订阅
        self._controller = self.CONTROLLER(
            broker=self,
            account_info=self.p.account_info,
            dyn_key=self.p.dyn_key,
            risk_control=self.p.risk_control,
            datas=self.datas,
        )
        # 通过控制器初始化broker
        self._controller.init_broker()

    def stop(self):
        self._controller.stop()

    def getcommissioninfo(self, data):
        if data._name in self.comminfo:
            return self.comminfo[data._name]

        return self.comminfo[None]

    def setcommission(
        self,
        commission=0.0,
        leverage=20,
        name=None,
    ):
        comm = LiveCommInfo(commission=commission, leverage=leverage)
        self.comminfo[name] = comm

    def addcommissioninfo(self, comminfo, name=None):
        """Adds a ``CommissionInfo`` object that will be the default for all assets if
        ``name`` is ``None``"""
        self.comminfo[name] = comminfo

    def get_notification(self):
        # 获取最新的通知
        try:
            return self.notifs.popleft()
        except IndexError:
            pass
        return None

    def get_cash(self):
        """返回可用现金的副本"""
        return float(self.cash)

    def get_cashbalance(self):
        return float(self.cashbalance)

    def get_positions(self):
        return self.positions.copy()

    def get_size(self, data=None):
        if data is None:
            data = self.datas[0]
        return self.get_positions()[data].size

    def get_open_price(self, data=None):
        if data is None:
            data = self.datas[0]
        return self.get_positions()[data].price

    def get_lever(self, data=None):
        if data is None:
            data = self.datas[0]
        return self.get_levers()[data]

    def get_abs_size(self, data=None):
        return abs(self.get_size(data=data))

    def set_cashbalance(self, cashbalance):
        self.cashbalance = cashbalance

    def set_positions(self, positions: dict):
        # 根据仓位参数设置仓位
        for data in self.datas:
            symbol = data.get_symbol()
            info = positions.get(symbol, {"price": 0, "size": 0})
            self.positions[data].set(info["size"], info["price"])
            if len(data.datetime) > 0:
                self.positions[data].updt = data.datetime[0]

    def set_levers(self, levers: dict):
        """
        设置多个品种杠杆
        """
        for data in self.datas:
            symbol = data.get_symbol().upper()
            lever = levers.get(symbol, 0)
            assert lever > 0, "杠杆获取为0"
            self.levers[data] = lever
            # 设置comminfo
            self.comminfo[data] = LiveCommInfo(leverage=lever)

    def update_lever(self, lever: dict):
        """
        更新杠杆
        """
        for data in self.datas:
            symbol = data.get_symbol().upper()
            lever = lever.get(symbol, None)
            if lever is None:
                continue
            self.levers[data] = lever
            # 设置comminfo
            self.comminfo[data] = LiveCommInfo(leverage=lever)

    def set_contract_to_coin(self, c2c: dict):
        self.contract_to_coin = c2c

    getcash = get_cash

    def update_cash_pnl_value(self):
        # 通过控制器更新cash, pnl, value
        self._controller.update_cash_pnl_value()

    def set_cash_pnl_value(self, cash, pnl, value):
        # 设置cash, pnl, value
        self.cash = cash
        self._unrealized = pnl
        self._value = value

    # def cancel(self, order, bracket=False):
    #     self._controller.cancel_order(order)
    #     self.notify(order)
    #     return True

    def get_value(self, datas=None, mkt=False, lever=False):
        # TODO data -> value
        return float(self._value)

    getvalue = get_value

    def get_value_lever(self, datas=None, mkt=False):  # 暂未实现
        return self.get_value(datas=datas, mkt=mkt)

    def get_levers(self):
        return self.levers.copy()

    def get_contract_to_coin(self):
        return self.contract_to_coin.copy()

    def get_orders_open(self, safe=False):
        """Returns an iterable with the orders which are still open (either not
        executed or partially executed

        The orders returned must not be touched.

        If order manipulation is needed, set the parameter ``safe`` to True
        """
        if safe:
            os = [x.clone() for x in self.pending]
        else:
            os = [x for x in self.pending]

        return os

    def getposition(self, data):
        """Returns the current position status (a ``Position`` instance) for
        the given ``data``"""
        return self.positions[data]

    def orderstatus(self, order):
        try:
            o = self.orders.index(order)
        except ValueError:
            o = order

        return o.status

    def submit(self, order, check=True):
        # 实时broker直接提交订单
        return self.transmit(order, check=check)

    def transmit(self, order, check=True):
        # if check and self.p.checksubmit:
        #     order.submit()
        #     self.submitted.append(order)
        #     self.orders.append(order)
        #     self.notify(order)
        # else:
        #     self.submit_accept(order)
        # 实时broker中进行实时检查
        if check and order.status == order.Expired:  # 非实时订单
            logger.warning("过期订单 %s, 跳过提交", order)
            self.notify(order)
            return order

        # 仓位检查, 平仓单无法在无仓位的情况下执行
        pos = self.get_size(order.data)
        if pos == 0 and order.is_close_order():
            order.reject()
            logger.warning("错误持仓的平仓订单 %s, 跳过提交", order)
            self.notify(order)
            return order

        self.orders.append(order)
        if self.submit_accept(order):
            return order
        else:
            return None

    def submit_accept(self, order):
        order.pannotated = None
        order.submit()
        # 检查保证金是否足够
        if self.check_margin(order):
            order.accept()
            self.pending.append(order)
            # 提交订单
            ## 提交前保存一份副本, 用于通知
            clone_order = order.clone()
            order = self._controller.submit_order(order)
            if not order:  # 下单失败
                self.pending.pop()
                return None
            self.notify(clone_order)
        else:
            order.margin()
        return True

    def change_order_by_account(self, order):
        return order

    def batch_submit(self, orders, check=True):
        # 批量提交订单
        pass

    def create_order_from_params(self, order_params: dict):
        """
        根据订单参数生成订单
        """
        pass

    def buy(
        self,
        owner,
        data,
        size,
        price=None,
        plimit=None,
        exectype=None,
        valid=None,
        tradeid=0,
        oco=None,
        trailamount=None,
        trailpercent=None,
        parent=None,
        transmit=True,
        histnotify=False,
        _checksubmit=True,
        side=None,
        submit=True,
        ignore_dt=False,
        **kwargs,
    ):

        order = LiveBuyOrder(
            owner=owner,
            data=data,
            size=size,
            price=price,
            exectype=exectype,
            valid=valid,
            tradeid=tradeid,
            side=side,
            ignore_dt=(self.p.ignore_order_dt or ignore_dt),
        )

        order.addinfo(**kwargs)

        # 生成订单时添加comminfo
        order.comminfo = self.comminfo[data]
        # 根据账户类型修改订单ref
        order = self.change_order_by_account(order)
        # self._ocoize(order, oco)
        # 是否直接提交
        if submit:
            return self.submit(order, check=_checksubmit)
        else:
            return order

    def sell(
        self,
        owner,
        data,
        size,
        price=None,
        plimit=None,
        exectype=None,
        valid=None,
        tradeid=0,
        oco=None,
        trailamount=None,
        trailpercent=None,
        parent=None,
        transmit=True,
        histnotify=False,
        _checksubmit=True,
        side=None,
        submit=True,
        ignore_dt=False,
        **kwargs,
    ):

        order = LiveSellOrder(
            owner=owner,
            data=data,
            size=size,
            price=price,
            exectype=exectype,
            valid=valid,
            tradeid=tradeid,
            side=side,
            ignore_dt=(self.p.ignore_order_dt or ignore_dt),
        )

        order.addinfo(**kwargs)
        # 生成订单时添加comminfo
        order.comminfo = self.comminfo[data]
        # 根据账户类型修改订单ref
        order = self.change_order_by_account(order)
        # self._ocoize(order, oco)
        # 是否直接提交
        if submit:
            return self.submit(order, check=_checksubmit)
        else:
            return order

    def close(
        self,
        owner,
        data,
        size=None,
        price=None,
        plimit=None,
        exectype=None,
        valid=None,
        tradeid=0,
        oco=None,
        trailamount=None,
        trailpercent=None,
        parent=None,
        transmit=True,
        histnotify=False,
        _checksubmit=True,
        side=None,
        ignore_dt=False,
        **kwargs,
    ):
        """对某个数据源进行平仓"""
        all_size = self.get_size(data=data)
        if size == None:
            size = all_size
        if all_size > 0:  # 做多平仓
            return self.sell(
                owner=owner,
                data=data,
                size=size,
                price=price,
                exectype=exectype,
                valid=valid,
                tradeid=tradeid,
                side="long",
                ignore_dt=(self.p.ignore_order_dt or ignore_dt),
                **kwargs,
            )
        elif all_size < 0:  # 做空平仓
            return self.buy(
                owner=owner,
                data=data,
                size=size,
                price=price,
                exectype=exectype,
                valid=valid,
                tradeid=tradeid,
                side="short",
                ignore_dt=(self.p.ignore_order_dt or ignore_dt),
                **kwargs,
            )
        else:
            return None

    def cancel(self, order):
        # 撤销某个订单
        self._controller.cancel_order(order)

    def notify(self, order):
        self.notifs.append(order.clone())

    def next(self):
        # 数据源为历史数据时, 跳过逻辑
        if any(not data.haslivedata() for data in self.datas):
            return
        # 检查连接情况
        self._controller.check_connection()
        # Iterate once over all elements of the pending queue
        self.pending.append(None)
        while True:
            order = self.pending.popleft()
            if order is None:
                break
            if order.alive():
                # 保留存活订单
                self.pending.append(order)
            else:
                # 通知结算订单
                self.notify(order)

        self.update_cash_pnl_value()  # 更新现金和value
        self.update_dyn_params()  # 更新动态参数
        self.update_control_params()  # 更新控制参数

    def cancel_all_orders(self, datas=None):
        # 撤销所有订单
        if self.no_order():
            return
        self._controller.cancel_all_orders(datas)

    def close_all_positions(self, owner):
        # 平掉所有仓位
        positions = self.get_positions()
        for data, position in positions.items():
            logger.info(f"检查仓位: {data.get_symbol()}: {position.size}")
            pos = position.size
            if pos > 0:
                order = self.sell(
                    owner=owner,
                    data=data,
                    size=pos,
                    side="long",
                    _checksubmit=False,
                    force=True,
                )
                order.inactive_event.wait(timeout=5)
            if pos < 0:
                order = self.buy(
                    owner=owner,
                    data=data,
                    size=pos,
                    side="short",
                    _checksubmit=False,
                    force=True,
                )
                order.inactive_event.wait(timeout=5)
        logger.info("平仓所有品种完成")

    def cancel_no_wait(self, order):
        # 撤销订单, 立即返回结果
        if not order.alive():
            return order
        for i in range(10):
            self._controller.cancel_order(order)
            order.inactive_event.wait(timeout=5)  # 等待订单活跃状态中止
            if order.alive():
                # 超时后仍然保持存活，可能有异常，需要主动查询一次更新订单信息
                self._controller.query_for_update_order(order)
            return order
        raise ValueError("order update wrong, check websocket connection!")

    def risked(self):
        # 账户是否被风控
        return self.p.risk_control and self._controller.risked()

    def update_dyn_params(self):
        # 更新动态参数
        self._controller.update_dyn_params()
        for key, value in self._dyn_params.items():
            # 处理普通参数
            self._stra_p.set_value(key, value)

    def update_control_params(self):
        # 更新控制参数
        self._controller.update_control_params()

    def set_ind_dynparams(self, ind):
        # 设置需要更新的动态参数
        self._stra_p.add_ind_params(ind)

    def set_state(self, state: str):
        self._runstate.set_state(state=state)

    def get_run_state(self):
        """获取当前控制状态"""
        return self._runstate

    def is_pause(self):
        return self._runstate.is_pause()

    def is_stop(self):
        return self._runstate.is_stop()

    def is_run(self):
        return self._runstate.is_run()

    @property
    def dynparams(self):
        return self._dyn_params

    def check_margin(self, order):
        """检查订单保证金是否足够"""
        return True

    def is_open_position(self, _all=False):
        """是否持有仓位"""
        size = 0
        if _all == False:
            return bool(self.get_positions()[self.datas[0]].size)
        for Pos in self.get_positions().values():
            size += abs(Pos.size)
        return bool(size)

    def no_open_position(self, _all=False):
        """是否不持仓位"""
        return not self.is_open_position(_all=_all)

    def is_buy_order(self):
        """是否持有buy委托"""
        return any(order.alive() and order.isbuy() for order in self.pending)

    def is_sell_order(self):
        """是否持有sell委托"""
        return any(order.alive() and order.isbuy() for order in self.pending)

    def no_order(self):
        """无委托"""
        return all(not order.alive() for order in self.pending)

    def is_order(self):
        """有委托"""
        return any(order.alive() for order in self.pending)

    def get_latest_alive_order(self):
        """获取最近的存活订单"""
        for order in list(self.pending)[::-1]:
            if order.alive():
                return order
        logger.debug("试图查找存活订单但是不存在")
        return None

    def get_alive_orders(self):
        """获取所有存活的订单"""
        return [order for order in list(self.pending)[::-1] if order.alive()]

    def get_latest_executed_order(self, data=None):
        """获取最近的已执行的订单"""
        for order in list(self.pending)[::-1]:
            if order.is_executed():
                return order
        for order in list(self.orders)[::-1]:
            if order.is_executed():
                return order
        logger.debug("试图查找已执行订单但是不存在")
        return None

    def get_holding_seconds(self, data=None):
        """holding period"""
        try:
            if data is None:
                data = self.datas[0]
            position = self.get_positions()[data]
            updt = position.updt
            if updt and self.datas[0].datetime[0]:
                holding = num2date(self.datas[0].datetime[0]) - num2date(updt)
                return holding.total_seconds()
            return 0
        except Exception as e:
            logger.exception(e)

    def query_open_position(self, data=None):
        """api请求仓位"""
        try:
            if data is None:
                data = self.datas[0]
            pos = self._controller.query_open_position(data=data)
            return pos
        except Exception as e:
            logger.exception(e)
            return None


class BinanceLiveBroker(LiveBroker):
    """
    币安broker
    币安的可用余额除了常规USDT, 还包括bnb
    """

    CONTROLLER = BinanceLiveBrokerController

    def init(self):
        super().init()
        self.bnb_balance = 0.0  # bnb金额

    def set_bnb(self, bnb):
        self.bnb_balance = bnb

    def get_bnb(self):
        return float(self.bnb_balance)

    def set_positions(self, positions: dict):
        # 根据仓位参数设置仓位
        for data in self.datas:
            symbol = data.get_symbol().upper()
            info = positions.get(symbol, {"price": 0, "size": 0})
            self.positions[data].set(info["size"], info["price"])
            if len(data.datetime) > 0:
                self.positions[data].updt = data.datetime[0]

    def change_order_by_account(self, order):
        account_info = self.p.account_info
        if account_info["purpose"] == 3:
            order.ref = (account_info["link_id_future"] + order.ref)[: len(order.ref)]
        return order

    def create_order_from_params(self, order_params: dict):
        """
        根据订单参数生成订单
        """
        pass

    def get_latest_executed_order(self, data=None):
        """
        获取最近的已执行的订单
        """
        if not data:
            data = self.datas[0]
        if not self.orders:
            self._controller.append_hist_orders(data=data)
        return self.orders[-1]
