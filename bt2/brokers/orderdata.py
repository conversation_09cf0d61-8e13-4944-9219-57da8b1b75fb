### 订单数据类型

from ..order import EventOrder


class BinanceOrderData:
    """
    币安订单数据类型
    Attr:
        status(str): 订单状态
        side(str): 订单方向
        exec_type(str): 订单类型
        ord_type(str): 订单买卖
        created_size(float): 原始委托数量
        created_price(float): 原始委托价格
        executed_size(float): 执行数量
        executed_price(float): 执行均价
        cum_size(float): 订单累积开仓数量
        avg_price(float): 订单成交均价
        dt(in): 当前时间戳
        symbol(str): 品种
        comm(float): 手续费
        pnl(float): 收益
        ref(str): 订单号
        bit(bool): 是否为单次子订单
        create_dt(int): 订单创建时间戳, 一般查询时会返回
    """

    STATUS_D = {
        "NEW": EventOrder.Accepted,
        "CANCELED": EventOrder.Canceled,
        "EXPIRED": EventOrder.Expired,
        "FILLED": EventOrder.Completed,
        "PARTIALLY_FILLED": EventOrder.Partial,
    }

    ORDTYPE_D = {
        "BUY": EventOrder.Buy,
        "SELL": EventOrder.Sell,
    }

    SIDE_D = {
        "LONG": EventOrder.LONG,
        "SHORT": EventOrder.SHORT,
    }

    EXEC_TYPE = {
        "MARKET": EventOrder.Market,
        "LIMIT": EventOrder.Limit,
    }

    def __init__(self):
        self.status = EventOrder.Created
        self.side = EventOrder.LONG
        self.exec_type = EventOrder.Market
        self.ord_type = EventOrder.Buy
        self.created_size = 0  # 原始数量
        self.created_price = 0  # 原始委托价格
        self.executed_size = 0  # 本次执行数量
        self.executed_price = 0  # 本次执行价格
        self.cum_size = 0  # 订单累积开仓数量
        self.avg_price = 0  # 订单成交均价
        self.dt = 0  # 执行时间
        self.symbol = "btcusdt"  # 品种
        self.comm = 0  # 手续费
        self.pnl = 0  # 收益
        self.ref = None  # 订单编号
        self.bit = False  # 是否为子订单
        self.created_dt = None  # 订单创建时间

    def __str__(self):
        d = {
            "status": self.status,
            "side": self.side,
            "exec_type": self.exec_type,
            "ord_type": self.ord_type,
            "created_size": self.created_size,
            "created_price": self.created_price,
            "executed_size": self.executed_size,
            "executed_price": self.executed_price,
            "cum_size": self.cum_size,
            "avg_price": self.avg_price,
            "dt": self.dt,
            "symbol": self.symbol,
            "comm": self.comm,
            "pnl": self.pnl,
            "ref": self.ref,
            "bit": self.bit,
        }
        return str(d)

    def is_open(self):
        """是否为开仓"""
        return self.side * self.ord_type == 1

    def is_close(self):
        return not self.is_open()

    def is_buy(self):
        return self.ord_type == EventOrder.Buy

    def is_sell(self):
        return not self.is_buy()

    def is_bit(self):
        return self.bit

    @property
    def executed_value(self):
        return self.executed_price * self.executed_size

    @classmethod
    def from_fstream(cls, data):
        """
        根据推送信息生成
        """
        orderdata = cls()
        ordinfo = data["o"]
        orderdata.ref = ordinfo["c"]
        orderdata.exec_type = cls.EXEC_TYPE[ordinfo["o"]]
        orderdata.status = cls.STATUS_D[ordinfo["X"]]
        orderdata.ord_type = cls.ORDTYPE_D[ordinfo["S"]]
        orderdata.side = cls.SIDE_D[ordinfo["ps"]]
        orderdata.created_size = float(ordinfo["q"]) * orderdata.ord_type
        orderdata.created_price = float(ordinfo["p"])
        # 本次订单成交量
        orderdata.executed_size = float(ordinfo["l"]) * orderdata.ord_type
        # 本次订单交易价格
        orderdata.executed_price = float(ordinfo["L"])
        # 本次订单收益
        orderdata.pnl = float(ordinfo["rp"])
        # 累积成交量
        orderdata.cum_size = float(ordinfo["z"]) * orderdata.ord_type
        # 成交均价
        orderdata.avg_price = float(ordinfo["ap"])
        # 手续费
        orderdata.comm = float(ordinfo["n"])
        # 时间
        orderdata.dt = int(ordinfo["T"])
        # 子订单
        orderdata.bit = True
        return orderdata

    @classmethod
    def from_query(cls, data):
        """
        根据请求信息生成
        无单次交易信息, 合并为累积交易
        查询结果无法计算当前订单收益
        """
        orderdata = cls()
        ordinfo = data
        orderdata.ref = ordinfo["clientOrderId"]
        orderdata.exec_type = cls.EXEC_TYPE[ordinfo["type"]]
        orderdata.status = cls.STATUS_D[ordinfo["status"]]
        orderdata.ord_type = cls.ORDTYPE_D[ordinfo["side"]]
        orderdata.side = cls.SIDE_D[ordinfo["positionSide"]]
        orderdata.created_size = float(ordinfo["origQty"]) * orderdata.ord_type
        orderdata.created_price = float(ordinfo["price"])
        # 累积成交量
        orderdata.cum_size = orderdata.executed_size = (
            float(ordinfo["executedQty"]) * orderdata.ord_type
        )
        # 成交均价
        orderdata.avg_price = orderdata.executed_price = float(ordinfo["avgPrice"])
        # 时间
        orderdata.dt = int(ordinfo["updateTime"])
        # 子订单
        orderdata.bit = False
        # 创建时间
        orderdata.created_dt = int(ordinfo["time"])
        return orderdata
