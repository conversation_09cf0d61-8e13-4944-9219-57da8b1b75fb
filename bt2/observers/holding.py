from backtrader import Observer, num2date


class HoldingPeriod(Observer):
    """
    持仓时间
    """

    _stclock = True

    lines = ("second",)

    def __init__(self):
        self.pre_pos = 0
        self.startholding = 0

    def next(self):
        pos = self._owner.broker.get_size()

        if pos == 0 or pos * self.pre_pos < 0:
            self.startholding = self.data.get_curr_dt()
            self.lines.second[0] = 0

        if pos != 0:  # OPEN
            if self.startholding == 0:
                self.startholding = self.data.get_curr_dt()
            seconds = (
                num2date(self.data.get_curr_dt()) - num2date(self.startholding)
            ).total_seconds()
            self.lines.second[0] = seconds

        self.pre_pos = pos
