from backtrader import Observer


class LiveDrawDown(Observer):
    """
    实时数据回撤
    实时数据可能在tick结束前就平仓, 也可能在tick结束后才平仓, 因此保留上一次高低值数据, 根据需求获取
    """

    _stclock = True

    lines = ("maxdrawdown", "drawdown", "highest", "lowest")

    plotinfo = dict(plot=True, subplot=True)

    plotlines = dict(
        maxdrawdown=dict(
            _plotskip=True,
        )
    )

    def __init__(self):
        self.pre_pos = 0
        self._prev_highest = self._highest = float("-inf")
        self._prev_lowest = self._lowest = float("inf")

    def next(self):
        pos = self._owner.broker.get_size()
        close = self._owner.data.close[0]

        self._prev_highest = self._highest
        self._prev_lowest = self._lowest

        if pos == 0 or pos * self.pre_pos < 0:
            self._highest = float("-inf")
            self._lowest = float("inf")
            self.lines.drawdown[0] = 0
            self.lines.maxdrawdown[0] = 0

        if pos != 0:  # OPEN
            self._highest = max(self._highest, close)
            self._lowest = min(self._lowest, close)
            self.lines.drawdown[0] = self._highest - close
            self.lines.maxdrawdown[0] = self._highest - self._lowest

        self.lines.highest[0] = self._highest
        self.lines.lowest[0] = self._lowest

        self.pre_pos = pos

    def get_prev_highest(self):
        return self._prev_highest

    def get_prev_lowest(self):
        return self._prev_lowest
