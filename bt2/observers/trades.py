from backtrader import Observer


class PosTrades(Observer):
    """
    根据仓位判断一次完整开平仓
    """

    _stclock = True

    lines = ("pnlplus", "pnlminus")

    params = dict(pnlcomm=True)

    plotinfo = dict(
        plot=True,
        subplot=True,
        plotname="Trades - Net Profit/Loss",
        plotymargin=0.10,
        plothlines=[0.0],
    )

    plotlines = dict(
        pnlplus=dict(
            _name="Positive",
            ls="",
            marker="o",
            color="blue",
            markersize=8.0,
            fillstyle="full",
        ),
        pnlminus=dict(
            _name="Negative",
            ls="",
            marker="o",
            color="red",
            markersize=8.0,
            fillstyle="full",
        ),
    )

    def __init__(self):
        self._last_size = 0
        self._last_price = 0

    def next(self):
        size = self._owner.broker.get_size()
        price = self._owner.broker.get_open_price()
        close = self._owner.data.close[0]
        if self._last_size != 0 and self._last_size * size <= 0:  # 一次完整交易
            pnl = (close - self._last_price) * self._last_size
            if pnl > 0:
                self.pnlplus[0] = pnl
            else:
                self.pnlminus[0] = pnl
        self._last_size = size
        self._last_price = price
