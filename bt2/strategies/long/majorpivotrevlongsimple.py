from ..live import LongLiveStratgegy
from ...conditions.longshort import *
from ...conditions.long import *
from logging import getLogger


logger = getLogger(__name__)


class MajorPivotRevLongSimpleStrategy(LongLiveStratgegy):
    """
    IDG-拐点做多策略
    Args:
        base_value (float): 开仓基准资金金额, 资金每上升一个step, 开仓金额增加一份base_value
        step (float): 资金step金额
        open_value_rate (float): 最大持仓价值/总资金价值的比例
        price_t (float): 进入条件, 价格振幅 阈值 百分比
        turnover_t (float): 进入条件, 成交额振幅 阈值 百分比
        vol_oi_t (float): 进入条件, 成交量/持仓量 阈值 百分比
    """

    alias = ("MajorPivotRevLongSimpleStrategy",)

    params = (
        ("base_value", 800),
        ("step", 2000),
        ("open_value_rate", 50),
        ("price_t", 5),
        ("turnover_t", 150),
        ("vol_oi_t", 10),
        ("openint_thres", 5),
    )

    @classmethod
    def get_default_data_params(cls, symbol):
        if symbol != "btcusdt":
            symbol_cmp = "btcusdt"
        else:
            symbol_cmp = "ethusdt"
        params = [
            {
                "dataname": f"{symbol}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": symbol,
                "days_ago": 3,
            },
            {
                "dataname": f"{symbol_cmp}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": symbol_cmp,
                "days_ago": 3,
            },
            {
                "dataname": "btcdomusdt_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": "btcdomusdt",
                "days_ago": 3,
            },
        ]
        return params

    def init(self):
        # 增加进入条件
        self.add_entry_cond(
            SARAboveAmpOpenintVolumeChangeEntryCond,
            pricedown_thres=self.p.price_t,
            turnover_thres=self.p.turnover_t * 10000,
            vol_oi_ratio_thres=self.p.vol_oi_t,
            openint_thres=self.p.openint_thres,
        )
        self.add_entry_cond(SARSwitchToBelowEntryCond)
        self.add_entry_cond(SARPriceDownAmpEntryCond)
        self.add_entry_cond(GtCmpPriceUpEntryCond, self.datas[0], self.datas[1])
        self.add_entry_cond(
            IndexMANotDscOrderEntryCond, self.datas[1]
        )  # 规避指数的空头排列

        # 增加开仓条件
        self.add_open_cond(
            CmpStepOverPriceOpenCond,
            self.datas[0],
            self.datas[1],
            self.datas[2],
            price_over_ratio=0.001,
            base_value=self.p.base_value,
            step=self.p.step,
        )

        # 增加追单条件
        self.add_append_cond(SARBelowAppendCond, open_value_rate=self.p.open_value_rate)
        self.add_append_cond(CancelTimeOutAppendCond)

        # 增加退出条件
        self.add_exit_cond(StopLossExitCond, loss_rate=0.05)
        self.add_exit_cond(StepTrailingStopExitCond)
        self.add_exit_cond(PriceBelowSARSwitchLowExitCond)
        self.add_exit_cond(ATRStopProfitExitCond, atr_period=12, atr_ratio=2)
        self.add_exit_cond(SARShortSwitchExitCond, length=5)
        self.add_exit_cond(PriceBelowATRExitCond)
        self.add_exit_cond(
            LongButOpenIntDescExitCond, open_desc_th=-2
        )  # 抄底上升过程中，持仓量下跌x%，抄底后劲不足，退出

        # 增加平仓条件
        self.add_close_cond(StopProfitQueueCond)
        self.add_close_cond(StopLossMarketCond)

    def heartbeat(self):
        logger.info(
            "dt: %s, openint: %s",
            bt.num2date(self.data.datetime[0]),
            self.data.openint[0],
        )

    def start(self):
        # 策略初始化, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略仓位初始化, 取消所有订单")
        self.cancel_all_orders()
        self.logger.info("策略仓位初始化完成")

    def stop(self):
        # 策略停止, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略停止, 取消所有订单")
        self.cancel_all_orders()
        self.logger.info("策略停止完成")

    def notify_order(self, order):
        self.logger.info("order: %s", order)


class MajorPivotRevLongSimpleStrategy2(MajorPivotRevLongSimpleStrategy):
    alias = ("MajorPivotRevLongSimpleStrategy2",)
