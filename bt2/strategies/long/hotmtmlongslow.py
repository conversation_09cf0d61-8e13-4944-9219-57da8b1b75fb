from ..live import LongLiveStrategy
from ...conditions.longshort import *
from ...conditions.long import *
from logging import getLogger

logger = getLogger(__name__)


class HotMtmLongSlowStrategy(LongLiveStrategy):
    """
    热点强势做多-可变周期版
    文档参考: https://ji9xzzjt0z.feishu.cn/docx/M12ndl3n5od2wKxrL9kc2ByhnIc
    """

    alias = ("HotMtmLongSlowStrategy",)

    params = (("no_pos_entry", False),)

    @classmethod
    def get_default_data_params(cls, symbol):
        if symbol != "btcusdt":
            symbol_cmp = "btcusdt"
        else:
            symbol_cmp = "ethusdt"
        params = [
            {
                "dataname": f"{symbol}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": symbol,
                "days_ago": 3,
            },
            {
                "dataname": f"{symbol_cmp}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": symbol_cmp,
                "days_ago": 3,
            },
            {
                "dataname": f"{symbol}_1h",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "1h",
                "symbol": symbol,
                "days_ago": 3,
            },
        ]
        return params

    @classmethod
    def get_default_account_name(cls):
        """默认账户"""
        return "TEST-jiaoyin"

    def init(self):
        # 进入条件
        self.add_entry_cond(GtCmpPriceUpEntryCond, self.datas[0], self.datas[1])
        self.add_entry_cond(MAAscendingOrderEntryCond, self.datas[2])
        self.add_entry_cond(ADXRIncreaseEntryCond, self.datas[2])
        self.add_entry_cond(ADXPositiveEntryCond, self.datas[2])
        self.add_entry_cond(NoBrakeEntryCond)
        self.add_entry_cond(PriceAboveEntryCond)
        self.add_entry_cond(PriceOverSAREntryCond)
        self.add_entry_cond(RSIUpEntryCond)
        self.add_entry_cond(TurnoverVolOpenintEntryCond)
        self.add_entry_cond(PosValueBelowEntryCond)
        self.add_entry_cond(MAAscendingOrderEntryCond, ma_periods=(5, 15))

        # 退出条件
        self.add_exit_cond(StopLossExitCond, loss_rate=0.05)
        self.add_exit_cond(
            StepTrailingStopExitCond,
            trigger_rates=(0.02, 0.05, 0.1),
            stop_rates=(0.6, 0.6, 0.5),
        )
        self.add_exit_cond(BenchMarkBarPriceDownExitCond, benchmark_thres=5)
        self.add_exit_cond(PriceCrossLagMAExitCond)
        self.add_exit_cond(MAAndSARCrossExitCond)
        self.add_exit_cond(ATRStopProfitExitCond, atr_period=96)
        self.add_exit_cond(MADescendingOrderExitCond, self.datas[1])
        self.add_exit_cond(PriceBelowOpenLowExitCond)

        # 开仓条件
        self.add_open_cond(StepOverPriceOpenCond, price_over_ratio=None)

        # 平仓条件
        self.add_close_cond(StopProfitQueueCond)
        self.add_close_cond(StopLossMarketCond)

        # 追单条件
        self.add_append_cond(CancelTimeOutAppendCond)

    def heartbeat(self):
        logger.info(
            "dt: %s, %s",
            bt.num2date(self.data.datetime[0]),
            bt.num2date(self.datas[1].datetime[0]),
        )

    def start(self):
        # 策略初始化, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略仓位初始化, 取消所有订单, 平掉所有仓位")
        self.cancel_all_orders()
        self.close_all_positions()
        self.logger.info("策略仓位初始化完成")

    def stop(self):
        # 策略停止, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略停止, 取消所有订单, 平掉所有仓位")
        self.cancel_all_orders()
        self.close_all_positions()
        self.logger.info("策略停止完成")

    def notify_order(self, order):
        self.logger.info("order: %s", order)
