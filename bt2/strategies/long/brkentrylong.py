from ..live import LongLiveStratgegy
from ...conditions.longshort import *
from ...conditions.long import *
from logging import getLogger

logger = getLogger(__name__)


class BrkEntryLongStrategy(LongLiveStratgegy):
    """
    龙头热点做多, 策略进入, 人工退出
    """

    alias = ("BrkEntryLongStrategy",)

    params = (("no_pos_entry", False),)

    @classmethod
    def get_default_data_params(cls, symbol):
        params = [
            {
                "dataname": f"{symbol}_1h",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "1h",
                "symbol": symbol,
                "days_ago": 3,
            },
        ]
        return params

    def init(self):
        # 进入条件
        self.add_entry_cond(MAAscendingOrderEntryCond, ma_periods=(1, 10))
        self.add_entry_cond(PriceAboveEntryCond)
        self.add_entry_cond(VolumeOverMax, vol_ratio=2)
        self.add_entry_cond(LastOrderPriceUpEntryCond)

        # 退出条件
        self.add_exit_cond(StopLossExitCond, loss_rate=0.05)
        self.add_exit_cond(PriceBelowLowExitCond)

        # 开仓条件
        self.add_open_cond(BalanceRatioOpenCond)
        # 平仓条件
        self.add_close_cond(StopProfitQueueCond)
        self.add_close_cond(StopLossMarketCond)

        # 追单条件
        self.add_append_cond(CancelTimeOutAppendCond)

        # 停止条件
        self.add_stop_cond(LosingStreakStopCond)

    def heartbeat(self):
        logger.info("dt: %s", bt.num2date(self.data.datetime[0]))

    def start(self):
        # 策略初始化, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略仓位初始化, 取消所有订单, 平掉所有仓位")
        self.cancel_all_orders()
        self.close_all_positions()
        self.logger.info("策略仓位初始化完成")

    def stop(self):
        # 策略停止, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略停止, 取消所有订单, 平掉所有仓位")
        self.cancel_all_orders()
        self.close_all_positions()
        self.logger.info("策略停止完成")

    def notify_order(self, order):
        self.logger.info("order: %s", order)
