from ..live import LongLiveStrategy
from ...conditions.longshort import *
from ...conditions.long import *
from logging import getLogger

logger = getLogger(__name__)


class ManualInAutoOutLongSlowStrategy(LongLiveStrategy):
    """
    人工智能
    文档参考: https://ji9xzzjt0z.feishu.cn/docx/YhIwdpCnVobxfwxmOuZcoaYMnid?from=from_copylink
    """

    alias = ("ManualInAutoOutLongSlowStrategy",)

    @classmethod
    def get_default_data_params(cls, symbol):
        params = [
            {
                "dataname": f"{symbol}_1m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "1m",
                "symbol": symbol,
                "days_ago": 2,
            },
            {
                "dataname": f"{symbol}_1h",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "1h",
                "symbol": symbol,
                "days_ago": 2,
            },
        ]
        return params

    @classmethod
    def get_default_account_name(cls):
        """默认账户"""
        return "TEST-tanli-bro"

    def init(self):
        self._entrys = []
        # 进入条件
        self.add_entry_cond(ManualFollowEntryCond)

        # 退出条件
        self.add_exit_cond(StopLossExitCond, loss_rate=0.05)  # 止损退出
        self.add_exit_cond(TrailingStopLiveExitCond, stop_profit_rate=0.6)  # 移动止盈
        # self.add_exit_cond(BreakevenLiveExitCond, stop_rate=0.002) # 保本退出
        # self.add_exit_cond(ChandelierExitCond, self.datas[1]) # 吊灯退出（增加）
        self.add_exit_cond(
            VolumeSurgeRSIExitCond, self.datas[1]
        )  # 放量冲高退出（增加）
        self.add_exit_cond(
            BenchMarkBarPriceDownExitCond, self.datas[1], benchmark_thres=5
        )  # 大振幅退出
        # self.add_exit_cond(SARCrossingAboveExitCond, self.datas[1]) # sar反转退出（增加）
        self.add_exit_cond(BrakeExitCond)  # 刹车退出
        self.add_exit_cond(
            ATRStopProfitExitCond, self.datas[1], atr_period=24
        )  # 主动atr止盈（增加）
        self.add_exit_cond(ManualPriceCrossLagMAExitCond, self.datas[1])  # 价格穿越退出

        # 开仓条件
        self.add_open_cond(NoOpenCond)
        # 平仓条件
        self.add_close_cond(StopProfitQueueCond)
        self.add_close_cond(StopLossMarketCond)

    def heartbeat(self):
        logger.info("dt: %s", bt.num2date(self.data.datetime[0]))

    def preheartbeat(self):
        logger.info("preheatbeat dt: %s", bt.num2date(self.data.datetime[0]))

    def start(self):
        # 策略初始化, 撤销所有订单, 平掉所有仓位
        # self.logger.info('策略仓位初始化, 取消所有订单, 平掉所有仓位')
        # self.cancel_all_orders()
        # self.close_all_positions()
        self.logger.info("MIAO_long策略仓位初始化完成")

    def stop(self):
        # 策略停止, 撤销所有订单, 平掉所有仓位
        # self.logger.info('策略停止, 取消所有订单, 平掉所有仓位')
        # self.cancel_all_orders()
        # self.close_all_positions()
        self.logger.info("策略停止完成")

    def notify_order(self, order):
        self.logger.info("order: %s", order)
