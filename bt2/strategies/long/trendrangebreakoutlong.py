from ..live import LongLiveStratgegy
from ...conditions.longshort import *
from ...conditions.long import *
from logging import getLogger

logger = getLogger(__name__)


class TrendRangeBreakoutLongStrategy(LongLiveStratgegy):
    """
    IDG-蓄势做多策略
    文档参考: https://ji9xzzjt0z.feishu.cn/docx/Ka6tdkgQMoCgirxhtffcMWhunWf
    """

    alias = ("TrendRangeBreakoutLongStrategy",)

    params = (
        # ('no_pos_entry', False),
    )

    @classmethod
    def get_default_data_params(cls, symbol):
        if symbol.lower() == "btcusdt":
            cmp_symbol = "ethusdt"
        else:
            cmp_symbol = "btcusdt"
        params = [
            {
                "dataname": f"{symbol}_1m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "1m",
                "symbol": symbol,
                "days_ago": 7,
            },
            {
                "dataname": f"{symbol}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": symbol,
                "days_ago": 7,
            },
            {
                "dataname": f"{cmp_symbol}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": cmp_symbol,
                "days_ago": 7,
            },
        ]
        return params

    def init(self):
        trend = self.add_entry_cond(TrendRangeEntryCond, self.datas[1])
        self.add_entry_cond(TrendEndUpEntryCond, self.datas[0], trend)
        self.add_entry_cond(TrendPriceUpAboveCmpEntryCond, self.datas[2], trend)
        # 退出条件
        self.add_exit_cond(ATRStopProfitExitCond, atr_ratio=0.5)
        self.add_exit_cond(PriceBelowTrendRangeLowExitCond, self.datas[1], trend)
        self.add_exit_cond(PriceBelowTrendLenLowExitCond, self.datas[1], trend)

        # 开仓条件
        self.add_open_cond(StepOverPriceOpenCond, price_over_ratio=None)

        # 平仓条件
        self.add_close_cond(StopProfitQueueCond)
        self.add_close_cond(StopLossMarketCond)

        # 追单条件
        self.add_append_cond(CancelTimeOutAppendCond)

        # 停止条件
        self.add_stop_cond(LosingStreakStopCond)

    def heartbeat(self):
        logger.info(
            "dt: %s, %s, %s",
            bt.num2date(self.data.datetime[0]),
            bt.num2date(self.datas[1].datetime[0]),
            bt.num2date(self.datas[1].get_closedt()),
        )

    def start(self):
        # 策略初始化, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略仓位初始化, 取消所有订单")
        self.cancel_all_orders()
        self.logger.info("策略仓位初始化完成")

    def stop(self):
        # 策略停止, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略停止, 取消所有订单")
        self.cancel_all_orders()
        self.logger.info("策略停止完成")

    def notify_order(self, order):
        self.logger.info("order: %s", order)
