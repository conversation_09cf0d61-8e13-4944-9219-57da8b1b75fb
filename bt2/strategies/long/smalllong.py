from ...strategies.live import LongLiveStrategy
from ...conditions import *
from logging import getLogger

logger = getLogger(__name__)


class SmallLongStrategy(LongLiveStrategy):
    """
    趋势跟踪
    """

    alias = ('SmallLongStrategy', )

    params = (
        ('no_pos_entry', False),
        ('base_value', 800),
        ('step', 2000),
        ('open_value_rate', 50),
    )

    @classmethod
    def get_default_data_params(cls, symbol):
        if symbol != 'btcusdt':
            symbol_cmp = 'btcusdt'
        else:
            symbol_cmp = 'ethusdt'
        params = [
            {
                "dataname": f"{symbol}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": symbol,
                "days_ago": 3,
            },
            {
                "dataname": f"{symbol_cmp}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": symbol_cmp,
                "days_ago": 3,
            },
            {
                "dataname": "btcdomusdt_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": "btcdomusdt",
                "days_ago": 3,
            },

        ]
        return params
        
    @classmethod
    def get_default_account_name(cls):
        """默认账户"""
        return ''

    def init(self):
        # 进入条件
        self.add_entry_cond(MACrossEntryCond, self.datas[0], ma_periods=(5, 20, 60))  # 均线向上发散趋势
        self.add_entry_cond(PriceSoftEntryCond, period=10, rsi_low_thres=50, rsi_high_thres=70)  # 温和的价格变化
        # self.add_entry_cond(VolumeSoftEntryCond, period=10)  # 温和的交易量变化
        self.add_entry_cond(NoBrakeEntryCond)  # 规避指数下跌
        self.add_entry_cond(PriceAboveEntryCond)
        self.add_entry_cond(PosValueBelowEntryCond)

        # 退出条件,止损
        self.add_exit_cond(StopLossExitCond, loss_rate=0.02)
        self.add_exit_cond(BenchMarkBarPriceDownExitCond, benchmark_thres=5)  # 关键K破位停止

        # 退出条件,止盈
        self.add_exit_cond(StepTrailingStopExitCond, trigger_rates=(0.01, 0.02, 0.5), stop_rates=(0.6, 0.6, 0.5))
        self.add_exit_cond(ATRStopProfitExitCond, atr_period=96)
        self.add_exit_cond(MADescendingOrderExitCond, ma_periods=(5, 20))

        # 开仓条件
        self.add_open_cond(CmpStepOverPriceOpenCond, self.datas[0], self.datas[1], self.datas[2],
                           price_over_ratio=0.001, base_value=self.p.base_value, step=self.p.step)
        
        # 平仓条件
        self.add_close_cond(StopProfitQueueCond)
        self.add_close_cond(StopLossMarketCond)

        # 追单条件
        self.add_append_cond(CancelTimeOutAppendCond)

    def heartbeat(self):
        logger.info('dt: %s, %s', bt.num2date(self.data.datetime[0]), bt.num2date(self.datas[1].datetime[0]))

    def start(self):
        # 策略初始化, 撤销所有订单, 平掉所有仓位
        self.logger.info('策略仓位初始化, 取消所有订单, 平掉所有仓位')
        self.cancel_all_orders()
        self.close_all_positions()
        self.logger.info('策略仓位初始化完成')

    def stop(self):
        # 策略停止, 撤销所有订单, 平掉所有仓位
        self.logger.info('策略停止, 取消所有订单, 平掉所有仓位')
        self.cancel_all_orders()
        self.close_all_positions()
        self.logger.info('策略停止完成')

    def notify_order(self, order): 
        self.logger.info('order: %s', order)