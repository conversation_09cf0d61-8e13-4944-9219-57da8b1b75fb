from ..live import LongLiveStrategy
from ...conditions.longshort import *
from ...conditions.long import *
from logging import getLogger

logger = getLogger(__name__)


class IndexRevLongStrategy(LongLiveStrategy):
    """
    指数做多
    """

    alias = ("IndexRevLongStrategy",)

    params = (
        ("no_pos_entry", False),
        ("base_value", 200),
    )

    @classmethod
    def get_default_data_params(cls, symbol):
        if symbol != "btcusdt":
            symbol_cmp = "btcusdt"
        else:
            symbol_cmp = "ethusdt"
        params = [
            {
                "dataname": f"{symbol}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": symbol,
                "days_ago": 3,
            },
            {
                "dataname": f"{symbol_cmp}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": symbol_cmp,
                "days_ago": 3,
            },
        ]
        return params

    def init(self):
        # 进入条件
        self.add_entry_cond(GtCmpPriceUpEntryCond, self.datas[0], self.datas[1])
        self.add_entry_cond(SARSwitchToBelowEntryCond)
        self.add_entry_cond(PriceBelowSARSwitchStartEntryCond)
        self.add_entry_cond(SARPriceDownAmpEntryCond, down_amp_thres=30)
        self.add_entry_cond(MAAscendingOrderEntryCond, ma_periods=(5, 15))

        self.add_entry_cond(PosValueBelowEntryCond)

        # 增加退出条件
        self.add_exit_cond(StopLossExitCond, loss_rate=0.05)
        self.add_exit_cond(PriceBelowSARSwitchLowExitCond)
        self.add_exit_cond(ATRStopProfitExitCond, atr_period=12, atr_ratio=2)
        self.add_exit_cond(SARSwitchToAboveExitCond)

        # 增加开仓条件
        self.add_open_cond(StepOverPriceOpenCond, base_value=self.p.base_value)
        # 平仓条件
        self.add_close_cond(StopProfitQueueCond)
        self.add_close_cond(StopLossMarketCond)
        self.add_exit_cond(PriceBelowATRExitCond)

        # 追单条件
        self.add_append_cond(CancelTimeOutAppendCond)

    def heartbeat(self):
        logger.info("dt: %s", bt.num2date(self.data.datetime[0]))

    def start(self):
        # 策略初始化, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略仓位初始化, 取消所有订单")
        self.cancel_all_orders()
        self.logger.info("策略仓位初始化完成")

    def stop(self):
        # 策略停止, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略停止, 取消所有订单")
        self.cancel_all_orders()
        self.logger.info("策略停止完成")

    def notify_order(self, order):
        self.logger.info("order: %s", order)


class IndexRevLongStrategy2(IndexRevLongStrategy):
    """
    IndexRevLongStrategy2
    """

    alias = ("IndexRevLongStrategy2",)
