__all__ = [
    "TestStrategy",
]


import backtrader as bt
from ..indicators import *
from ..conditions import *
from ..strategy import *
from ..storages import *
from .live import EntryExitStrategy
from logging import getLogger

logger = getLogger(__name__)


class TestStrategy(EntryExitStrategy):
    """
    demo策略
    """

    alias = ("TestStrategy",)

    params = (("p1", 20),)

    @classmethod
    def get_default_data_params(cls, symbol):
        params = [
            {
                "dataname": f"{symbol}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": symbol,
                "days_ago": 5,
                # "is_live": False,
            },
        ]
        return params

    @classmethod
    def get_default_account_name(cls):
        """默认账户"""
        return "liquid_test"

    def init(self):
        self.t = self.n = 0
        pass
        self.t = self.n = 0

    def next(self):
        pass
        logger.info(self.broker.pending)
        if self.next_stop():
            return self.env.runstop()
        if self.t >= 3 and self.broker.no_order():
            # 任务停止
            self.logger.info("task stop")
            self.env.runstop()
            return
        if self.data.is_live_status():
            logger.info("openInt:%s", self.data.openint[0])
            logger.info("%s, n:%s, t:%s", self.data.datetime.datetime(), self.n, self.t)
            if self.n == 20:
                logger.info("sell")
                self.sell(side="short", parValue=20)

            if self.n == 50:
                logger.info("close")
                self.buy(side="short", parValue=20, trigger="test", ignore_dt=True)
                self.n = 0
                self.t += 1

            self.n += 1

    def stop(self):
        logger.info("自动停止")

    def start(self):
        logger.info("任务开始")

    def notify_order(self, order):
        self.logger.info("order: %s", order)

    def preheartbeat(self):
        logger.info("preheatbeat dt: %s", bt.num2date(self.data.datetime[0]))

    def heartbeat(self):
        logger.info(
            "dt: %s, %s, %s",
            bt.num2date(self.data.datetime[0]),
            self.broker.get_size(),
            self.broker.get_open_price(),
        )
