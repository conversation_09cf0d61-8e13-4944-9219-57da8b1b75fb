__all__ = [
    "LiveEntryExitStrategy",
    "LongLiveStrategy",
    "ShortLiveStrategy",
]

from ..strategy import EntryExitStrategy
from ..conditions import *
from logging import getLogger

logger = getLogger(__name__)


class LiveEntryExitStrategy(EntryExitStrategy):
    """
    实时条件策略
    """

    params = (
        ("simulate", False),
        ("no_pos_entry", True),
        ("no_order_entry", True),
    )

    def preinit(self):
        # 添加默认进入条件
        self.add_entry_cond(RunStateCond)
        if not self.p.simulate:
            self.add_entry_cond(LiveDataCond)
        if self.p.no_pos_entry:
            self.add_entry_cond(NoPosEntryCond)
        if self.p.no_order_entry:
            self.add_entry_cond(NoOrderEntryCond)
        self.add_entry_cond(BarOpenOnceEntryCond)
        # 添加默认停止条件
        self.add_stop_cond(StopStateCond)
        self.add_stop_cond(RiskStopCond)


class LongLiveStrategy(LiveEntryExitStrategy):
    """
    实时做多
    """

    params = (("side", EntryExitStrategy.LONG),)


class ShortLiveStrategy(LiveEntryExitStrategy):
    """
    实时做空
    """

    params = (("side", EntryExitStrategy.SHORT),)
