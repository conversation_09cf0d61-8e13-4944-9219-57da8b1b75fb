__all__ = [
    "YugeStrategy",
]


import backtrader as bt
from ...indicators import *
from ...conditions import *
from ...strategy import *
from ...storages import *
from ..live import LiveEntryExitStrategy
from logging import getLogger

logger = getLogger(__name__)


class YugeStrategy(LiveEntryExitStrategy):
    """
    宇哥设计的多空策略

    策略概述:
    这是一个基于技术指标的多空交易策略，主要通过均线趋势、MACD信号、
    成交量放量和K线形态等多重条件来判断入场时机。

    核心逻辑:
    1. 趋势判断: 使用60周期和24周期均线配合MACD来确定大趋势方向
    2. 入场信号: 连续放量配合阳线/阴线形态，在适当振幅范围内入场
    3. 风险控制: 设置止损和分批止盈机制

    适用市场: 期货市场，1分钟级别数据
    """

    alias = ("YugeStrategy",)

    params = (
        (
            "open_size",  # 开仓数量
            9,
        ),
    )

    @classmethod
    def get_default_data_params(cls, symbol):
        """
        获取默认数据参数配置

        Args:
            symbol: 交易标的符号

        Returns:
            list: 数据源配置参数列表
        """
        params = [
            {
                "dataname": f"{symbol}_1m",  # 数据源名称，1分钟K线
                "platform": "binance",  # 交易平台
                "inst_type": "futures",  # 合约类型：期货
                "freq": "1m",  # 数据频率：1分钟
                "symbol": symbol,  # 交易标的
                # "days_ago": 5,            # 历史数据天数（可选）
                # "is_live": False,         # 是否实时数据（可选）
            },
        ]
        return params

    @classmethod
    def get_default_account_name(cls):
        """
        获取默认账户名称

        Returns:
            str: 默认使用的测试账户名
        """
        return "liquid_test"

    def init(self):
        """
        策略初始化方法

        设置策略的核心组件:
        1. 平均价格指标 - 用于计算持仓均价
        2. 入场条件 - YugeEntryCond
        3. 止损退出条件 - YugeStopLossExitCond
        4. 止盈退出条件 - YugeStopProfitExitCond
        """
        # 获取持仓平均价格指标
        avgPrice = AvgPriceInd()

        # 添加入场条件
        entry = self.add_entry_cond(YugeEntryCond)

        # 添加止损退出条件
        self.add_exit_cond(YugeStopLossExitCond, self.datas[0], entry)

        # 添加止盈退出条件
        self.add_exit_cond(YugeStopProfitExitCond, self.datas[0], entry, avgPrice)

    def stop(self):
        """策略停止时的回调方法"""
        logger.info("自动停止")

    def start(self):
        """策略启动时的回调方法"""
        logger.info("任务开始")

    def notify_order(self, order):
        """订单状态变化通知方法"""
        self.logger.info("order: %s", order)

    def preheartbeat(self):
        """心跳前置处理方法，记录当前时间"""
        logger.info("preheatbeat dt: %s", bt.num2date(self.data.datetime[0]))


class YugeEntryCond(EntryCond):
    """
    宇哥策略的入场条件类

    该类实现了多空入场的核心逻辑，通过多重技术指标的组合来判断入场时机:

    技术指标组合:
    1. 均线趋势: 60周期均线与24周期均线的关系判断大趋势
    2. MACD信号: MACD线与0轴的关系确认趋势方向
    3. 成交量: 连续3周期的成交量递增
    4. K线形态: 连续5根阳线(做多)或阴线(做空)
    5. 振幅控制: 5周期振幅累积在合理范围内(0.2%-2%)
    6. 价格过滤: 价格与10周期均线的关系作为最终过滤

    入场逻辑:
    - 做多: 趋势向上 + 放量 + 连续阳线 + 振幅合理 + 价格高于10均线
    - 做空: 趋势向下 + 放量 + 连续阴线 + 振幅合理 + 价格低于10均线
    """

    alias = ("YugeEntryCond",)

    params = (
        (
            "open_size",  # 开仓数量
            9,
        ),
    )

    def __init__(self):
        """
        初始化入场条件的各项技术指标
        """
        # === 趋势判断指标 ===
        # 24周期简单移动平均线 (短期趋势)
        self.sma24 = bt.indicators.SMA(self.data, period=24)
        self.sma24_plot = BPlot(self.sma24, b_subplot=False)
        self.sma24_plot.plotlines.value.b_color = "green"  # 绿色显示

        # 60周期简单移动平均线 (长期趋势)
        self.sma60 = bt.indicators.SMA(self.data, period=60)
        self.sma60_plot = BPlot(self.sma60, b_subplot=False)
        self.sma60_plot.plotlines.value.b_color = "red"  # 红色显示

        # 均线关系比较: sma60 > sma24 为1(上涨趋势), sma60 < sma24 为-1(下跌趋势)
        maCond = Cmp(self.sma60, self.sma24, b_plot=True)

        # === MACD趋势确认指标 ===
        # MACD指标 (12, 26, 9)
        self.macd = bt.indicators.MACD(
            self.data, period_me1=12, period_me2=26, period_signal=9
        )
        # MACD线与0轴比较: > 0 为1(多头), < 0 为-1(空头)
        self.macdCond = CmpValue(self.macd.macd, num=0)

        # === 趋势方向确定 ===
        # 只有当均线趋势和MACD信号一致时才确定趋势方向，否则为0(观望)
        self.trendDirection = If(Eq(maCond, self.macdCond), maCond, ConstValue(num=0))

        # === 成交量放量条件 ===
        # 连续3周期成交量递增
        self.volInc = Increase(Lag(self.data.volume), period=3)

        # === K线形态判断 ===
        # 判断是否为阳线(收盘价 > 开盘价)
        isBull = BullishBar(self.data)

        # 连续5根阳线条件 (做多信号)
        bullCond = GteValue(AccumulateInd(isBull, period=5), num=5)
        # 连续5根阴线条件 (做空信号)
        bearCond = GteValue(AccumulateInd(Not(isBull), period=5), num=5)

        # === 振幅控制条件 ===
        # 当前周期振幅率
        self.amp = AmplitudeRate(self.data, period=1)
        # 5周期振幅累积
        self.ampAcc = AccumulateInd(self.amp, period=5, b_plot=True)
        # 振幅在合理范围内: 0.2% < 累积振幅 < 2%
        ampCond = And(LtValue(self.ampAcc, num=2), GtValue(self.ampAcc, num=0.2))

        # === 最终入场条件组合 ===
        # 做多条件: 上涨趋势 + 放量 + 连续阳线 + 振幅合理
        self.longCond = And(
            EqValue(self.trendDirection, num=1),  # 趋势向上
            self.volInc,  # 成交量递增
            bullCond,  # 连续阳线
            ampCond,  # 振幅合理
        )

        # 做空条件: 下跌趋势 + 放量 + 连续阴线 + 振幅合理
        self.shortCond = And(
            EqValue(self.trendDirection, num=-1),  # 趋势向下
            self.volInc,  # 成交量递增
            bearCond,  # 连续阴线
            ampCond,  # 振幅合理
        )

        # === 价格过滤指标 ===
        # 10周期均线，用于最终的价格过滤
        self.sma10 = bt.indicators.SMA(self.data, period=10)

    def next(self):
        """
        每个数据周期的入场条件判断

        在满足基础条件的前提下，加入价格与10周期均线的关系作为最终过滤:
        - 做多: 基础做多条件 + 当前价格高于10周期均线
        - 做空: 基础做空条件 + 当前价格低于10周期均线

        Returns:
            int: 1=做多信号, -1=做空信号, 0=无信号
        """
        value = 0

        # 做多信号: 满足做多条件且价格突破10均线
        if self.longCond[0] and self.sma10[0] < self.data.close[0]:
            value = 1

        # 做空信号: 满足做空条件且价格跌破10均线
        if self.shortCond[0] and self.sma10[0] > self.data.close[0]:
            value = -1

        return value

    def submit_open_order(self):
        """
        提交开仓订单

        根据入场信号提交相应的买入或卖出订单:
        - 使用IOC(立即成交或取消)订单类型
        - 使用OPPONENT_5价格匹配模式(对手价+5档)

        Returns:
            bool: True=成功提交订单, False=无信号或提交失败
        """
        # 做多开仓
        if self.lines.value[0] == 1:
            self._owner.buy(
                size=self.p.open_size,  # 开仓数量
                side="long",  # 做多方向
                priceMatch="OPPONENT_5",  # 对手价+5档
                timeInForce="IOC",  # 立即成交或取消
            )
            return True

        # 做空开仓
        if self.lines.value[0] == -1:
            self._owner.sell(
                size=self.p.open_size,  # 开仓数量
                side="short",  # 做空方向
                priceMatch="OPPONENT_5",  # 对手价+5档
                timeInForce="IOC",  # 立即成交或取消
            )
            return True

        return False


class YugeStopProfitExitCond(ExitCond):
    """
    宇哥策略的止盈退出条件类

    该类实现了基于盈亏比的分批止盈策略:

    止盈逻辑:
    1. 计算当前盈亏比 = 当前盈利 / 初始风险
    2. 盈亏比2-3倍时: 部分止盈(1/3仓位)
    3. 盈亏比>3倍时: 根据价格与10均线关系决定是否止盈

    风险计算:
    - 做多: 风险 = 开仓价 - 开仓时前一根K线的最低价
    - 做空: 风险 = 开仓时前一根K线的最高价 - 开仓价

    盈利计算:
    - 做多: 盈利 = 当前价格 - 开仓价
    - 做空: 盈利 = 开仓价 - 当前价格
    """

    alias = ("YugeStopProfitExitCond",)

    params = (
        (
            "open_size",  # 开仓数量
            9,
        ),
    )

    def __init__(self):
        """
        初始化止盈条件的相关指标
        """
        # === 持仓信息获取 ===
        # 持仓方向: 1=多头, -1=空头
        self.posSide = Step(self.datas[1])

        # 开仓价格
        entryPrice = self.datas[2]

        # === 止损价格计算 ===
        # 做多止损价格: 开仓时前一根K线的最低价
        longStopPrice = Step(
            If(EqValue(self.datas[1], num=1), Lag(self.data.low), ConstValue(num=0)),
        )

        # 做空止损价格: 开仓时前一根K线的最高价
        shortStopPrice = Step(
            If(EqValue(self.datas[1], num=-1), Lag(self.data.high), ConstValue(num=0)),
        )

        # === 技术指标 ===
        # 10周期均线，用于高盈亏比时的退出判断
        self.sma10 = bt.indicators.SMA(self.data, period=10, b_plot=True)

        # === 盈亏比计算 ===
        # 根据持仓方向选择对应的止损价格
        stopPrice = If(EqValue(self.posSide, num=1), longStopPrice, shortStopPrice)

        # 初始风险 = |开仓价 - 止损价|
        risk = Abs(entryPrice - stopPrice)

        # 当前盈利 = (当前价格 - 开仓价) * 持仓方向
        profit = Mul(self.data.close - entryPrice, self.posSide)

        # 盈亏比 = 当前盈利 / 初始风险
        self.profitRatio = Div(profit, risk, b_plot=True)

    def next(self):
        """
        每个数据周期的止盈条件判断

        分层止盈策略:
        1. 盈亏比2-3倍: 立即触发部分止盈
        2. 盈亏比>3倍: 根据价格与10均线关系判断
           - 做多: 当最高价回落至10均线以下时止盈
           - 做空: 当最低价反弹至10均线以上时止盈

        Returns:
            bool: True=触发止盈, False=不触发
        """
        # 盈亏比在2-3倍之间，立即部分止盈
        if self.profitRatio[0] >= 2 and self.profitRatio[0] <= 3:
            return True

        # 盈亏比超过3倍，根据价格与均线关系判断
        if self.profitRatio[0] > 3:
            # 做多: 最高价回落至10均线以下
            # 做空: 最低价反弹至10均线以上
            return (self.posSide == 1 and self.data.high < self.sma10) or (
                self.posSide == -1 and self.data.low > self.sma10
            )

    def submit_close_order(self):
        """
        提交平仓订单

        根据盈亏比确定平仓数量:
        - 盈亏比2-3倍: 平仓1/3仓位(部分止盈)
        - 盈亏比>3倍: 平仓全部剩余仓位

        Returns:
            bool: True=成功提交平仓订单
        """
        # 确定平仓数量
        if self.profitRatio[0] >= 2 and self.profitRatio[0] <= 3:
            size = self.p.open_size / 3  # 部分止盈: 1/3仓位
        else:
            size = None  # 全部平仓

        # 提交平仓订单
        self._owner.close(
            size=size,  # 平仓数量
            priceMatch="OPPONENT_5",  # 对手价+5档
            timeInForce="IOC",  # 立即成交或取消
            trigger=self.get_name(),  # 触发器名称
        )
        return True


class YugeStopLossExitCond(ExitCond):
    """
    宇哥策略的止损退出条件类

    该类实现了基于前一根K线极值的止损策略:

    止损逻辑:
    - 做多止损: 当前最低价跌破开仓时前一根K线的最低价
    - 做空止损: 当前最高价突破开仓时前一根K线的最高价

    止损特点:
    1. 使用开仓时刻的前一根K线极值作为止损位
    2. 一旦触发立即全仓止损
    3. 采用市价单快速止损，避免滑点扩大损失
    """

    alias = ("YugeStopLossExitCond",)

    params = (
        (
            "open_size",  # 开仓数量
            9,
        ),
    )

    def __init__(self):
        """
        初始化止损条件的相关指标
        """
        # === 持仓信息获取 ===
        # 持仓方向: 1=多头, -1=空头
        self.posSide = Step(self.datas[1])

        # === 止损价格设定 ===
        # 做多止损价格: 开仓时前一根K线的最低价
        self.longStopPrice = Step(
            If(EqValue(self.datas[1], num=1), Lag(self.data.low), ConstValue(num=0)),
        )

        # 做空止损价格: 开仓时前一根K线的最高价
        self.shortStopPrice = Step(
            If(EqValue(self.datas[1], num=-1), Lag(self.data.high), ConstValue(num=0)),
        )

        # === 止损触发条件 ===
        # 做多止损条件: 当前最低价 < 止损价格
        self.longStopLossCond = Lt(self.data.low, self.longStopPrice)

        # 做空止损条件: 当前最高价 > 止损价格
        self.shortStopLossCond = Gt(self.data.high, self.shortStopPrice)

    def next(self):
        """
        每个数据周期的止损条件判断

        根据持仓方向判断是否触发止损:
        - 多头持仓: 检查做多止损条件
        - 空头持仓: 检查做空止损条件

        Returns:
            bool: True=触发止损, False=不触发
        """
        return (self.posSide[0] == 1 and self.longStopLossCond[0]) or (
            self.posSide[0] == -1 and self.shortStopLossCond[0]
        )

    def submit_close_order(self):
        """
        提交止损平仓订单

        一旦触发止损，立即全仓平仓:
        - 不指定size参数，默认全仓平仓
        - 使用对手价+5档确保快速成交
        - 使用IOC订单类型避免挂单

        Returns:
            bool: True=成功提交止损订单
        """
        self._owner.close(
            priceMatch="OPPONENT_5",  # 对手价+5档，确保快速成交
            timeInForce="IOC",  # 立即成交或取消
            trigger=self.get_name(),  # 触发器名称
        )
        return True
