import datetime

from ..datafeeds import OneWSRedisDataFeed
from ..datafeed import ResampleDataFeed
from ..context import DBTaskStatusContext
from ..mysql import DBManager
from ..redismanager import (
    MultiKLineRedisManager,
    DynParamRedisManager,
    PlatIndRedisManager,
    RiskRedisManager,
    RunControlRedisManager,
    MultiOpenIntRedisManager,
)
from ..sizers import NoneSize
from ..ws import MultiKLineWSManager, BinanceBrokerWSManager
from ..api import BinanceBrokerAPIManager
from ..storages import IndCsvManager
import backtrader as bt


def simulate(symbols, strategy, days_ago=None, start_date=None, end_date=None):
    # 手动配置mysql数据库
    taskmanager = DBManager(
        uri="mysql+pymysql://fhj-trade:YcjfdEmZPj6KyrRj@*************/fhj-trade"
    )

    # 手动配置数据源redis
    klinelivemanager = MultiKLineRedisManager(
        host="*************",
        port=6379,
        db=13,
        password="fhj666888",
    )

    # 手动配置持仓量redis
    openintmanager = MultiOpenIntRedisManager(
        host="*************",
        port=6379,
        db=13,
        password="fhj666888",
    )

    # 手动配置历史数据websocket
    wsmanager = MultiKLineWSManager(
        uri="ws://apiws.karlet.com/market",
    )

    # 手动配置币安账户api
    brokerapimanager = BinanceBrokerAPIManager(
        uri="http://api.karlet.com",
    )

    # 手动配置币安账户websocket
    brokermanager = BinanceBrokerWSManager(uri="ws://ws.karlet.com:80/ws")

    # 手动配置动态参数redis
    dynparammanager = DynParamRedisManager(
        host="127.0.0.1",
        port=6379,
        db=12,
        password="fhj666888",
    )

    # 手动配置运行控制redis
    controlmanager = RunControlRedisManager(
        host="127.0.0.1",
        port=6379,
        db=12,
        password="fhj666888",
    )

    # 手动配置指标csv存储
    indcsvmanager = IndCsvManager(filedir="/Users/<USER>/mnt/DataA")

    # 手动配置平台指标
    platindmanager = PlatIndRedisManager(
        host="*************",
        port=6379,
        db=13,
        password="fhj666888",
    )

    # 手动配置风控
    riskmanager = RiskRedisManager(
        host="127.0.0.1",
        port=6379,
        db=13,
        password="fhj666888",
    )
    results = []  # 用于存储每次回测的结果
    stats = {}  # 用于存储每个币种的统计信息
    start_time = datetime.datetime.now()
    for symbol in symbols:
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        print(f"开始执行任务{symbol},当前时间: {current_time}")
        with (
            taskmanager,
            klinelivemanager,
            openintmanager,
            wsmanager,
            brokermanager,
            brokerapimanager,
            dynparammanager,
            indcsvmanager,
            riskmanager,
            platindmanager,
            controlmanager,
        ):
            try:
                ## 解析参数
                # 任务id
                task_id = 1
                # 获取策略默认参数
                context_params = strategy.get_default_data_params(symbol=symbol)

                # 对默认参数做修改
                for d in context_params:
                    # 是否采用实时数据
                    d["is_live"] = False
                    # 历史数据时间长度
                    if days_ago:
                        d["days_ago"] = days_ago
                        d["start_date"] = None
                        d["end_date"] = None
                    else:
                        d["days_ago"] = None
                        d["start_date"] = start_date
                        d["end_date"] = end_date
                stra_params = {
                    "simulate": simulate,
                }

                from bt2 import Cerebro2
                from bt2.brokers import BackBroker2, BinanceLiveBroker

                # 创建cerebro
                cerebro = Cerebro2(
                    cheat_on_open=True, stdstats=True, exactbars=0, preload=False
                )
                # 添加交易分析器
                cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name="trades")

                # 创建代理人
                # 模拟broker
                broker = BackBroker2(cashbalance=1000)
                cerebro.setbroker(broker=broker)

                # 创建数据源对象
                for data_params in context_params:
                    dataname = data_params.pop("dataname")
                    resampledata = data_params.pop("resampledata", None)
                    if resampledata:
                        data = ResampleDataFeed(
                            origin_data=cerebro.datasbyname[resampledata],
                            dataname=dataname,
                            **data_params,
                        )
                    else:
                        data = OneWSRedisDataFeed(dataname=dataname, **data_params)
                    cerebro.adddata(data)

                # 增加策略
                cerebro.addstrategy(strategy=strategy, **stra_params)

                # 设置仓位控制
                cerebro.addsizer(NoneSize)

                # 运行
                with DBTaskStatusContext(task_id=task_id, conn_db=False):
                    res = cerebro.run()

                # 将结果添加到结果列表中
                results.append((res[0], cerebro))

                # 统计信息
                trades = res[0].analyzers.trades.get_analysis()
                losing_trades, total_pnl, total_trades, win_rate, winning_trades = (
                    statsdata(trades)
                )

                # 存储统计信息
                stats[symbol] = {
                    "total_trades": total_trades,
                    "winning_trades": winning_trades,
                    "losing_trades": losing_trades,
                    "win_rate": win_rate,
                    "total_pnl": total_pnl,
                }

            except Exception as e:
                print(f"任务异常{symbol},但继续执行，异常{e}")
                # raise e
            finally:
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[
                    :-3
                ]
                print(f"结束执行任务{symbol},当前时间: {current_time}")
    end_time = datetime.datetime.now()
    print(f"整体回测耗时: {(end_time - start_time).total_seconds()}秒")
    return results, stats  # 返回结果列表和统计信息


def statsdata(trades):
    # 防御式数据提取
    pnl_net = trades.get("pnl", {})
    if isinstance(pnl_net, dict):
        pnl_value = pnl_net.get("net", {}).get("total", 0.0)
    else:
        pnl_value = getattr(getattr(pnl_net, "net", object), "total", 0.0)
    stats_data = {
        "total": int(trades.get("total", {}).get("total", 0)),
        "won": int(trades.get("won", {}).get("total", 0)),
        "lost": int(trades.get("lost", {}).get("total", 0)),
        "pnl": float(pnl_value),
    }
    # 计算结果
    total_trades = int(stats_data["total"])
    winning_trades = int(stats_data["won"])
    losing_trades = stats_data["lost"]
    total_pnl = stats_data["pnl"]
    # 空交易处理
    if total_trades == 0:
        win_rate = 0.0
    else:
        win_rate = winning_trades / total_trades
    return losing_trades, total_pnl, total_trades, win_rate, winning_trades
