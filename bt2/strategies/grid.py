from .live import LiveEntryExitStrategy
from ..conditions.grid import *
from ..conditions.longshort import *
from logging import getLogger

logger = getLogger(__name__)


class SimpleGridStrategy(LiveEntryExitStrategy):
    """
    网格策略
    """

    alias = ("SimpleGridStrategy",)

    params = (
        ("value_ratio", 0.5),
        ("upper_price", 0),
        ("lower_price", 0),
        ("num", 10),
        ("trigger_price", None),
    )

    @classmethod
    def get_default_data_params(cls, symbol):
        params = [
            {
                "dataname": f"{symbol}_1m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "1m",
                "symbol": symbol,
                "days_ago": 2,
            },
        ]
        return params

    @classmethod
    def get_default_account_name(cls):
        """默认账户"""
        return "TEST-liquid"

    def init(self):
        # 进入条件
        self.add_entry_cond(NoEntryCond)

        # 退出条件
        # self.add_exit_cond(StopLossExitCond)

        # 平仓条件
        self.add_close_cond(StopProfitQueueCond)
        self.add_close_cond(StopLossMarketCond)

        # 追单条件
        self.add_append_cond(
            GridCondition,
            value_ratio=self.p.value_ratio,
            lower_price=self.p.lower_price,
            upper_price=self.p.upper_price,
            num=self.p.num,
            trigger_price=self.p.trigger_price,
        )

    def heartbeat(self):
        logger.info("dt: %s", bt.num2date(self.data.datetime[0]))

    def start(self):
        # 策略初始化, 撤销所有订单, 平掉所有仓位
        # self.logger.info('策略仓位初始化, 取消所有订单, 平掉所有仓位')
        # self.cancel_all_orders()
        # self.close_all_positions()
        self.logger.info("策略仓位初始化完成")

    def stop(self):
        # 策略停止, 撤销所有订单, 平掉所有仓位
        # self.logger.info('策略停止, 取消所有订单, 平掉所有仓位')
        # self.cancel_all_orders()
        # self.close_all_positions()
        self.logger.info("策略停止完成")

    def notify_order(self, order):
        self.logger.info("order: %s", order)


class MovableGridStrategy(LiveEntryExitStrategy):
    """
    移动网格策略
    """

    alias = ("MovableGridStrategy",)

    params = (
        ("used_margin", None),
        ("order_value", 100),
        ("value_base", 10),
        ("value_ratio", 0.5),
        ("upper_price", 0),
        ("lower_price", 0),
        ("trigger_price", None),
        ("mid_price", 0),
        ("num", 10),
        ("order_num", 10),
        ("movable", False),
        ("stop_to_close", False),
    )

    @classmethod
    def get_default_data_params(cls, symbol):
        params = [
            {
                "dataname": f"{symbol}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": symbol,
                "days_ago": 2,
            },
        ]
        return params

    @classmethod
    def get_default_account_name(cls):
        """默认账户"""
        return "TEST-liquid"

    def init(self):
        # 进入条件
        self.add_entry_cond(NoEntryCond)

        # 追单条件
        gcond = self.add_append_cond(
            MovableGridCondition,
            used_margin=self.p.used_margin,
            order_value=self.p.order_value,
            value_ratio=self.p.value_ratio,
            upper_price=self.p.upper_price,
            lower_price=self.p.lower_price,
            trigger_price=self.p.trigger_price,
            mid_price=self.p.mid_price,
            num=self.p.num,
            order_num=self.p.order_num,
            movable=self.p.movable,
        )

        # 停止条件
        self.add_stop_cond(
            MovableGridStopCond,
            gcond,
        )

    def heartbeat(self):
        logger.info("dt: %s", bt.num2date(self.data.datetime[0]))

    def start(self):
        self.logger.info("策略仓位初始化完成")

    def stop(self):
        ## 撤销现有订单
        self.logger.info("撤销所有订单")
        self.cancel_all_orders()
        if self.p.stop_to_close:
            self.logger.info("策略结束并平仓")
            self.close_all_positions()
        self.logger.info("策略停止完成")

    def notify_order(self, order):
        self.logger.info("order: %s", order)


class MovableGridStrategy2(MovableGridStrategy):
    """多个账号网格策略2"""

    alias = ("MovableGridStrategy2",)


class MovableGridStrategy3(MovableGridStrategy):
    """多个账号网格策略3"""

    alias = ("MovableGridStrategy3",)


class MovableGridStrategy4(MovableGridStrategy):
    """多个账号网格策略4"""

    alias = ("MovableGridStrategy4",)


class MovableGridStrategy5(MovableGridStrategy):
    """多个账号网格策略5"""

    alias = ("MovableGridStrategy5",)


class MovableGridStrategy6(MovableGridStrategy):
    """多个账号网格策略6"""

    alias = ("MovableGridStrategy6",)


class MovableGridStrategy7(MovableGridStrategy):
    """多个账号网格策略7"""

    alias = ("MovableGridStrategy7",)


class MovableGridStrategy8(MovableGridStrategy):
    """多个账号网格策略8"""

    alias = ("MovableGridStrategy8",)
