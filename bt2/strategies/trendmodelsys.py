from ..strategies.live import LiveEntryExitStrategy
from ..conditions import *
import backtrader as bt


class TrendCond(AppendCond):
    """
    trendmodel策略进出条件
    """

    plotinfo = {
        "b_plot": True,
    }

    def __init__(self):
        self.macd = bt.indicators.MACD(period_me1=12, period_me2=26, period_signal=9)
        self.macd.plotinfo.b_plot = True
        self.macd.plotinfo.b_plotname = "macd"
        self.atr = bt.indicators.AverageTrueRange(period=14)

        self.golden_cross = Switch(self.macd.macd > 0, b_plot=True)
        self.death_cross = Switch(self.macd.macd < 0, b_plot=True)

        self.golden_high = Filter(self.golden_cross, self.data.high, b_plot=True)
        self.death_low = Filter(self.death_cross, self.data.low, b_plot=True)

        self.lastn_high = LastNHigh(self.golden_high, n=7, b_plot=True)
        self.lastn_low = LastNLow(self.death_low, n=7, b_plot=True)

        self.break_long = self.lastn_high + 0.5 * self.atr
        self.break_long.plotinfo.b_plot = True
        self.break_short = self.lastn_low - 0.5 * self.atr
        self.break_short.plotinfo.b_plot = True

        self.entry_long = self.data.close > self.break_long
        self.entry_short = self.data.close > self.break_short

        self.exit_long = self.data.close < bt.indicators.Lowest(self.data.low, period=7)
        self.exit_short = self.data.close > bt.indicators.Highest(
            self.data.high, period=7
        )

    def next(self):
        if self.broker.no_open_position():
            if self.entry_long[0]:
                self._owner.buy(side=self.LONG, size=50)
                return True
            if self.entry_short[0]:
                self._owner.sell(side=self.SHORT, size=50)
                return True
        if self.broker.get_size() > 0:
            if self.exit_long[0]:
                self._owner.close()
                return True
        if self.broker.get_size() < 0:
            if self.exit_long[0]:
                self._owner.close()
                return True


class TrendModelSysStrategy(LiveEntryExitStrategy):

    @classmethod
    def get_default_data_params(cls, symbol):
        params = [
            {
                "dataname": f"{symbol}_1m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "1m",
                "symbol": symbol,
                "days_ago": 7,
                "is_live": True,
            },
        ]
        return params

    def init(self):
        # 进入条件
        self.add_entry_cond(NoEntryCond)
        # 追单条件
        self.add_append_cond(
            TrendCond,
        )
