from ..live import ShortLiveStrategy
from ...conditions.longshort import *
from ...conditions.short import *
from logging import getLogger

logger = getLogger(__name__)


class ManualInAutoOutShortSlowStrategy(ShortLiveStrategy):
    """
    人工智能
    文档参考: https://ji9xzzjt0z.feishu.cn/docx/LhG9d1CProdophx6dlwcrNhqnpD
    """

    alias = ("ManualInAutoOutShortSlowStrategy",)

    @classmethod
    def get_default_data_params(cls, symbol):
        params = [
            {
                "dataname": f"{symbol}_1m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "1m",
                "symbol": symbol,
                "days_ago": 2,
            },
            {
                "dataname": f"{symbol}_1h",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "1h",
                "symbol": symbol,
                "days_ago": 2,
            },
        ]
        return params

    @classmethod
    def get_default_account_name(cls):
        """默认账户"""
        return "TEST-lucy-bro"

    def init(self):
        self._entrys = []
        # 进入条件
        self.add_entry_cond(ManualFollowEntryCond)

        # 退出条件
        """
        1硬止损
        2移动止盈
        3保本
        4吊灯
        5放量冲高
        6大振幅定K
        7Sar反转
        8刹车
        9主动ATR
        """
        self.add_exit_cond(StopLossExitCond, loss_rate=0.05)  # 硬止损
        self.add_exit_cond(TrailingStopLiveExitCond, stop_profit_rate=0.6)  # 移动止盈
        # self.add_exit_cond(BreakevenLiveExitCond, stop_rate=0.002)  # 保本
        # self.add_exit_cond(ChandelierExitCond, self.datas[1])  # 吊灯
        self.add_exit_cond(VolumeSurgeExitCond, self.datas[1])  # 放量
        self.add_exit_cond(
            BenchMarkBarPriceUpExitCond, self.datas[1], benchmark_thres=5
        )  # 大振幅
        # self.add_exit_cond(SARCrossingBelowExitCond, self.datas[1])  # Sar反转
        self.add_exit_cond(BrakeExitCond)  # 刹车
        self.add_exit_cond(
            ATRStopProfitExitCond, self.datas[1], atr_period=12
        )  # 主动ATR
        self.add_exit_cond(ManualPriceCrossLagMAExitCond, self.datas[1])  # 价格穿越退出

        # 开仓条件
        self.add_open_cond(NoOpenCond)
        # 平仓条件
        self.add_close_cond(StopProfitQueueCond)
        self.add_close_cond(StopLossMarketCond)

    def heartbeat(self):
        logger.info("dt: %s", bt.num2date(self.data.datetime[0]))

    def preheartbeat(self):
        logger.info("preheatbeat dt: %s", bt.num2date(self.data.datetime[0]))

    def start(self):
        # 策略初始化, 撤销所有订单, 平掉所有仓位
        # self.logger.info('策略仓位初始化, 取消所有订单, 平掉所有仓位')
        # self.cancel_all_orders()
        # self.close_all_positions()
        self.logger.info("MIAO_short策略仓位初始化完成")

    def stop(self):
        # 策略停止, 撤销所有订单, 平掉所有仓位
        # self.logger.info('策略停止, 取消所有订单, 平掉所有仓位')
        # self.cancel_all_orders()
        # self.close_all_positions()
        self.logger.info("策略停止完成")

    def notify_order(self, order):
        self.logger.info("order: %s", order)
