from ...conditions import PriceBelowEntryCond, MAAscendingOrderExitCond
from ...conditions.entry import MACrossShortEntryCond
from ...strategies.live import ShortLiveStratgegy
from ...conditions.longshort import *
from logging import getLogger

logger = getLogger(__name__)


class SmallShortStrategy(ShortLiveStratgegy):
    """
    IDG-顺势做空策略
    """

    alias = ('SmallShortStrategy',)

    params = (
        ('base_value', 800),
        ('step', 2000),
        ('open_value_rate', 50),
        ('price_t', 5),
        ('turnover_t', 150),
        ('vol_oi_t', 10),
    )

    @classmethod
    def get_default_data_params(cls, symbol):
        if symbol != 'btcusdt':
            symbol_cmp = 'btcusdt'
        else:
            symbol_cmp = 'ethusdt'
        params = [
            {
                "dataname": f"{symbol}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": symbol,
                "days_ago": 3,
            },
            {
                "dataname": f"{symbol_cmp}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": symbol_cmp,
                "days_ago": 3,
            },
            {
                "dataname": "btcdomusdt_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": "btcdomusdt",
                "days_ago": 3,
            },
        ]
        return params

    def init(self):
        # 进入条件
        self.add_entry_cond(MACrossShortEntryCond, self.datas[0], ma_periods=(5, 20, 60))  # 均线向下发散趋势
        self.add_entry_cond(PriceSoftEntryCond, period=10, rsi_low_thres=30, rsi_high_thres=50)  # 温和的价格变化
        self.add_entry_cond(NoBrakeEntryCond)  # 规避指数上行
        self.add_entry_cond(PriceBelowEntryCond)
        self.add_entry_cond(PosValueBelowEntryCond)

        # 增加开仓条件
        self.add_open_cond(CmpStepOverPriceOpenCond, self.datas[0], self.datas[1], self.datas[2],
                           price_over_ratio=0.001, base_value=self.p.base_value, step=self.p.step)

        # 增加退出条件
        self.add_exit_cond(StopLossExitCond, loss_rate=0.02)  # 设置止损
        self.add_exit_cond(StepTrailingStopExitCond, trigger_rates=(0.01, 0.02, 0.5), stop_rates=(0.6, 0.6, 0.5))
        self.add_exit_cond(ATRStopProfitExitCond, atr_period=96)
        self.add_exit_cond(MAAscendingOrderExitCond, ma_periods=(5, 20))

        # 增加平仓条件
        self.add_close_cond(StopProfitQueueCond)
        self.add_close_cond(StopLossMarketCond)

        # 追单条件
        self.add_append_cond(CancelTimeOutAppendCond)

    def heartbeat(self):
        logger.info('dt: %s, openint: %s', bt.num2date(self.data.datetime[0]), self.data.openint[0])

    def start(self):
        # 策略初始化, 撤销所有订单, 平掉所有仓位
        self.logger.info('策略仓位初始化, 取消所有订单')
        self.cancel_all_orders()
        self.logger.info('策略仓位初始化完成')

    def stop(self):
        # 策略停止, 撤销所有订单, 平掉所有仓位
        self.logger.info('策略停止, 取消所有订单')
        self.cancel_all_orders()
        self.logger.info('策略停止完成')

    def notify_order(self, order):
        self.logger.info('order: %s', order)

