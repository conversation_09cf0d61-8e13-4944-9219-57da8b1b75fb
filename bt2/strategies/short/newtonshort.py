from ..live import ShortLiveStratgegy
from ...conditions.longshort import *
from ...conditions.short import *
from logging import getLogger

logger = getLogger(__name__)


class NewtonShortStrategy(ShortLiveStratgegy):
    """
    动量做空
    文档参考: https://ji9xzzjt0z.feishu.cn/docx/TXBxdnHYFoq73NxsGXNcedfOn1b
    """

    alias = ("NewtonShortStrategy",)

    @classmethod
    def get_default_data_params(cls, symbol):
        params = [
            {
                "dataname": f"{symbol}_1m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "1m",
                "symbol": symbol,
                "days_ago": 1,
            },
        ]
        return params

    @classmethod
    def get_default_account_name(cls):
        """默认账户"""
        return "TEST-tom-bro"

    def init(self):
        # 进入条件
        period = 5
        self.add_entry_cond(VelocityEntryCond, period=period)
        # self.add_entry_cond(AccelerationEntryCond, period=period)

        # 开仓条件
        open_cond = self.add_open_cond(
            BalanceOverPriceOpenCond, symbol_count=60, value_ratio=1
        )

        # 退出条件
        # 主动止盈退出一半
        self.add_exit_cond(
            MakerProfitExitCond, pos_rate=1, stop_rate1=0.0035, stop_rate2=0.007
        )
        # 超出前bar极值退出
        self.add_exit_cond(LastBarExtremumExitCond, self.data, open_cond)
        # 其他退出条件退出所有
        self.add_exit_cond(BreakevenExitCond, lower_limit=0.0035, stop_rate=0.001)
        self.add_exit_cond(BreakevenExitCond2, lower_limit=0.007, stop_rate=0.0035)
        self.add_exit_cond(StopLossExitCond, loss_rate=0.0025)

        # 平仓条件
        self.add_close_cond(StopProfitQueueCond)
        self.add_close_cond(StopLossMarketCond2)

    def heartbeat(self):
        logger.info("dt: %s", bt.num2date(self.data.datetime[0]))

    def start(self):
        # 策略初始化, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略仓位初始化, 取消所有订单, 平掉所有仓位")
        self.cancel_all_orders()
        self.close_all_positions()
        self.logger.info("策略仓位初始化完成")

    def stop(self):
        # 策略停止, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略停止, 取消所有订单, 平掉所有仓位")
        self.cancel_all_orders()
        self.close_all_positions()
        self.logger.info("策略停止完成")

    def notify_order(self, order):
        self.logger.info("order: %s", order)
