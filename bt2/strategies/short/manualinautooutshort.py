from ..live import ShortLiveStrategy
from ...conditions.longshort import *
from ...conditions.short import *
from logging import getLogger

logger = getLogger(__name__)


class ManualInAutoOutShortStrategy(ShortLiveStrategy):
    """
    人工智能
    文档参考: https://ji9xzzjt0z.feishu.cn/docx/UHXmdMOqeo4ZEkxCp6DcT6fvnLb
    """

    alias = ("ManualInAutoOutShortStrategy",)

    @classmethod
    def get_default_data_params(cls, symbol):
        params = [
            {
                "dataname": f"{symbol}_1m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "1m",
                "symbol": symbol,
                "days_ago": 2,
            },
        ]
        return params

    @classmethod
    def get_default_account_name(cls):
        """默认账户"""
        return "TEST-lucy"

    def init(self):
        self._entrys = []
        # 进入条件
        self.add_entry_cond(ManualFollowEntryCond)

        # 退出条件
        self.add_exit_cond(StopLossExitCond)
        self.add_exit_cond(TrailingStopLiveExitCond, stop_profit_rate=0.75)
        self.add_exit_cond(BreakevenLiveExitCond)
        # self.add_exit_cond(VolumeSurgeExitCond)
        self.add_exit_cond(BenchMarkBarPriceUpExitCond)
        self.add_exit_cond(BrakeExitCond)

        # 开仓条件
        self.add_open_cond(NoOpenCond)
        # 平仓条件
        self.add_close_cond(StopProfitQueueCond)
        self.add_close_cond(StopLossMarketCond)

    def heartbeat(self):
        logger.info("dt: %s", bt.num2date(self.data.datetime[0]))

    def preheartbeat(self):
        logger.info("preheatbeat dt: %s", bt.num2date(self.data.datetime[0]))

    def start(self):
        # 策略初始化, 撤销所有订单, 平掉所有仓位
        # self.logger.info('策略仓位初始化, 取消所有订单, 平掉所有仓位')
        # self.cancel_all_orders()
        # self.close_all_positions()
        self.logger.info("MIAO_short策略仓位初始化完成")

    def stop(self):
        # 策略停止, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略停止, 取消所有订单, 平掉所有仓位")
        self.cancel_all_orders()
        self.close_all_positions()
        self.logger.info("策略停止完成")

    def notify_order(self, order):
        self.logger.info("order: %s", order)
