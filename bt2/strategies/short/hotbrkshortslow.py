from ..live import ShortLiveStratgegy
from ...conditions.longshort import *
from ...conditions.short import *
from logging import getLogger

logger = getLogger(__name__)


class HotBrkShortSlowStrategy(ShortLiveStratgegy):
    """
    热点爆点做空-可变周期版
    """

    alias = ("HotBrkShortSlowStrategy",)

    params = (("no_pos_entry", False),)

    @classmethod
    def get_default_data_params(cls, symbol):
        params = [
            {
                "dataname": f"{symbol}_15m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "15m",
                "symbol": symbol,
                "days_ago": 7,
            },
            {
                "dataname": f"{symbol}_4h",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "4h",
                "symbol": symbol,
                "days_ago": 7,
            },
        ]
        return params

    @classmethod
    def get_default_account_name(cls):
        """默认账户"""
        return "TEST-jiaoyin-bro"

    def init(self):
        # 进入条件
        self.add_entry_cond(
            MADescendingOrderEntryCond, self.datas[1], ma_periods=(5, 15, 25)
        )
        self.add_entry_cond(ADXRDecreaseEntryCond, self.datas[1])
        self.add_entry_cond(ADXNegativeEntryCond, self.datas[1])
        self.add_entry_cond(NoBrakeEntryCond)
        self.add_entry_cond(PriceBelowEntryCond)
        self.add_entry_cond(NewLowCountEntryCond)
        self.add_entry_cond(PriceDownBreakEntryCond)
        self.add_entry_cond(Near10DayLowEntryCond)
        self.add_entry_cond(PosValueBelowEntryCond)
        self.add_entry_cond(BarsOpenOnceEntryCond, bar_num=96)

        # 退出条件
        self.add_exit_cond(StopLossExitCond, loss_rate=0.05)
        self.add_exit_cond(StepTrailingStopExitCond)
        self.add_exit_cond(BenchMarkBarPriceUpExitCond, benchmark_thres=5)
        self.add_exit_cond(PriceCrossLagMAExitCond)
        # self.add_exit_cond(SARCrossingAboveExitCond)
        self.add_exit_cond(ATRStopProfitExitCond, atr_period=96)
        self.add_exit_cond(MAAscendingOrderExitCond, self.datas[1])
        self.add_exit_cond(BrakeExitCond)

        # 开仓条件
        self.add_open_cond(StepOverPriceOpenCond, price_over_ratio=None)

        # 平仓条件
        self.add_close_cond(StopProfitQueueCond)
        self.add_close_cond(StopLossMarketCond)

        # 追单条件
        self.add_append_cond(CancelTimeOutAppendCond)

        # 停止条件
        self.add_stop_cond(LosingStreakStopCond)

    def heartbeat(self):
        logger.info("dt: %s", bt.num2date(self.data.datetime[0]))

    def start(self):
        # 策略初始化, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略仓位初始化, 取消所有订单, 平掉所有仓位")
        self.cancel_all_orders()
        self.close_all_positions()
        self.logger.info("策略仓位初始化完成")

    def stop(self):
        # 策略停止, 撤销所有订单, 平掉所有仓位
        self.logger.info("策略停止, 取消所有订单, 平掉所有仓位")
        self.cancel_all_orders()
        self.close_all_positions()
        self.logger.info("策略停止完成")

    def notify_order(self, order):
        self.logger.info("order: %s", order)
