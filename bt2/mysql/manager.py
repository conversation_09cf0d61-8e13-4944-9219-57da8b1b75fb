from functools import wraps
import time
import threading
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.exc import DisconnectionError, TimeoutError
from ..manager import ManagerBase
from logging import getLogger

logger = getLogger(__name__)


class DBManager(ManagerBase):
    """
    任务信息数据库会话管理器, 用于管理数据库会话
    多个线程创建的会话保存在各自的线程变量中, 一个会话对应一个线程
    Args:
        db_url: str, 连接数据库的url
    Methods:
        get_session -> Session, 获取当前会话
    """

    ConfigKey = "TASK_DB"

    params = {"uri": None}

    thread_local = threading.local()  # 线程变量
    scoped_session = None

    @classmethod
    def initcls(cls):
        engine = create_engine(
            cls.p.uri,
            pool_size=10,  # 连接池大小
            max_overflow=20,  # 允许连接池溢出的连接数
            pool_pre_ping=True,  # 在每次连接使用前检查连接是否有效
            pool_recycle=3600,  # 每隔3600秒（1小时）回收连接，防止MySQL超时断开
        )
        factory = sessionmaker(bind=engine, expire_on_commit=False)
        cls.scoped_session = scoped_session(factory)
        cls.thread_local.depth = 0

    def __enter__(self):
        if self.thread_local.depth == 0:
            # 新事务开始时无需手动new_session，scoped_session会自动提供线程会话
            pass
        self.thread_local.depth += 1
        return self.get_session()

    def __exit__(self, exc_type, exc_value, traceback):
        self.thread_local.depth -= 1
        if self.thread_local.depth == 0:
            session = self.get_session()
            try:
                if exc_type is None:
                    session.commit()
                else:
                    session.rollback()
            finally:
                session.close()
                self.scoped_session.remove()  # 移除当前线程Session绑定，确保下次是新的Session

    @classmethod
    def get_session(cls):
        # 获取当前线程的会话
        return cls.scoped_session()

    @classmethod
    def reset_session(cls):
        # 先remove当前线程的Session，确保下一次调用get_session是新session
        cls.scoped_session.remove()
        # 调用后，scoped_session() 会返回新的 Session 对象
        return cls.scoped_session()


def session_decorator(func):
    """自动填写session参数装饰器, 默认导入DBManager中的session"""

    @wraps(func)
    def session_wrapper(*args, **kwargs):
        exc = None
        for attempt in range(10):
            try:
                session = DBManager.get_session()
                result = func(*args, **kwargs, session=session)
                session.commit()
                return result
            except (DisconnectionError, TimeoutError) as e:
                # 连接异常
                session.rollback()
                time.sleep(1)
                exc = e
                logger.warning(
                    "mysql Exception: %s, try %s reconnect mysql server", e, attempt + 1
                )
                DBManager.reset_session()
            except Exception as e:
                # 其他异常
                session.rollback()
                logger.exception(e)
                raise e
        logger.exception(exc)
        raise exc

    return session_wrapper


def create_all_tables():
    """自动创建所有表"""
    from .mysql.models import Base

    Base.metadata.create_all(DBManager.engine)
