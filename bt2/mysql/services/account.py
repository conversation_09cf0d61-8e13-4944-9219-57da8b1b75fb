## 服务层

from ..dao import AccountDao
from ..models import Account
from logging import getLogger

logger = getLogger(__name__)


class AccountService:
    """
    账户服务
    """

    @classmethod
    def create_account(cls, name, platform, apikey, apisecret, apipwd):
        """创建账户"""
        account = Account(
            nickname=name,
            platform=platform,
            api_key=apikey,
            api_secret=apisecret,
            api_pwd=apipwd,
        )
        AccountDao.insert_one(account)
        return account.id

    @classmethod
    def query_by_name(cls, name):
        """根据账户名查询账户"""
        account = AccountDao.select_one({"nickname": name})
        if not account:
            raise KeyError(f"no account {name} exists")
        return account

    @classmethod
    def query_name_by_id(cls, account_id):
        """
        根据id查询账户
        """
        account = AccountDao.select_one({"id": account_id})
        if not account:
            raise KeyError(f"no account {account_id} exists")
        return account.nickname
