## 服务层

from ..dao import ServerDao, ServerConfigDao
from ..models import Server, ServerConfig


class ServerConfigService:
    """
    服务器配置
    """

    @classmethod
    def add_config(cls, name, params={}):
        config = ServerConfig(name=name, params=params)
        ServerConfigDao.insert_one(config)

    @classmethod
    def query_by_name(cls, name):
        config = ServerConfigDao.select_one({"name": name})
        if not config:
            raise KeyError(f"no config {name} exists")
        return config


class ServerService:
    """服务器相关操作"""

    @classmethod
    def add_or_update_server(cls, ip="127.0.0.1", config_name="default"):
        """
        添加或更新一台服务器
        Args:
            ip (str): 服务器ip
            config_name (str): 配置名称
        """
        config = ServerConfigService.query_by_name(config_name)
        server = Server(server_config_id=config.id, ip=ip)
        ServerDao.insert_or_update_server(server)

    @classmethod
    def query_by_name(cls, name):
        server = ServerDao.select_one({"name": name})
        if not server:
            raise KeyError(f"no server {name} exists")
        return server

    @classmethod
    def add_or_update_server_config(cls, config_name="default", config: dict = {}):
        """
        增加或更新一个服务器配置
        Args:
            config_name (str): 配置名称
            config (dict): 配置参数
        """
        config = ServerConfig(name=config_name, params=config)
        ServerConfigDao.insert_or_update_config(config)

    @classmethod
    def query_by_ip(cls, ip):
        """
        通过ip查找服务器
        """
        server = ServerDao.get_server_by_ip(ip)
        if not server:
            raise KeyError(f"no server {ip} exists")
        return server

    @classmethod
    def get_servers_up(cls):
        """
        获取在线的服务器对象
        """
        return ServerDao.select_many({"state": "up"})
