## 订单

from ..models import TaskOrder
from ..dao import TaskOrderDao
from datetime import datetime


class TaskOrderService:
    """订单相关操作"""

    @classmethod
    def add_orders(cls, order_param_list: list):
        """
        添加多个订单
        Args:
            order_param_list (list): 订单信息列表
        """
        ordermodels = []
        for order_params in order_param_list:
            ordermodels.append(TaskOrder(**order_params))
        TaskOrderDao.insert_many(ordermodels)

    @classmethod
    def drop_task_orders(cls, task_id):
        """删除某个任务下的所有订单信息"""
        TaskOrderDao.delete_many_by_query({"task_id": task_id})

    @classmethod
    def update_order_by_num(cls, order_num, order_params):
        """根据订单编号并更新订单"""
        task = TaskOrderDao.select_one({"order_num": order_num})
        if not order_params:
            # 测试, 将参数按照固定方式修改
            from random import randint

            task.trade_price = randint(1000, 2000)
            task.trade_quantity = task.order_quantity
            task.trade_ts = int(datetime.now().timestamp() * 1000)
            task.state = "success"
        TaskOrderDao.update_one(task)

    @classmethod
    def get_orders_trade_status(cls, order_nums: list):
        """
        查询多个订单编号对应的交易状态
        """
        order_status_dict = {}
        for order_num in order_nums:
            order = TaskOrderDao.select_one({"order_num": order_num})
            order_status_dict[order_num] = {
                "trade_quantity": order.trade_quantity,
                "trade_price": order.trade_price,
                "trade_ts": order.trade_ts,
                "state": order.state,
            }
        return order_status_dict
