## 服务层

from ..dao import StrategyDao, StrategyParamsDao
from ..models import Strategy, StrategyParams
from logging import getLogger

logger = getLogger(__name__)


class StrategyService:
    """
    单个指标存储策略服务
    """

    @classmethod
    def register(cls, strategy):
        """登记一个策略"""
        strategy = Strategy(
            name=strategy.get_name(), default_params=strategy.get_default_params()
        )
        StrategyDao.insert_or_update_one(strategy, "name")

    @classmethod
    def query_by_name(cls, name):
        strategy = StrategyDao.select_one({"name": name})
        if not strategy:
            raise KeyError(f"no strategy {name} exists")
        return strategy

    @classmethod
    def query_account_id_by_name(cls, name):
        """
        查询账户
        """
        strategy = StrategyDao.select_one({"name": name})
        account_id = strategy.account_id
        return account_id


class StrategyParamService:
    """
    策略参数
    """

    @classmethod
    def add_params(cls, name, params={}):
        params = StrategyParams(name=name, params=params)
        StrategyParamsDao.insert_one(params, "name")

    @classmethod
    def query_by_name(cls, name):
        params = StrategyParamsDao.select_one({"name": name})
        if not params:
            raise KeyError(f"no params {name} exists")
        return params
