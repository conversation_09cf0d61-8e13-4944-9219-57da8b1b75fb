from typing import Optional, List
from dateutil.parser import parse
from ..dao import TaskDao
from ..models import Task
from logging import getLogger
from .account import AccountService
from .strategy import StrategyService, StrategyParamService
from .server import ServerService


logger = getLogger(__name__)


class TaskService:
    """
    单个任务服务
    """

    @classmethod
    def create_task(
        cls,
        task_name,
        strategy_name,
        account_name=None,
        server_name=None,
        params_name=None,
        context: dict = {},
        params: dict = {},
    ):
        """
        创建任务
        任务创建成功时返回任务对象, 创建失败时返回False
        """
        strategy = StrategyService.query_by_name(name=strategy_name)
        account_id = (
            AccountService.query_by_name(name=account_name).id
            if account_name is not None
            else strategy.account_id
        )
        experts_extern_id = (
            StrategyParamService.query_by_name(name=params_name)
            if params_name is not None
            else None
        )
        server_id = (
            ServerService.query_by_name(name=server_name)
            if server_name is not None
            else None
        )
        task = Task(
            task_name=task_name,
            account_id=account_id,
            experts_id=strategy.id,
            experts_extern_id=experts_extern_id,
            server_id=server_id,
            params=params,
            context=context,
        )
        TaskDao.insert_one(task)
        return task

    @classmethod
    def create_or_get_task(
        cls,
        task_name,
        strategy_name,
        account_name=None,
        server_name=None,
        params_name=None,
        context: dict = {},
        params: dict = {},
    ):
        """
        创建任务
        任务创建成功时返回任务对象, 创建失败时返回已存在的任务对象
        """
        strategy = StrategyService.query_by_name(name=strategy_name)
        account_id = (
            AccountService.query_by_name(name=account_name).id
            if account_name is not None
            else strategy.account_id
        )
        experts_extern_id = (
            StrategyParamService.query_by_name(name=params_name)
            if params_name is not None
            else None
        )
        server_id = (
            ServerService.query_by_name(name=server_name)
            if server_name is not None
            else None
        )
        task = Task(
            task_name=task_name,
            account_id=account_id,
            experts_id=strategy.id,
            experts_extern_id=experts_extern_id,
            server_id=server_id,
            params=params,
            context=context,
        )
        return TaskDao.insert_or_get_task(task)

    @classmethod
    def create_or_update_task(
        cls,
        task_name,
        strategy_name,
        account_name=None,
        params_name=None,
        context: dict = {},
        params: dict = {},
    ):
        """
        创建任务
        任务创建成功时返回任务对象, 创建失败时返回已存在的任务对象, 并且更新对象
        """
        strategy = StrategyService.query_by_name(name=strategy_name)
        account_id = (
            AccountService.query_by_name(name=account_name).id
            if account_name is not None
            else strategy.account_id
        )
        experts_extern_id = (
            StrategyParamService.query_by_name(name=params_name)
            if params_name is not None
            else None
        )
        task = Task(
            task_name=task_name,
            account_id=account_id,
            experts_id=strategy.id,
            experts_extern_id=experts_extern_id,
            params=params,
            context=context,
        )
        task = TaskDao.insert_or_get_task(task)
        cls.update_task(
            task,
            account_id=account_id,
            experts_extern_id=experts_extern_id,
            context=context,
            params=params,
        )
        return task

    @classmethod
    def update_task(cls, task, **kwargs):
        """更新任务参数"""
        for k, v in kwargs.items():
            setattr(task, k, v)
        TaskDao.insert_or_update_one(task)

    @classmethod
    def update_task_server_by_ip(cls, task_name, server_ip):
        """更新一个任务的服务器"""
        task = cls.query_by_name(task_name=task_name)
        server = ServerService.query_by_ip(ip=server_ip)
        cls.update_task(task, server_id=server.id)
        return task

    @classmethod
    def update_task_server_by_name(cls, task_id, server_name):
        """更新一个任务的服务器"""
        task = cls.query_by_id(task_id)
        server = ServerService.query_by_name(name=server_name)
        cls.update_task(task, server_id=server.id)

    @classmethod
    def query_by_name(cls, task_name):
        """根据任务名查询任务, 返回第一个任务"""
        tasks = TaskDao.select_many({"task_name": task_name})
        if not tasks:
            raise KeyError(f"no task {task_name} exists")
        return tasks[0]

    @classmethod
    def query_by_id(cls, task_id):
        """根据任务id查询任务"""
        task = TaskDao.select_one({"id": task_id})
        if not task:
            raise KeyError(f"no task {task_id} exists")
        return task

    @classmethod
    def query_by_strategy(cls, strategy_name):
        """根据策略名查询任务"""
        strategy = StrategyService.query_by_name(name=strategy_name)
        return TaskDao.select_many({"experts_id": strategy.id})

    @classmethod
    def query_by_strategy_ran_after_dt(cls, strategy_name: str, dt: str):
        """获取策略名对应的任务, 并且在指定时间后运行过"""
        tasks = cls.query_by_strategy(strategy_name=strategy_name)
        dt = parse(dt)
        res = []
        for task in tasks:
            if cls.task_is_alive(task):
                res.append(task)
            else:
                if task.updatetime > dt:
                    res.append(task)
        return res

    @classmethod
    def query_by_server_ip(cls, ip):
        server = ServerService.get_by_ip(ip)
        return TaskDao.select_many({"server_id": server.id})

    @classmethod
    def all_tasks(cls):
        """获取所有任务"""
        tasks = TaskDao.select_many({})
        return tasks

    @classmethod
    def init_task(cls, task_id):
        """初始化单个任务"""
        logger.info("task_id: %s, change runstate: init", task_id)
        return TaskDao.query_and_update_one(
            query_dict={"id": task_id}, update_dict={"run_state": "init"}
        )

    @classmethod
    def ready_task(cls, task_id):
        """预发布单个任务"""
        logger.info("task_id: %s, change runstate: ready", task_id)
        return TaskDao.query_and_update_one(
            query_dict={"id": task_id}, update_dict={"run_state": "ready"}
        )

    @classmethod
    def publish_task(cls, task_id):
        """发布单个任务"""
        logger.info("task_id: %s, change runstate: published", task_id)
        return TaskDao.query_and_update_one(
            query_dict={"id": task_id}, update_dict={"run_state": "published"}
        )

    @classmethod
    def publish_tasks(cls, task_name):
        """发布多个任务"""
        logger.info("task_name: %s, change runstate: published", task_name)
        return TaskDao.query_and_update_many(
            query_dict={"task_name": task_name}, update_dict={"run_state": "published"}
        )

    @classmethod
    def prestop_tasks(cls, task_name):
        """预停止多个任务"""
        logger.info("task_name: %s, change runstate: prestop", task_name)
        return TaskDao.query_and_update_many(
            query_dict={"task_name": task_name}, update_dict={"run_state": "prestop"}
        )

    @classmethod
    def prestop_task(cls, task_id):
        """预停止任务"""
        logger.info("task_id: %s, change runstate: prestop", task_id)
        return TaskDao.query_and_update_one(
            query_dict={"id": task_id}, update_dict={"run_state": "prestop"}
        )

    @classmethod
    def finish_task(cls, task_id: int):
        """单个任务完成"""
        logger.info("task_id: %s, change runstate: finished", task_id)
        return TaskDao.query_and_update_one(
            query_dict={"id": task_id}, update_dict={"run_state": "finished"}
        )

    @classmethod
    def interrupt_task(cls, task_id: int):
        """单个任务中断"""
        logger.info("task_id: %s, change runstate: interrupted", task_id)
        return TaskDao.query_and_update_one(
            query_dict={"id": task_id}, update_dict={"run_state": "interrupted"}
        )

    @classmethod
    def run_task(cls, task_id: int):
        """单个任务开始运行"""
        logger.info("task_id: %s, change runstate: running", task_id)
        return TaskDao.query_and_update_one(
            query_dict={"id": task_id}, update_dict={"run_state": "running"}
        )

    @classmethod
    def get_alive_tasks(cls):
        """获取存活任务"""
        tasks = []
        tasks.extend(TaskDao.select_many({"run_state": "published"}))
        tasks.extend(TaskDao.select_many({"run_state": "ready"}))
        tasks.extend(TaskDao.select_many({"run_state": "running"}))
        return tasks

    @classmethod
    def get_published_tasks(cls) -> Optional[List[Task]]:
        """获取所有发布的任务"""
        return TaskDao.select_many({"run_state": "published"})

    @classmethod
    def get_running_tasks(cls):
        """获取正在运行的任务"""
        return TaskDao.select_many({"run_state": "running"})

    @classmethod
    def get_interrupted_tasks(cls):
        """获取正在运行的任务"""
        return TaskDao.select_many({"run_state": "interrupted"})

    @classmethod
    def get_published_own_tasks(cls, ip) -> Optional[List[Task]]:
        """获取所有发布且属于某个ip的任务id"""
        server_id = ServerService.get_by_ip(ip)
        tasks = TaskDao.select_many({"run_state": "published", "server_id": server_id})
        return tasks

    @classmethod
    def get_task_running_params(cls, task: Task):
        """
        获取任务运行参数
        Args:
            task (Task): 任务模型
        Returns:
            dict:
                task_id (int): 任务id
                strategy (str): 策略名
                params (dict): 策略参数
                context (dict): 数据上下文参数
        """
        # 获取默认参数
        strategy_name = task.strategy.name
        context_params = task.context
        strategy_params = task.params
        extern = task.extern
        if extern is not None:
            params = extern.params
            params.update(strategy_params)
        else:
            params = strategy_params
        account = task.account
        account_params = (
            {
                "account_id": account.id,
                "platform": account.platform,
                "apikey": account.api_key,
                "apisecret": account.api_secret,
                "apipass": account.api_pwd,
                "purpose": account.purpose,
                "link_id_future": account.link_id_future,
            }
            if account is not None
            else {}
        )
        return {
            "task_id": task.id,
            "strategy": strategy_name,
            "params": params,
            "context": context_params,
            "account": account_params,
        }

    @classmethod
    def task_is_alive(cls, task):
        """
        判断任务是否为存活状态
        表示任务已经开始运行
        """
        return task.run_state in {"ready", "running", "prestop"}

    @classmethod
    def task_is_alive_to_run(cls, task):
        """
        判断任务是否为存活且运行状态
        表示任务已经开始运行, 且没有进入prestop
        """
        return task.run_state in {"ready", "running"}

    @classmethod
    def task_is_interrupted(cls, task):
        """
        判断任务是否为中断状态
        表示任务无法继续运行
        """
        return task.run_state == "interrupted"

    @classmethod
    def task_is_prestop(cls, task):
        """判断任务是否为预备停止状态"""
        return task.run_state == "prestop"

    @classmethod
    def task_is_idle(cls, task):
        """
        判断任务是否为待机状态
        表示任务已经停止, 可以再次运行
        """
        return task.run_state in {"init", "finished"}
