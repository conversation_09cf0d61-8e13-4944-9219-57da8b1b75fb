from sqlalchemy import (
    Enum,
    String,
    DECIMAL,
    BIGINT,
    BOOLEAN,
    UniqueConstraint,
)
from sqlalchemy.orm import (
    Mapped,
    mapped_column,
)

from sqlalchemy.ext.hybrid import hybrid_property


from .base import Base


class BnMarginTrade(Base):
    """
    币安杠杆交易表
    """

    __tablename__ = "bn_margin_trade"

    _id: Mapped[int] = mapped_column(
        primary_key=True, nullable=False, autoincrement=True
    )
    id: Mapped[int] = mapped_column(nullable=False, autoincrement=False)
    accountId: Mapped[int] = mapped_column(nullable=False, comment="账户ID")
    symbol: Mapped[str] = mapped_column(String(20), index=True, comment="品种")
    orderId: Mapped[int] = mapped_column(BIGINT, nullable=False, comment="订单ID")
    price: Mapped[DECIMAL] = mapped_column(
        DECIMAL(18, 8), nullable=False, comment="成交价格"
    )
    qty: Mapped[DECIMAL] = mapped_column(
        DECIMAL(18, 8), nullable=False, comment="成交数量"
    )
    quoteQty: Mapped[DECIMAL] = mapped_column(
        DECIMAL(18, 8), nullable=False, comment="成交额"
    )
    commission: Mapped[DECIMAL] = mapped_column(
        DECIMAL(18, 8), nullable=False, comment="手续费"
    )
    commissionAsset: Mapped[str] = mapped_column(
        String(20), nullable=True, comment="手续费资产"
    )
    buyer: Mapped[bool] = mapped_column(BOOLEAN, nullable=False, comment="买单")
    maker: Mapped[bool] = mapped_column(BOOLEAN, nullable=False, comment="maker")
    cldOrderId: Mapped[str] = mapped_column(
        String(255), nullable=True, comment="自定义订单ID"
    )
    time: Mapped[int] = mapped_column(
        BIGINT, index=True, nullable=False, comment="时间"
    )

    __table_args__ = (
        UniqueConstraint("accountId", "id", name="uix_bn_margin_trade_accountId_id"),
    )

    @hybrid_property
    def isBuyer(self):
        return self.buyer

    @isBuyer.setter
    def isBuyer(self, value):
        self.buyer = value

    @hybrid_property
    def isMaker(self):
        return self.maker

    @isMaker.setter
    def isMaker(self, value):
        self.maker = value

    @hybrid_property
    def isBestMatch(self):
        pass

    @isBestMatch.setter
    def isBestMatch(self, value):
        pass


class BnUMTrade(Base):
    """
    币安U本位交易表
    """

    __tablename__ = "bn_um_trade"

    _id: Mapped[int] = mapped_column(
        primary_key=True, nullable=False, autoincrement=True
    )
    id: Mapped[int] = mapped_column(nullable=False, autoincrement=False)
    accountId: Mapped[int] = mapped_column(nullable=False, comment="账户ID")
    symbol: Mapped[str] = mapped_column(String(20), index=True, comment="品种")
    orderId: Mapped[int] = mapped_column(BIGINT, nullable=False, comment="订单ID")
    positionSide: Mapped[str] = mapped_column(
        Enum("LONG", "SHORT"), nullable=False, comment="方向"
    )
    price: Mapped[DECIMAL] = mapped_column(
        DECIMAL(18, 8), nullable=False, comment="成交价格"
    )
    qty: Mapped[DECIMAL] = mapped_column(
        DECIMAL(18, 8), nullable=False, comment="成交数量"
    )
    realizedPnl: Mapped[DECIMAL] = mapped_column(
        DECIMAL(18, 8), nullable=False, comment="已实现盈亏"
    )
    quoteQty: Mapped[DECIMAL] = mapped_column(
        DECIMAL(18, 8), nullable=False, comment="成交额"
    )
    commission: Mapped[DECIMAL] = mapped_column(
        DECIMAL(18, 8), nullable=False, comment="手续费"
    )
    commissionAsset: Mapped[str] = mapped_column(
        String(20), nullable=True, comment="手续费资产"
    )
    buyer: Mapped[bool] = mapped_column(BOOLEAN, nullable=False, comment="买单")
    maker: Mapped[bool] = mapped_column(BOOLEAN, nullable=False, comment="maker")
    cldOrderId: Mapped[str] = mapped_column(
        String(255), nullable=True, comment="自定义订单ID"
    )
    time: Mapped[int] = mapped_column(
        BIGINT, index=True, nullable=False, comment="时间"
    )

    __table_args__ = (
        UniqueConstraint("accountId", "id", name="uix_bn_um_trade_accountId_id"),
    )

    @hybrid_property
    def side(self):
        pass

    @side.setter
    def side(self, value):
        pass
