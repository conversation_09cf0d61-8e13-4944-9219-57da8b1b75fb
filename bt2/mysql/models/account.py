from typing import List
from sqlalchemy import String, Integer
from sqlalchemy.orm import (
    Mapped,
    relationship,
    mapped_column,
)

from .base import Base


class Account(Base):
    """
    账户表, 存储账户信息
    """

    __tablename__ = "f_user_account"

    id: Mapped[int] = mapped_column(primary_key=True)
    nickname: Mapped[str] = mapped_column(
        String(255), nullable=False, unique=True, comment="账户名称"
    )
    platform: Mapped[str] = mapped_column(String(255), comment="平台")
    api_pwd: Mapped[str] = mapped_column(String(255), default="", comment="apipass")
    api_key: Mapped[str] = mapped_column(String(255), default="", comment="apikey")
    api_secret: Mapped[str] = mapped_column(
        String(255), default="", comment="apisecret"
    )
    purpose: Mapped[int] = mapped_column(Integer(), comment="交易账户类型")
    link_id_future: Mapped[str] = mapped_column(
        String(255), comment="经纪商LinkID(合约)"
    )

    tasks: Mapped[List["Task"]] = relationship(back_populates="account")
