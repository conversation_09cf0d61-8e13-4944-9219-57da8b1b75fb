from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    J<PERSON><PERSON>,
    Enum,
    String,
    TIMESTAMP,
    Integer,
    func,
    event,
)
from sqlalchemy.orm import (
    Mapped,
    relationship,
    mapped_column,
)
from sqlalchemy.orm import attributes
import hashlib, json
from ..exception import TaskInvalidStateException
from .base import Base


class Task(Base):
    """
    任务表, 存放运行任务相关信息和状态
    """

    __tablename__ = "f_task"

    id: Mapped[int] = mapped_column(primary_key=True)
    task_name: Mapped[str] = mapped_column(
        String(255), nullable=False, comment="任务名称"
    )
    experts_id: Mapped[int] = mapped_column(
        ForeignKey("f_experts.id"), nullable=False, comment="策略id"
    )
    experts_extern_id: Mapped[int] = mapped_column(
        ForeignKey("f_experts_extern.id"), nullable=True, comment="策略参数id"
    )
    server_id: Mapped[int] = mapped_column(
        ForeignKey("f_server.id"), nullable=True, comment="运算服务器id"
    )
    account_id: Mapped[int] = mapped_column(
        ForeignKey("f_user_account.id"), nullable=True, comment="账户id"
    )
    context: Mapped[str] = mapped_column(
        JSON, nullable=False, default=[], comment="数据源参数"
    )
    params: Mapped[str] = mapped_column(
        JSON, nullable=False, default={}, comment="任务实际运行参数, 任务参数快照"
    )
    run_state: Mapped[str] = mapped_column(
        Enum(
            "init",
            "ready",
            "published",
            "running",
            "prestop",
            "finished",
            "interrupted",
        ),
        nullable=False,
        default="init",
        comment="运行状态",
    )
    task_hash: Mapped[str] = mapped_column(
        String(64), unique=True, nullable=False, comment="任务参数hash值, 创建时生成"
    )
    createtime: Mapped[str] = mapped_column(
        TIMESTAMP, server_default=func.now(), nullable=True, comment="创建时间"
    )
    updatetime: Mapped[str] = mapped_column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        nullable=True,
        comment="更新时间",
    )

    version_id: Mapped[int] = mapped_column(Integer, nullable=False, comment="版本号")

    __mapper_args__ = {"version_id_col": version_id}

    account: Mapped["Account"] = relationship(back_populates="tasks")
    strategy: Mapped["Strategy"] = relationship(back_populates="tasks")
    extern: Mapped["StrategyParams"] = relationship(back_populates="tasks")

    def __repr__(self):
        return f"<Task(id={self.id}, task_name={self.task_name}, run_state={self.run_state})>"

    def gen_task_hash(self):
        """task生成task_hash"""
        params = {
            "task_name": self.task_name,
        }
        return self._gen_task_hash(params)

    @staticmethod
    def _gen_task_hash(params):
        """
        生成task_hash规则
        """
        return hashlib.md5(json.dumps(params).encode()).hexdigest()

    @staticmethod
    def _add_task_hash(mapper, connection, target):
        """
        task添加task_hash, 用于钩子 before_insert
        """
        target.task_hash = target.gen_task_hash()

    @staticmethod
    def _check_run_state(mapper, connection, target):
        """状态变化检查"""
        transitions = {
            "init": {"published"},
            "published": {"init", "ready"},
            "ready": {"init", "running"},
            "running": {"init", "prestop", "finished", "interrupted"},
            "prestop": {"init", "finished", "interrupted"},
            "finished": {"init", "published"},
            "interrupted": {"init"},
        }
        # 获取更新前的状态
        history = attributes.get_history(target, "run_state")
        if history.has_changes():
            old_state = history.deleted[0]
            new_state = history.added[0]
            if new_state not in transitions[old_state]:
                raise TaskInvalidStateException(
                    f"{target.id} has a wrong state {old_state} to {new_state}"
                )


event.listen(Task, "before_insert", Task._add_task_hash)

event.listen(Task, "before_update", Task._check_run_state)
