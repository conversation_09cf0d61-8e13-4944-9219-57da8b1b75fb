## 策略和策略参数

from sqlalchemy import (
    ForeignKey,
    Enum,
    String,
    BIGINT,
    DECIMAL,
    BOOLEAN,
    PrimaryKeyConstraint,
)
from sqlalchemy.ext.hybrid import hybrid_property

from sqlalchemy.orm import (
    Mapped,
    mapped_column,
)
import uuid

from .base import Base


class TaskOrder(Base):
    """
    任务订单表
    """

    __tablename__ = "f_task_order"

    id: Mapped[int] = mapped_column(primary_key=True)
    task_id: Mapped[int] = mapped_column(
        ForeignKey("f_task.id"), nullable=False, comment="任务id"
    )
    order_num: Mapped[str] = mapped_column(
        String(36), default=lambda: str(uuid.uuid4()), nullable=False, comment="订单号"
    )
    symbol: Mapped[str] = mapped_column(String(255), nullable=False, comment="品种")
    side: Mapped[str] = mapped_column(
        Enum("BUY", "SELL"), nullable=False, comment="买卖方向"
    )
    position_side: Mapped[str] = mapped_column(
        Enum("LONG", "SHORT"), nullable=False, comment="持仓方向"
    )
    order_quantity: Mapped[float] = mapped_column(comment="下单数量")
    trade_quantity: Mapped[float] = mapped_column(comment="成交数量")
    order_price: Mapped[float] = mapped_column(comment="下单价格")
    trade_price: Mapped[float] = mapped_column(comment="成交价格")
    order_ts: Mapped[int] = mapped_column(BIGINT, comment="下单时间")
    trade_ts: Mapped[int] = mapped_column(BIGINT, comment="成交时间")
    order_type: Mapped[str] = mapped_column(
        String(16), default="Market", comment="下单类型"
    )
    state: Mapped[str] = mapped_column(
        Enum("pending", "processing", "success", "fail"), comment="订单状态"
    )


class BnUMOrder(Base):
    """
    币安U本位订单表
    """

    __tablename__ = "bn_um_order"

    # 联合主键
    __table_args__ = (PrimaryKeyConstraint("accountId", "orderId"),)

    accountId: Mapped[int] = mapped_column(nullable=False, comment="账户ID")
    orderId: Mapped[int] = mapped_column(BIGINT, nullable=False, comment="订单ID")
    avgPrice: Mapped[float] = mapped_column(DECIMAL(50, 25), comment="平均成交价格")
    clientOrderId: Mapped[str] = mapped_column(String(255), comment="客户端订单ID")
    cumQuote: Mapped[float] = mapped_column(DECIMAL(50, 25), comment="成交金额")
    executedQty: Mapped[float] = mapped_column(DECIMAL(50, 25), comment="已成交数量")
    origQty: Mapped[float] = mapped_column(DECIMAL(50, 25), comment="原始订单数量")
    origType: Mapped[str] = mapped_column(String(50), comment="原始订单类型")
    price: Mapped[float] = mapped_column(DECIMAL(50, 25), comment="订单价格")
    reduceOnly: Mapped[bool] = mapped_column(BOOLEAN, comment="是否仅减仓")
    side: Mapped[str] = mapped_column(
        Enum("BUY", "SELL"), nullable=False, comment="买卖方向"
    )
    positionSide: Mapped[str] = mapped_column(
        Enum("BOTH", "LONG", "SHORT"), comment="持仓方向"
    )
    status: Mapped[str] = mapped_column(
        Enum("NEW", "PARTIALLY_FILLED", "FILLED", "CANCELED", "REJECTED", "EXPIRED"),
        nullable=False,
        comment="订单状态",
    )
    symbol: Mapped[str] = mapped_column(String(50), comment="交易对")
    time: Mapped[int] = mapped_column(BIGINT, comment="订单时间")
    timeInForce: Mapped[str] = mapped_column(
        Enum("GTC", "IOC", "FOK", "GTX"), nullable=False, comment="有效方式"
    )
    ordType: Mapped[str] = mapped_column(
        Enum(
            "LIMIT",
            "MARKET",
            "STOP",
            "STOP_MARKET",
            "TAKE_PROFIT",
            "TAKE_PROFIT_MARKET",
            "TRAILING_STOP_MARKET",
        ),
        nullable=False,
        comment="订单类型",
    )
    updateTime: Mapped[int] = mapped_column(BIGINT, comment="订单更新时间")
    selfTradePreventionMode: Mapped[str] = mapped_column(
        String(50), comment="自我交易预防模式"
    )
    goodTillDate: Mapped[int] = mapped_column(BIGINT, comment="订单有效期")
    priceMatch: Mapped[str] = mapped_column(String(50), comment="价格匹配模式")

    @hybrid_property
    def type(self):
        return self.ordType

    @type.setter
    def type(self, value):
        self.ordType = value


class BnMarginOrder(Base):
    """
    币安杠杆订单表
    """

    __tablename__ = "bn_margin_order"

    # 联合主键
    __table_args__ = (PrimaryKeyConstraint("accountId", "orderId"),)

    accountId: Mapped[int] = mapped_column(nullable=False, comment="账户ID")
    orderId: Mapped[int] = mapped_column(BIGINT, nullable=False, comment="订单ID")
    clientOrderId: Mapped[str] = mapped_column(
        String(255), nullable=False, comment="客户端订单ID"
    )
    cummulativeQuoteQty: Mapped[float] = mapped_column(
        DECIMAL(50, 25), nullable=False, comment="累计成交金额"
    )
    executedQty: Mapped[float] = mapped_column(
        DECIMAL(50, 25), nullable=False, comment="已成交数量"
    )
    icebergQty: Mapped[float] = mapped_column(
        DECIMAL(50, 25), nullable=False, comment="冰山订单数量"
    )
    isWorking: Mapped[bool] = mapped_column(
        BOOLEAN, nullable=False, comment="是否在工作"
    )
    origQty: Mapped[float] = mapped_column(
        DECIMAL(50, 25), nullable=False, comment="原始订单数量"
    )
    price: Mapped[float] = mapped_column(
        DECIMAL(50, 25), nullable=False, comment="订单价格"
    )
    side: Mapped[str] = mapped_column(
        Enum("BUY", "SELL"), nullable=False, comment="买卖方向"
    )
    status: Mapped[str] = mapped_column(
        Enum(
            "NEW",
            "PARTIALLY_FILLED",
            "FILLED",
            "CANCELED",
            "PENDING_CANCEL",
            "REJECTED",
            "EXPIRED",
        ),
        nullable=False,
        comment="订单状态",
    )
    stopPrice: Mapped[float] = mapped_column(
        DECIMAL(50, 25), nullable=False, comment="止损价格"
    )
    symbol: Mapped[str] = mapped_column(String(50), nullable=False, comment="交易对")
    time: Mapped[int] = mapped_column(BIGINT, nullable=False, comment="订单创建时间")
    timeInForce: Mapped[str] = mapped_column(
        Enum("GTC", "IOC", "FOK", "GTX"), nullable=False, comment="有效方式"
    )
    ordType: Mapped[str] = mapped_column(
        Enum(
            "LIMIT",
            "MARKET",
            "STOP",
            "STOP_MARKET",
            "TAKE_PROFIT",
            "TAKE_PROFIT_MARKET",
            "TRAILING_STOP_MARKET",
        ),
        nullable=False,
        comment="订单类型",
    )
    updateTime: Mapped[int] = mapped_column(
        BIGINT, nullable=False, comment="订单更新时间"
    )
    selfTradePreventionMode: Mapped[str] = mapped_column(
        String(50), comment="自我交易预防模式"
    )
    preventedMatchId: Mapped[str] = mapped_column(String(50), nullable=True)
    preventedQuantity: Mapped[str] = mapped_column(String(50), nullable=True)

    @hybrid_property
    def type(self):
        return self.ordType

    @type.setter
    def type(self, value):
        self.ordType = value


class OkUMOrder(Base):
    """
    欧意U本位订单表
    """

    __tablename__ = "ok_um_order"

    # 联合主键
    __table_args__ = (PrimaryKeyConstraint("accountId", "orderId"),)

    accountId: Mapped[int] = mapped_column(nullable=False, comment="账户ID")
    orderId: Mapped[int] = mapped_column(BIGINT, nullable=False, comment="订单ID")
    avgPrice: Mapped[float] = mapped_column(DECIMAL(50, 25), comment="平均成交价格")
    clientOrderId: Mapped[str] = mapped_column(String(255), comment="客户端订单ID")
    cumQuote: Mapped[float] = mapped_column(DECIMAL(50, 25), comment="成交金额")
    executedQty: Mapped[float] = mapped_column(DECIMAL(50, 25), comment="已成交数量")
    origQty: Mapped[float] = mapped_column(DECIMAL(50, 25), comment="原始订单数量")
    origType: Mapped[str] = mapped_column(String(50), comment="原始订单类型")
    price: Mapped[float] = mapped_column(DECIMAL(50, 25), comment="订单价格")
    reduceOnly: Mapped[bool] = mapped_column(BOOLEAN, comment="是否仅减仓")
    side: Mapped[str] = mapped_column(
        Enum("BUY", "SELL"), nullable=False, comment="买卖方向"
    )
    positionSide: Mapped[str] = mapped_column(
        Enum("BOTH", "LONG", "SHORT"), comment="持仓方向"
    )
    status: Mapped[str] = mapped_column(
        Enum("NEW", "PARTIALLY_FILLED", "FILLED", "CANCELED", "REJECTED", "EXPIRED"),
        nullable=False,
        comment="订单状态",
    )
    symbol: Mapped[str] = mapped_column(String(50), comment="交易对")
    time: Mapped[int] = mapped_column(BIGINT, comment="订单时间")
    timeInForce: Mapped[str] = mapped_column(
        Enum("GTC", "IOC", "FOK", "GTX"), nullable=False, comment="有效方式"
    )
    ordType: Mapped[str] = mapped_column(
        Enum(
            "LIMIT",
            "MARKET",
            "STOP",
            "STOP_MARKET",
            "TAKE_PROFIT",
            "TAKE_PROFIT_MARKET",
            "TRAILING_STOP_MARKET",
        ),
        nullable=False,
        comment="订单类型",
    )
    updateTime: Mapped[int] = mapped_column(BIGINT, comment="订单更新时间")
    selfTradePreventionMode: Mapped[str] = mapped_column(
        String(50), comment="自我交易预防模式"
    )
    goodTillDate: Mapped[int] = mapped_column(BIGINT, comment="订单有效期")
    priceMatch: Mapped[str] = mapped_column(String(50), comment="价格匹配模式")


class OkMarginOrder(Base):
    """
    欧意现货订单表
    """

    __tablename__ = "ok_margin_order"

    # 联合主键
    __table_args__ = (PrimaryKeyConstraint("accountId", "orderId"),)

    accountId: Mapped[int] = mapped_column(nullable=False, comment="账户ID")
    orderId: Mapped[int] = mapped_column(BIGINT, nullable=False, comment="订单ID")
    clientOrderId: Mapped[str] = mapped_column(
        String(255), nullable=False, comment="客户端订单ID"
    )
    cummulativeQuoteQty: Mapped[float] = mapped_column(
        DECIMAL(50, 25), nullable=False, comment="累计成交金额"
    )
    executedQty: Mapped[float] = mapped_column(
        DECIMAL(50, 25), nullable=False, comment="已成交数量"
    )
    icebergQty: Mapped[float] = mapped_column(
        DECIMAL(50, 25), nullable=False, comment="冰山订单数量"
    )
    isWorking: Mapped[bool] = mapped_column(
        BOOLEAN, nullable=False, comment="是否在工作"
    )
    origQty: Mapped[float] = mapped_column(
        DECIMAL(50, 25), nullable=False, comment="原始订单数量"
    )
    price: Mapped[float] = mapped_column(
        DECIMAL(50, 25), nullable=False, comment="订单价格"
    )
    side: Mapped[str] = mapped_column(
        Enum("BUY", "SELL"), nullable=False, comment="买卖方向"
    )
    status: Mapped[str] = mapped_column(
        Enum(
            "NEW",
            "PARTIALLY_FILLED",
            "FILLED",
            "CANCELED",
            "PENDING_CANCEL",
            "REJECTED",
            "EXPIRED",
        ),
        nullable=False,
        comment="订单状态",
    )
    stopPrice: Mapped[float] = mapped_column(
        DECIMAL(50, 25), nullable=False, comment="止损价格"
    )
    symbol: Mapped[str] = mapped_column(String(50), nullable=False, comment="交易对")
    time: Mapped[int] = mapped_column(BIGINT, nullable=False, comment="订单创建时间")
    timeInForce: Mapped[str] = mapped_column(
        Enum("GTC", "IOC", "FOK", "GTX"), nullable=False, comment="有效方式"
    )
    ordType: Mapped[str] = mapped_column(
        Enum(
            "LIMIT",
            "MARKET",
            "STOP",
            "STOP_MARKET",
            "TAKE_PROFIT",
            "TAKE_PROFIT_MARKET",
            "TRAILING_STOP_MARKET",
        ),
        nullable=False,
        comment="订单类型",
    )
    updateTime: Mapped[int] = mapped_column(
        BIGINT, nullable=False, comment="订单更新时间"
    )
    selfTradePreventionMode: Mapped[str] = mapped_column(
        String(50), comment="自我交易预防模式"
    )
    preventedMatchId: Mapped[str] = mapped_column(String(50), nullable=True)
    preventedQuantity: Mapped[str] = mapped_column(String(50), nullable=True)
