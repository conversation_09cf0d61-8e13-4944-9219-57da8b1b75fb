## 服务器配置


from typing import List, Optional
from sqlalchemy import <PERSON><PERSON>ey, JSO<PERSON>, String, Enum
from sqlalchemy.orm import (
    Mapped,
    relationship,
    mapped_column,
)

from .base import Base


class ServerConfig(Base):
    """
    服务器配置表, 存放配置策略
    """

    __tablename__ = "f_server_config"

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        unique=True,
        default="default",
        comment="服务器配置名称",
    )
    params: Mapped[str] = mapped_column(JSON, nullable=False, comment="服务器配置参数")

    server: Mapped[Optional[List["Server"]]] = relationship(back_populates="config")


class Server(Base):
    """
    服务器管理表, 存放具体的服务器信息
    """

    __tablename__ = "f_server"

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(
        String(32), nullable=True, unique=True, default="default", comment="服务器名称"
    )
    server_config_id: Mapped[int] = mapped_column(
        ForeignKey("f_server_config.id"), nullable=False, comment="服务器配置id"
    )
    ip: Mapped[str] = mapped_column(String(255), nullable=False, comment="ip")
    state: Mapped[str] = mapped_column(
        Enum("up", "down"), nullable=True, default="up", comment="服务器状态"
    )

    config: Mapped["ServerConfig"] = relationship(back_populates="server")
