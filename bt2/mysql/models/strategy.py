## 策略和策略参数

from typing import List, Optional
from sqlalchemy import (
    JSON,
    String,
)
from sqlalchemy.orm import (
    Mapped,
    relationship,
    mapped_column,
)

from .base import Base


class Strategy(Base):
    """
    策略表, 存放登记的策略
    Fields:
        name (str): 策略名称
    """

    __tablename__ = "f_experts"

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(
        String(255), nullable=False, unique=True, comment="策略名称"
    )
    default_params: Mapped[str] = mapped_column(
        JSON, nullable=True, default={}, comment="策略默认参数"
    )
    account_id: Mapped[int] = mapped_column(comment="账户id")

    tasks: Mapped[Optional[List["Task"]]] = relationship(back_populates="strategy")

    def __repr__(self):
        return f"<Strategy(id={self.id}, name='{self.name}')>"


class StrategyParams(Base):
    """
    策略参数表, 存放策略的参数/参数组
    参数字段为序列化后的json字符串
    Fields:
        name (str): 策略参数名
    """

    __tablename__ = "f_experts_extern"

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(
        String(255), nullable=False, comment="策略参数名称, 便于通过名称获取参数"
    )
    params: Mapped[str] = mapped_column(JSON, nullable=False, comment="策略参数")

    tasks: Mapped[Optional[List["Task"]]] = relationship(back_populates="extern")

    def __repr__(self):
        return f"<StrategyParams(id={self.id}, name='{self.name}')>"
