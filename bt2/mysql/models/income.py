from sqlalchemy import (
    Enum,
    String,
    Integer,
    DECIMAL,
    BIGINT,
    PrimaryKeyConstraint,
)
from sqlalchemy.orm import (
    Mapped,
    mapped_column,
)

from .base import Base


class BnUMIncome(Base):
    """
    币安损益表
    """

    __tablename__ = "bn_um_income"

    accountId: Mapped[int] = mapped_column(nullable=False, comment="账户ID")
    symbol: Mapped[str] = mapped_column(String(50), index=True, comment="品种")
    incomeType: Mapped[str] = mapped_column(
        Enum(
            "TRANSFER",
            "WELCOME_BONUS",
            "REALIZED_PNL",
            "FUNDING_FEE",
            "COMMISSION",
            "INSURANCE_CLEAR",
            "REFERRAL_KICKBACK",
            "COMMISSION_REBATE",
            "API_REBATE",
            "CONTEST_REWARD",
            "CROSS_COLLATERAL_TRANSFER",
            "OPTIONS_PREMIUM_FEE",
            "OPTIONS_SETTLE_PROFIT",
            "INTERNAL_TRANSFER",
            "AUTO_EXCHANGE",
            "DELIVERED_SETTELMENT",
            "COIN_SWAP_DEPOSIT",
            "COIN_SWAP_WITHDRAW",
            "POSITION_LIMIT_INCREASE_FEE",
        ),
        comment="收益类型",
    )
    income: Mapped[DECIMAL] = mapped_column(
        DECIMAL(50, 25), nullable=False, comment="收益"
    )
    asset: Mapped[str] = mapped_column(String(50), nullable=True, comment="资产")
    time: Mapped[int] = mapped_column(
        BIGINT, index=True, nullable=False, comment="时间"
    )
    tradeId: Mapped[str] = mapped_column(String(255), nullable=True, comment="trade ID")
    tranId: Mapped[int] = mapped_column(BIGINT, nullable=True, comment="tran ID")
    info: Mapped[str] = mapped_column(String(255), nullable=True, comment="info")

    __table_args__ = (
        PrimaryKeyConstraint(
            "accountId", "incomeType", "tranId", name="uix_bn_um_income"
        ),
    )


class OkUMIncome(Base):
    """
    欧意损益表
    """

    __tablename__ = "ok_um_income"

    # 联合主键
    __table_args__ = (
        PrimaryKeyConstraint(
            "accountId",
            "tranId",
            "incomeType",
        ),
    )

    accountId: Mapped[int] = mapped_column(nullable=False, comment="账户ID")
    symbol: Mapped[str] = mapped_column(String(50), index=True, comment="品种")
    incomeType: Mapped[str] = mapped_column(
        Enum(
            "TRANSFER",
            "WELCOME_BONUS",
            "REALIZED_PNL",
            "FUNDING_FEE",
            "COMMISSION",
            "INSURANCE_CLEAR",
            "REFERRAL_KICKBACK",
            "COMMISSION_REBATE",
            "API_REBATE",
            "CONTEST_REWARD",
            "CROSS_COLLATERAL_TRANSFER",
            "OPTIONS_PREMIUM_FEE",
            "OPTIONS_SETTLE_PROFIT",
            "INTERNAL_TRANSFER",
            "AUTO_EXCHANGE",
            "DELIVERED_SETTELMENT",
            "COIN_SWAP_DEPOSIT",
            "COIN_SWAP_WITHDRAW",
            "POSITION_LIMIT_INCREASE_FEE",
        ),
        comment="收益类型",
    )
    income: Mapped[DECIMAL] = mapped_column(
        DECIMAL(50, 25), nullable=False, comment="收益"
    )
    asset: Mapped[str] = mapped_column(String(50), nullable=True, comment="资产")
    time: Mapped[int] = mapped_column(
        BIGINT, index=True, nullable=False, comment="时间"
    )
    tradeId: Mapped[str] = mapped_column(String(255), nullable=True, comment="trade ID")
    tranId: Mapped[int] = mapped_column(BIGINT, nullable=True, comment="tran ID")
    info: Mapped[str] = mapped_column(String(255), nullable=True, comment="info")
