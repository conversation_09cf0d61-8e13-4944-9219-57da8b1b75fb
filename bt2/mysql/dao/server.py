from sqlalchemy.orm import Session
from ..models import Server, ServerConfig
from .base import BaseDao
from sqlalchemy.future import select
from ..manager import session_decorator


class ServerDao(BaseDao):
    """
    服务器表Dao
    """

    Model = Server

    @classmethod
    @session_decorator
    def get_server_by_ip(cls, ip: str, session: Session):
        """获取ip对应的server"""
        query = select(Server).where(Server.ip == ip)
        res = session.scalars(query).one_or_none()
        return res

    @classmethod
    @session_decorator
    def insert_or_update_server(cls, server: Server, session: Session):
        """根据ip更新或新增一个server"""
        cls.insert_or_update_one(server, "ip")


class ServerConfigDao(BaseDao):
    """
    服务器配置表Dao
    """

    Model = ServerConfig

    @classmethod
    @session_decorator
    def get_config_by_name(cls, name, session: Session):
        """根据参数名获取serverconfig"""
        query = select(ServerConfig).where(ServerConfig.name == name)
        res = session.scalars(query).one_or_none()
        return res

    @classmethod
    @session_decorator
    def insert_or_update_config(cls, config: ServerConfig, session: Session):
        """根据config_name更新或新增一个serverconfig"""
        cls.insert_or_update_one(config, "name")
