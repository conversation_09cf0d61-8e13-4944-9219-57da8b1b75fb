# 基础DAO接口

from sqlalchemy.orm import Session
from sqlalchemy import select

from ..manager import session_decorator
from ..models.base import Base


class BaseDao:
    Model = Base

    @classmethod
    @session_decorator
    def insert_one(cls, obj, session: Session):
        """插入一个model"""
        session.add(obj)

    @classmethod
    @session_decorator
    def insert_many(cls, objs: list, session: Session):
        """插入多个model"""
        session.add_all(objs)

    @classmethod
    @session_decorator
    def insert_or_update_one(cls, obj, session: Session):
        """
        插入或更新一个model
        Args:
            obj (Model), 插入或更新的model
            args (List[str]), 用于查找的字段
        """
        session.merge(obj)

    @classmethod
    @session_decorator
    def insert_or_update_many(cls, objs, session: Session):
        """插入或更新多个model, 性能较差, 需求高性能时手动实现"""
        for obj in objs:
            cls.insert_or_update_one(obj)

    @classmethod
    @session_decorator
    def query_and_update_one(cls, session: Session, query_dict={}, update_dict={}):
        """
        查询并更新一个model
        Args:
            query_dict (dict), 查找字典
            update_dict (dict), 更新字典
        """
        obj = session.query(cls.Model).filter_by(**query_dict).one()
        for arg, value in update_dict.items():
            setattr(obj, arg, value)
        return obj

    @classmethod
    @session_decorator
    def query_and_update_many(cls, session: Session, query_dict={}, update_dict={}):
        """
        查询并更新多个model, 性能较差, 需求高性能时手动实现
        Args:
            query_dict (dict), 查找字典
            update_dict (dict), 更新字典
        """
        Model = cls.Model
        query = select(Model)
        for arg, value in query_dict.items():
            query = query.where(getattr(Model, arg) == value)
        objs = session.scalars(query).all()
        for arg, value in update_dict.items():
            for obj in objs:
                setattr(obj, arg, value)
        return objs

    @classmethod
    @session_decorator
    def select_one(cls, query_dict: dict, session: Session, allow_none=True):
        """
        选择一个model
        Args:
            query_dict (dict): 参数字典
            allow_none (bool): 允许返回none值
        """
        query = select(cls.Model)
        for arg, value in query_dict.items():
            query = query.where(getattr(cls.Model, arg) == value)
        if allow_none:
            res = session.scalars(query).one_or_none()
        else:
            res = session.scalars(query).one()
        return res

    @classmethod
    @session_decorator
    def select_many(cls, query_dict: dict, session: Session):
        query = select(cls.Model)
        for arg, value in query_dict.items():
            query = query.where(getattr(cls.Model, arg) == value)
        res = session.scalars(query).all()
        return res

    @classmethod
    @session_decorator
    def delete_many(cls, objs, session: Session):
        """删除多个model"""
        for obj in objs:
            session.delete(obj)

    @classmethod
    def get_columns(cls, obj, ignore_id=True, ignore_autoincrement=True):
        columns = obj.__table__.columns.keys()
        if ignore_autoincrement:
            # 不包含自增列
            columns = [
                key
                for key in columns
                if obj.__table__.columns[key].autoincrement is not True
            ]
        if ignore_id and "id" in columns:
            columns.remove("id")
        return columns

    @classmethod
    def update_fields(cls, replaced_obj, obj):
        for col in cls.get_columns(obj):
            setattr(replaced_obj, col, getattr(obj, col))

    @classmethod
    def to_dict(cls, objs, ignore_id=True, ignore_autoincrement=True):
        return [
            {
                col: getattr(obj, col)
                for col in cls.get_columns(obj, ignore_id, ignore_autoincrement)
            }
            for obj in objs
        ]
