from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from ..models import Task
from .base import Base<PERSON><PERSON>
from sqlalchemy import select
from ..manager import session_decorator


class TaskDao(BaseDao):
    """
    任务表Dao
    """

    Model = Task

    @classmethod
    @session_decorator
    def get_task_ids_by_state(cls, run_state, session: Session):
        """获取状态对应的任务"""
        query = select(Task).where(Task.run_state == run_state)
        res = session.scalars(query).all()
        return res

    @classmethod
    @session_decorator
    def get_task_ids_by_state_ip(cls, run_state, server_id, session: Session):
        """根据状态和ip获取任务"""
        query = (
            select(Task)
            .where(Task.run_state == run_state)
            .where(Task.server_id == server_id)
        )
        res = session.scalars(query).all()
        return res

    @classmethod
    @session_decorator
    def insert_or_get_task(cls, task, session: Session):
        """新建一个task, 如果已存在则返回该task"""
        try:
            # 检查task是否存在
            task_hash = task.gen_task_hash()
            query_task = cls.select_one({"task_hash": task_hash})
            if query_task:
                task = query_task
            else:
                session.add(task)
                session.commit()
        except IntegrityError:
            session.rollback()
            query = select(cls.Model).where(cls.Model.task_hash == task.task_hash)
            task = session.scalars(query).first()
        finally:
            return task
