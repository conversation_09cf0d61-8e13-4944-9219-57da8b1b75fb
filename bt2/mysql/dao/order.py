from sqlalchemy.orm import Session
from decimal import Decimal
from ..models import TaskOrder, BnUMOrder, BnMarginOrder, OkUMOrder, OkMarginOrder
from .base import BaseDao
from sqlalchemy import select, insert
from ..manager import session_decorator


class TaskOrderDao(BaseDao):
    """
    订单表Dao
    """

    Model = TaskOrder

    @classmethod
    def delete_many_by_query(cls, query_dict):
        """根据查询条件删除"""
        objs = cls.select_many(query_dict=query_dict)
        cls.delete_many(objs)


class BnUMOrderDao(BaseDao):
    Model = BnUMOrder

    @classmethod
    @session_decorator
    def insert_or_ignore_many(cls, objs, session: Session):
        """插入多个model"""
        records = cls.to_dict(objs)
        stmt = insert(cls.Model).prefix_with("IGNORE").values(records)
        session.execute(stmt)

    @classmethod
    def from_data(cls, acount_id, data: list) -> list:
        # 修改字典字段
        converts = []
        for d in data:
            conv = {}
            conv["accountId"] = acount_id
            conv["orderId"] = d["orderId"]
            conv["avgPrice"] = d["avgPrice"]
            conv["clientOrderId"] = d["clientOrderId"]
            conv["cumQuote"] = d["cumQuote"]
            conv["executedQty"] = d["executedQty"]
            conv["origQty"] = d["origQty"]
            conv["origType"] = d["origType"]
            conv["price"] = d["price"]
            conv["reduceOnly"] = d["reduceOnly"]
            conv["side"] = d["side"]
            conv["positionSide"] = d["positionSide"]
            conv["status"] = d["status"]
            conv["symbol"] = d["symbol"]
            conv["time"] = d["time"]
            conv["timeInForce"] = d["timeInForce"]
            conv["ordType"] = d["type"]
            conv["updateTime"] = d["updateTime"]
            conv["selfTradePreventionMode"] = d["selfTradePreventionMode"]
            conv["goodTillDate"] = d["goodTillDate"]
            conv["priceMatch"] = d["priceMatch"]
            converts.append(conv)
        return [BnUMOrder(**k) for k in converts]

    @classmethod
    @session_decorator
    def query_by_time(cls, account_id, start_time, end_time, session: Session):
        return session.scalars(
            select(cls.Model)
            .where(cls.Model.accountId == account_id)
            .where(cls.Model.time.between(start_time, end_time))
        ).all()


class BnMarginOrderDao(BaseDao):
    Model = BnMarginOrder

    @classmethod
    @session_decorator
    def insert_or_ignore_many(cls, objs, session: Session):
        """插入多个model"""
        records = cls.to_dict(objs)
        stmt = insert(cls.Model).prefix_with("IGNORE").values(records)
        session.execute(stmt)

    @classmethod
    def from_data(cls, account_id, data: list) -> list:
        # 修改字典字段
        converts = []
        for d in data:
            d["accountId"] = account_id
            conv = d
            converts.append(conv)
        return [BnMarginOrder(**k) for k in converts]

    @classmethod
    @session_decorator
    def query_by_time(cls, account_id, start_time, end_time, session: Session):
        return session.scalars(
            select(cls.Model)
            .where(cls.Model.accountId == account_id)
            .where(cls.Model.time.between(start_time, end_time))
        ).all()


class OkUMOrderDao(BaseDao):
    Model = OkUMOrder

    @classmethod
    @session_decorator
    def insert_or_ignore_many(cls, objs, session: Session):
        """插入多个model"""
        records = cls.to_dict(objs)
        stmt = insert(cls.Model).prefix_with("IGNORE").values(records)
        session.execute(stmt)

    @classmethod
    def from_data(cls, acount_id: int, ctvals: dict, data: list) -> list:
        # 修改字典字段
        convert = []
        for d in data:
            # 只考虑永续合约
            if d["instType"] != "SWAP" or d["feeCcy"] != "USDT":
                continue
            convert_d = {}

            convert_d["symbol"] = symbol = "".join(d.get("instId", "").split("-")[:2])
            ctval = ctvals.get(symbol, 1)

            convert_d["accountId"] = acount_id
            convert_d["orderId"] = int(d["ordId"])
            convert_d["avgPrice"] = Decimal(d.get("avgPx") or "0")
            convert_d["clientOrderId"] = d.get("clOrdId", "")
            convert_d["executedQty"] = Decimal(d.get("accFillSz") or "0") * Decimal(
                str(ctval)
            )
            convert_d["origQty"] = Decimal(d.get("sz") or "0") * Decimal(str(ctval))
            convert_d["cumQuote"] = convert_d["executedQty"] * convert_d["avgPrice"]
            # 根据ordType判断InForce
            if d["ordType"] == "market":
                convert_d["ordType"] = convert_d["origType"] = "MARKET"
                convert_d["timeInForce"] = "GTC"
            elif d["ordType"] == "limit":
                convert_d["ordType"] = convert_d["origType"] = "LIMIT"
                convert_d["timeInForce"] = "GTC"
            elif d["ordType"] == "ioc":
                convert_d["ordType"] = convert_d["origType"] = "LIMIT"
                convert_d["timeInForce"] = "IOC"
            elif d["ordType"] == "fok":
                convert_d["ordType"] = convert_d["origType"] = "LIMIT"
                convert_d["timeInForce"] = "FOK"
            else:
                raise KeyError(f"Unknown ordType {d['ordType']}")
            convert_d["price"] = Decimal(d.get("px") or "0")
            convert_d["reduceOnly"] = d.get("reduceOnly", "false") == "true"
            convert_d["side"] = d.get("side", "").upper()
            convert_d["positionSide"] = d.get("posSide", "").upper()
            # 设置status
            if d["state"] == "filled":
                convert_d["status"] = "FILLED"
            elif d["state"] == "canceled":
                convert_d["status"] = "CANCELED"
            else:
                raise KeyError(f"Unknown state {d['state']}")
            # 设置symbol

            convert_d["time"] = int(d.get("cTime") or "0")
            convert_d["updateTime"] = int(d.get("uTime") or "0")
            convert_d["selfTradePreventionMode"] = d.get("stpMode", "")
            convert_d["goodTillDate"] = 0
            convert_d["priceMatch"] = ""
            convert.append(cls.Model(**convert_d))
        return convert

    @classmethod
    @session_decorator
    def query_by_time(cls, account_id, start_time, end_time, session: Session):
        return session.scalars(
            select(cls.Model)
            .where(cls.Model.accountId == account_id)
            .where(cls.Model.time.between(start_time, end_time))
        ).all()


class OkMarginOrderDao(BaseDao):
    Model = OkMarginOrder

    @classmethod
    @session_decorator
    def insert_or_ignore_many(cls, objs, session: Session):
        """插入多个model"""
        records = cls.to_dict(objs)
        stmt = insert(cls.Model).prefix_with("IGNORE").values(records)
        session.execute(stmt)

    @classmethod
    def from_data(cls, acount_id: int, data: list) -> list:
        # 修改字典字段
        convert = []
        for d in data:
            # 只考虑永续合约
            if d["instType"] != "SPOT":
                continue
            convert_d = {}
            convert_d["accountId"] = acount_id
            convert_d["orderId"] = int(d["ordId"])
            convert_d["clientOrderId"] = d.get("clOrdId", "")
            avg_price = Decimal(d.get("avgPx") or "0")
            executed_quantity = Decimal(d.get("accFillSz") or "0")
            convert_d["cummulativeQuoteQty"] = avg_price * executed_quantity
            convert_d["executedQty"] = executed_quantity
            convert_d["icebergQty"] = 0
            convert_d["origQty"] = Decimal(d.get("sz") or "0")
            # 根据ordType判断InForce
            if d["ordType"] == "market":
                convert_d["ordType"] = "MARKET"
                convert_d["timeInForce"] = "GTC"
            elif d["ordType"] == "limit":
                convert_d["ordType"] = "LIMIT"
                convert_d["timeInForce"] = "GTC"
            elif d["ordType"] == "ioc":
                convert_d["ordType"] = "LIMIT"
                convert_d["timeInForce"] = "IOC"
            elif d["ordType"] == "fok":
                convert_d["ordType"] = "LIMIT"
                convert_d["timeInForce"] = "FOK"
            else:
                raise KeyError(f"Unknown ordType {d['ordType']}")

            convert_d["price"] = Decimal(d.get("px") or "0")
            convert_d["side"] = d.get("side", "").upper()
            # 设置status
            if d["state"] == "filled":
                convert_d["status"] = "FILLED"
                convert_d["isWorking"] = False
            elif d["state"] == "canceled":
                convert_d["status"] = "CANCELED"
                convert_d["isWorking"] = False
            else:
                raise KeyError(f"Unknown state {d['state']}")

            convert_d["stopPrice"] = 0
            # 设置symbol
            convert_d["symbol"] = "".join(d.get("instId", "").split("-")[:2])
            convert_d["time"] = int(d.get("cTime", "0"))
            convert_d["updateTime"] = int(d.get("uTime") or "0")
            convert_d["selfTradePreventionMode"] = d.get("stpMode", "")
            convert.append(cls.Model(**convert_d))
        return convert

    @classmethod
    @session_decorator
    def query_by_time(cls, account_id, start_time, end_time, session: Session):
        return session.scalars(
            select(cls.Model)
            .where(cls.Model.accountId == account_id)
            .where(cls.Model.time.between(start_time, end_time))
        ).all()
