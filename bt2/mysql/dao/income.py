from ..models import BnUMIncome, OkUMIncome
from .base import BaseDao
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import select, insert
from ..manager import session_decorator


class BnUMIncomeDao(BaseDao):
    Model = BnUMIncome

    @classmethod
    @session_decorator
    def insert_or_ignore_many(cls, objs, session: Session):
        """插入多个model"""
        records = cls.to_dict(objs)
        stmt = insert(cls.Model).prefix_with("IGNORE").values(records)
        session.execute(stmt)

    @classmethod
    def from_data(cls, account_id, data: list) -> list:
        models = [BnUMIncome(accountId=account_id, **k) for k in data]
        return models

    @classmethod
    @session_decorator
    def query_by_time(cls, account_id, start_time, end_time, session: Session):
        # 获取一段时间的收益
        return session.scalars(
            select(cls.Model)
            .where(cls.Model.accountId == account_id)
            .where(cls.Model.time.between(start_time, end_time))
        ).all()


class OkUMIncomeDao(BaseDao):
    Model = OkUMIncome

    @classmethod
    @session_decorator
    def insert_or_ignore_many(cls, objs, session: Session):
        """插入多个model"""
        records = cls.to_dict(objs)
        stmt = insert(cls.Model).prefix_with("IGNORE").values(records)
        session.execute(stmt)

    @classmethod
    def from_data(cls, account_id, data: list) -> list:
        convert_models = []
        for d in data:
            # 只考虑强平, 手续费, pnl和资金费
            if d["type"] not in ["2", "5", "8", "10"]:
                continue

            convert_d = {
                "accountId": account_id,
                "symbol": "".join(d.get("instId", "").split("-")[:2]),
                "asset": d["ccy"],
                "time": int(d["fillTime"]),
                "tradeId": d["tradeId"],
                "tranId": d["billId"],
                "info": d["notes"],
            }

            if d["type"] == "8":
                # 资金费
                convert_d["incomeType"] = "FUNDING_FEE"
                convert_d["income"] = Decimal(d["pnl"])
                convert_models.append(cls.Model(**convert_d))
                continue

            if d["type"] == "2" or d["type"] == "5":
                # 生成手续费和pnl
                # 手续费
                fee_d = convert_d.copy()
                fee_d["incomeType"] = "COMMISSION"
                fee_d["income"] = d["fee"]
                # pnl
                pnl_d = convert_d.copy()
                if d["type"] == "2":
                    pnl_d["incomeType"] = "REALIZED_PNL"
                    pnl_d["income"] = d["pnl"]
                # 强平
                if d["type"] == "5":
                    pnl_d["incomeType"] = "INSURANCE_CLEAR"
                    pnl_d["income"] = d["pnl"]
                convert_models.append(cls.Model(**fee_d))
                convert_models.append(cls.Model(**pnl_d))
                continue

            if d["type"] == "10":
                # 穿仓补偿
                convert_d["incomeType"] = "INSURANCE_CLEAR"
                convert_d["income"] = d["sz"]
                convert_models.append(cls.Model(**convert_d))
                continue

        return convert_models

    @classmethod
    @session_decorator
    def query_by_time(cls, account_id, start_time, end_time, session: Session):
        # 获取一段时间的收益
        return session.scalars(
            select(cls.Model)
            .where(cls.Model.accountId == account_id)
            .where(cls.Model.time.between(start_time, end_time))
        ).all()
