from ..models import BnMarginTrade, BnUMTrade
from .base import BaseDao
from sqlalchemy.orm import Session
from ..manager import session_decorator
from sqlalchemy.dialects.mysql import insert


class TradeDao(BaseDao):
    @classmethod
    @session_decorator
    def insert_or_ignore_many(cls, objs, session: Session):
        """插入多个model"""
        records = cls.to_dict(objs)
        stmt = insert(cls.Model).prefix_with("IGNORE").values(records)
        session.execute(stmt)


class BnMarginTradeDao(TradeDao):
    Model = BnMarginTrade


class BnUMTradeDao(TradeDao):
    Model = BnUMTrade
