## 管理器基类


from configloader import ManagerConfig
from types import MappingProxyType
from logging import getLogger

logger = getLogger(__name__)


class ParamsDict:
    """
    属性字典, 可以通过调用属性的方式获取value
    """

    def __init__(self, d):
        self._data = MappingProxyType(d)

    def __getattr__(self, key):
        return self._data[key]

    def __getitem__(self, key):
        return self._data[key]

    def __str__(self):
        strings = [f"{k}={v}" for k, v in self._data.items()]
        return ",\n".join(strings)

    def __repr__(self):
        return self.__class__.__name__ + ": " + str(self._data)


class ParamsMeta(type):
    """
    参数元类, 通过params属性设定参数, 且参数可继承
    """

    def __new__(cls, name, bases, dct):
        # 初始化 params 并在类创建时将其加入到 dct
        _params = dct.pop("params", {})
        # 支持元祖类型
        if isinstance(_params, tuple):
            _params = dict(_params)
        # 继承父类的参数
        for base in bases:
            if hasattr(base, "_params"):
                for k, v in base._params.items():
                    if k not in _params:
                        _params[k] = v
        # 只浅拷贝, 注意可变对象的问题
        dct["_params"] = _params.copy()
        # 只读别名
        dct["p"] = dct["params"] = ParamsDict(dct["_params"])
        return super().__new__(cls, name, bases, dct)


class ManagerBase(metaclass=ParamsMeta):
    """
    管理器基类
    管理器用于对全局变量做分类管理, 并提供参数涉及的相关功能
    依赖管理器的类不用关心具体参数配置
    """

    ConfigKey = ""

    def __init__(self, **kwargs):
        cls = type(self)
        for key, value in kwargs.items():
            if key in cls._params:
                cls._params[key] = value
            else:
                logger.warning(f"%s : %s is not a valid parameter", cls, key)

        cls.initcls()

    @classmethod
    def initcls(cls):
        pass

    @classmethod
    def from_config(cls):
        return cls(**ManagerConfig[cls.ConfigKey])

    def __enter__(self):
        return

    def __exit__(self, exc_type, exc_value, traceback):
        return
