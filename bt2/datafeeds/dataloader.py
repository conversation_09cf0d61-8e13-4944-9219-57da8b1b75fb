## 获取数据
### 包括各类数据库的数据获取


import time
import queue
import threading
import json
from copy import deepcopy
from dateutil.parser import parse
from ..redismanager import MultiKLineRedisManager
from logging import getLogger
from ..dataloader import DataLoader
from ..ws import MultiKLineWSManager


logger = getLogger(__name__)


class OneWebSocketDataLoader(DataLoader):
    """
    采用websocket订阅历史数据, 只启动单个ws对象
    """

    PAGE_SIZE = 1000

    MANAGER = MultiKLineWSManager

    def __init__(
        self,
        platform="binance",
        symbol="btcusdt",
        inst_type="futures",
        freq="1m",
        start_date: str = None,
        end_date: str = None,
    ):
        # 线程初始化
        self._platform = platform
        self._inst_type = inst_type
        self._symbol = symbol.lower()
        self._freq = freq
        self._queue = queue.Queue()
        self.start_date = start_date
        self.end_date = end_date
        # ws启动完成的标志, 如果没有启动完成, 执行send会弹出异常ws为close状态
        self.ws_opened = threading.Event()
        channel = self.get_channel()
        self._ws = self.MANAGER.get_ws(channel, self._queue, self.ws_opened)

    def load_data(self):
        if self.start_date is None:
            return
        query = self.get_query()
        opened = self.ws_opened.wait(timeout=10)  # 等待ws启动完成
        if not opened:
            raise TimeoutError(
                f"{self._platform} {self._symbol} {self._freq} {self.MANAGER} ws获取线程失败"
            )
        logger.info(
            f"{self._platform} {self._symbol} {self._freq} {self.MANAGER} ws获取线程成功"
        )
        logger.info("发送历史数据请求: %s", query)
        self._ws.send(json.dumps(query))

    def get_query(self):
        query = {
            "platform": self._platform,
            "instType": self._inst_type,
            "symbol": self.symbol,
            "interval": self.freq,
            "pageSize": self.PAGE_SIZE,
        }
        query["startTime"] = (
            parse(self.start_date).timestamp() if self.start_date else None
        )
        query["endTime"] = parse(self.end_date).timestamp() if self.end_date else None
        return query

    @property
    def symbol(self):
        return self._symbol.lower()

    @property
    def freq(self):
        if self._freq == "tick":
            return "tickers"
        else:
            return f"kline{self._freq}"

    def start(self):
        self.load_data()

    def stop(self):
        pass

    def _next(self):
        for i in range(20):  # 重试20次
            try:
                data = self._queue.get(timeout=5)
                if data == None:
                    logger.info(
                        f"{self._platform} {self._symbol} {self._freq} 历史数据读取完毕"
                    )
                    self._queue.put(None)
                    return False
                return data
            except queue.Empty:
                logger.warning(
                    f"{self._platform} {self._symbol} {self._freq} 历史数据队列读取超时, 为空"
                )
        raise ValueError(
            f"{self._platform} {self._symbol} {self._freq} 历史数据队列读取失败"
        )

    def next(self):
        data = self._next()
        if not data:
            return False
        # logger.info(f'db  {self.freq}:  {data}')
        data["edge"] = True
        return data

    def reload_data(self, start_ts, end_ts):
        """
        补充数据
        """
        # 先强制控制频率
        time.sleep(1)
        query = self.get_query()
        query["startTime"] = start_ts // 1000
        query["endTime"] = end_ts // 1000 + 1
        self._ws.send(json.dumps(query))

    def get_channel(self):
        return "_".join([self._platform, self._inst_type, self.freq, self.symbol])


class OneRedisSubscriberThread(DataLoader):
    """
    redis订阅类,
    Args:
        platform (str): 平台
        symbol (str): 品种
        inst_type (str): 资产类型, 期货、现货
        freq (str): 频率
        sub_depth (bool): 是否获取深度信息, 默认否
        timeout (str): 等待超时时间
        retry (int): 重试次数
    """

    MANAGER = MultiKLineRedisManager

    def __init__(
        self,
        platform="binance",
        symbol="btcusdt",
        inst_type="futures",
        freq="1m",
        sub_depth=False,
        timeout=2,
        retry=30,
    ):
        # 线程初始化
        # 创建一个队列
        self.platform = platform
        self.inst_type = inst_type
        self.symbol = symbol.lower()
        self.freq = freq
        self.depth = {"depth": 0}
        self.data_queue = queue.Queue()
        self.MANAGER.subscribe(self.get_channel(), self.data_queue)  # 订阅频道
        self.timeout = timeout
        self.retry = retry

    def start(self):
        # 订阅
        logger.info(
            "订阅 %s  %s  %s 实时k线数据", self.platform, self.symbol, self.freq
        )
        self.MANAGER.start()  # 实际订阅延迟到此处

    def next(self):
        for i in range(self.retry):
            try:
                data = self.data_queue.get(timeout=2)
                return data
            except queue.Empty:
                logger.warning(
                    f"%s %s %s实时数据队列为空, 重试次数: %s",
                    self.platform,
                    self.symbol,
                    self.freq,
                    i,
                )
                self.MANAGER.check_connection()
        logger.error(
            "%s %s %s 获取实时数据重试次数到达上限",
            self.platform,
            self.symbol,
            self.freq,
        )
        raise queue.Empty

    def get_depth(self):
        # 获取深度数据的副本
        return deepcopy(self.depth)

    # 停止
    def stop(self):
        self.MANAGER.stop()
        pass

    def get_symbol(self):
        # 获取品种名
        if "binance" in self.platform:
            return self.inst_type.upper()
        if "okex" in self.platform:
            return self.inst_type.lower()

    def get_channel(self):
        if self.freq == "tick":  # tick 数据源
            return f"{self.platform}_{self.inst_type}_aggTrade_{self.symbol}"
        else:
            return f"{self.platform}_{self.inst_type}_{self.freq}_{self.symbol}"

    def get_depth_channel(self):
        return f"{self.platform}_{self.inst_type}_depthUpdate_{self.symbol}"

    def get_openint_channel(self):
        return f"{self.platform}_{self.inst_type}_oi_{self.symbol}"
