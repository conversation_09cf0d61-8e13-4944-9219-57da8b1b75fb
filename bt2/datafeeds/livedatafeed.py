from logging import getLogger
from ..datafeed import HistoricAndLiveDataFeed
from .dataloader import (
    OneWebSocketDataLoader,
    OneRedisSubscriberThread,
    )

logger = getLogger(__name__)



class OneWSRedisDataFeed(HistoricAndLiveDataFeed):
    """
    实时数据datafeed, 通过websocket获取历史数据, 通过redis订阅获取实时数据
    使用单个对象的ws和redis
    """
    HistoricLoader = OneWebSocketDataLoader
    LiveLoader = OneRedisSubscriberThread

