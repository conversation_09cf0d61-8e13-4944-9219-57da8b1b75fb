## mock数据源


from ..datafeed import DataBase2
from ..utils import ts2num
import threading
from queue import Queue
import collections 
from itertools import zip_longest
import time
import datetime
import backtrader as bt




class UserDefinedDataFeed(DataBase2):
    """
    根据自定义数组和参数构造数据源
    Args:
        input (dict): 自定义的输入, key表示数据属性, 比如open, datetime, value为对应数据的数组
        interval (float): 数据发送间隔
    """
    params = (
        ('input', {}), 
        ('interval', -1),
    )
    def __init__(self):
        super().__init__()
        self._stop = False
        self._data_input = collections.deque(
            [dict(zip(self.p.input.keys(), values)) for values in zip_longest(*self.p.input.values())]
        )
        self._data_queue = Queue()
        self._data = None
        pass

    def start(self):
        # 启动一个新的线程定时投放数据
        self._laststatus = self.LIVE
        if self.p.interval > 0:
            self._autorun = True
            self._thread_task = threading.Thread(target=self._put_data_into_queue)
            self._thread_task.start()
        else:
            self._autorun = False
        self._tz = None
        self._tzinput = None
        self.started = True
        
        
    def stop(self):
        self._laststatus = self.DISCONNECTED
        self._thread_task.join()
    
    def _put_data_into_queue(self):
        while self._laststatus == self.LIVE:
            if self._data_input:
                self._data_queue.put(self._data_input.popleft())
                time.sleep(self.p.interval)
            else:
                self._laststatus = self.DISCONNECTED
    
    def _load(self):
        if not self._autorun:
            if self._data_input:
                self._data_queue.put(self._data_input.popleft())
            else:
                pass
        else:
            pass
        try:
            data = self._data_queue.get(timeout=1)
            now_dt = bt.date2num(datetime.datetime.now())
            # 基础数据填充
            self.lines.datetime[0] = data.pop('datetime', now_dt)
            self.lines.open[0] = data.pop('open', 0)
            self.lines.high[0] = data.pop('high', 0)
            self.lines.low[0] = data.pop('low', 0)
            self.lines.close[0] = data.pop('close', 0)
            self.lines.volume[0] = data.pop('volume', 0)
    
            self.curr_dt = ts2num(data.pop('lt', 0))
    
            # 其余数据作为状态量保存
            self._data = data
    
            self._tick_fill()
    
            return True
        except Exception as e:
            return False

    def get_user_data(self, key):
        # 获取自定义的data数值
        return self._data[key]