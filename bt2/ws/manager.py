## 事件型websocket

import threading
import requests
import websocket
import json
import queue
from logging import getLogger
from ..const import Exchange
import traceback
import backoff
from urllib.parse import urlencode
from ..manager import ManagerBase

logger = getLogger(__name__)


TIMEOUT = 10  # 超时秒数
MAX_TRIES = 20  # 最大重试次数


class KLineWSManager(ManagerBase):
    """
    k线websocket管理器
    每个线程中的管理器都有独立的属性
    """

    params = {
        "uri": None,
    }

    ws_app = None

    @classmethod
    def get_ws(cls, kline_queue: queue.Queue, opened_event: threading.Event):
        """
        获取ws对象
        ws对象启动在一个单独的线程中
        输入一个线程队列, 通过on_message方法将信息放入队列中
        """
        on_message = cls._get_on_message(kline_queue)
        ws_app = websocket.WebSocketApp(
            cls.p.uri,
            on_open=lambda ws: opened_event.set(),
            on_message=on_message,
        )
        thread = threading.Thread(target=ws_app.run_forever)
        thread.daemon = True  # 设置为守护线程，确保主程序退出时线程也会退出
        thread.start()
        return ws_app

    @classmethod
    def _get_on_message(cls, kline_queue):
        """生成on_message方法"""

        def on_message(ws, message):
            try:
                if message == "pong":
                    return
                if message == "ping":
                    ws.send("pong")
                    return
                resp = json.loads(message)
                for d in resp["data"]:
                    kline_queue.put(d)
                if resp["msg"] == "success":
                    # 结尾放入None
                    if kline_queue.empty():
                        logger.warning("无历史数据传输")
                    kline_queue.put(None)
                    # 接收完毕, 关闭ws
                    # ws.close()
                    return
            except Exception as e:
                logger.exception(e)
                traceback.print_exc()

        return on_message


class WSManger(ManagerBase):
    """
    WebSocket 管理器
    通过子线程管理
    """

    _ws_app = None  # ws对象
    _thread = None  # 子线程, 只保持有一个子线程

    def __exit__(self, exc_type, exc_value, traceback):
        cls = self.__class__
        if cls._ws_app != None:
            cls._ws_app.close()
            cls._thread.join()
            cls._ws_app = None
            cls._thread = None


class BrokerWSManager(WSManger):
    """
    broker websocket管理器
    """

    params = dict(
        uri=None,
        # apikey = None,
        # apisecret = None,
        account_id=None,
        symbols=None,
    )
    _platform = None  # 平台
    PING_INTERVAL = 300  # ping帧发送间隔
    connected_event = threading.Event()  # 连接开关

    # @classmethod
    # def set_account(cls, apikey, apisecret, *args, **kwargs):
    #     """设置账户信息"""
    #     cls(apikey=apikey, apisecret=apisecret)

    @classmethod
    def set_account(cls, account_id, *args, **kwargs):
        """设置账户信息"""
        cls(account_id=account_id)

    @classmethod
    def set_symbols(cls, symbols):
        """设置品种信息"""
        cls(symbols=symbols)

    @classmethod
    def get_url(cls):
        return (
            cls.p.uri
            + "?"
            + urlencode(
                {"symbol": cls.p.symbols, "accountId": cls.p.account_id}, doseq=True
            )
        )

    @classmethod
    def get_ws(cls, broker_controller):
        """
        获取ws对象
        ws对象启动在一个单独的线程中
        输入一个broker控制器对象, 该对象包含了broker实例和一些操作方法,
        通过该控制器实现对broker的状态维护
        """
        cls._ws_app = websocket.WebSocketApp(
            cls.get_url(),
            on_message=cls._get_on_message(broker_controller),
            on_open=cls._get_on_open(),
            on_close=cls._get_on_close(),
            on_error=cls._get_on_error(),
        )
        cls._connect()
        return cls._ws_app

    @classmethod
    def _connect(cls):
        cls._thread = threading.Thread(
            target=cls._ws_app.run_forever,
            kwargs=dict(ping_interval=cls.PING_INTERVAL, ping_timeout=TIMEOUT),
        )
        cls._thread.daemon = True  # 设置为守护线程，确保主程序退出时线程也会退出
        cls._thread.start()
        logger.info("%s 等待连接websocket...", cls.get_url())
        if not cls.connected():  # 等待启动超时
            cls._ws_app.close()
            raise TimeoutError(f"broker {cls._platform} 连接websocket超时 {TIMEOUT} s")
        logger.info(f"{cls._platform} 连接websocket成功")

    @classmethod
    def _get_on_open(cls):
        """生成on_message方法, 需要打开连接开关"""

        def on_open(ws, code, msg):
            logger.info(f"{cls._platform} websocket连接成功 {msg}")
            cls.connected_event.set()

        return on_open

    @classmethod
    def _get_on_close(cls):
        def on_close(ws, code, msg):
            logger.info(f"{cls._platform} websocket连接关闭 {code} {msg}")
            cls.connected_event.clear()

        return on_close

    @classmethod
    def _get_on_error(cls):
        def on_error(ws, error):
            logger.warning(f"{cls._platform} websocket连接错误: {error}")
            cls.connected_event.clear()

        return on_error

    @classmethod
    @backoff.on_exception(
        backoff.expo,
        TimeoutError,
        max_tries=MAX_TRIES,
    )
    def reconnect(cls, broker_controller):
        """重连"""
        logger.info("准备重试")
        cls._thread.join()
        return cls.get_ws(broker_controller)

    @classmethod
    def connected(cls):
        # 检查是否有连接
        return cls.connected_event.wait(timeout=TIMEOUT)

    @classmethod
    def check_connection(cls):
        return cls.connected()


class BinanceBrokerWSManager(BrokerWSManager):
    """
    币安 broker websocket管理器
    """

    ConfigKey = "BROKER_WS"

    _platform = Exchange.BINANCE
    connected_event = threading.Event()  # 连接开关

    @classmethod
    def _get_on_open(cls):
        """币安增加登录信息的发送"""

        # LoginMessage = cls._login_message()
        def on_open(ws):
            cls.connected_event.set()
            # ws.send(LoginMessage)

        return on_open

    @classmethod
    def _get_on_message(cls, controller):
        def on_message(ws, message):
            try:
                logger.info(f"币安 ws 接收信息 {message}")
                with controller.lock:
                    message = json.loads(message)
                    event_type = message.get("e", "")
                    if event_type == "ACCOUNT_UPDATE":
                        # 更新账户信息
                        controller.update_account(message)
                        # 更新仓位信息
                        controller.update_positions(message)
                    elif event_type == "ORDER_TRADE_UPDATE":
                        # 订单更新
                        controller.update_orders(message)
                    elif event_type == "ACCOUNT_CONFIG_UPDATE":
                        # 杠杆倍数更新
                        controller.update_lever(message)
                    else:
                        logger.warning("未分类逻辑: %s", message)
            except Exception as e:
                logger.exception(e)

        return on_message

    # @classmethod
    # def _login_message(cls):
    #     """币安生成登录信息"""
    #     return json.dumps({
    #         'apikey': cls.p.apikey,
    #         'secretkey': cls.p.apisecret,
    #         'symbols': cls.p.symbols,
    #     })


class OkexBrokerWSManager(BrokerWSManager):
    """
    欧意 broker websocket管理器
    """

    _platform = Exchange.OKEX
    connected_event = threading.Event()  # 连接开关

    @classmethod
    def _get_on_message_okex(cls, controller):
        """生成on_message方法"""

        def on_message(ws, message):
            try:
                logger.debug(f"ws 接收信息: {message}")
                # 获取线程锁
                with controller.lock:
                    # 通过调用broker中的方法来更新broker
                    message = json.loads(message)
                    channel = message["arg"]["channel"]
                    data = message["data"]
                    if channel == "account":
                        # 更新账户信息
                        controller.update_account(data)
                        logger.info("账户更新成功")
                        return
                    if channel == "positions":
                        # 更新仓位信息
                        controller.update_positions(data)
                        logger.info("仓位更新成功")
                        return
                    if channel == "orders":
                        controller.update_orders(data)
                        logger.info("订单更新成功")
                        return
            except Exception as e:
                logger.exception(e)

        return on_message


class BinanceRiskWSManager(BrokerWSManager):
    """
    币安风控 websocket管理器
    """

    params = {
        "login_uri": None,
        "phone": None,
        "passwd": None,
    }

    @classmethod
    def get_ws(cls, broker_controller):
        """
        获取ws对象
        ws对象启动在一个单独的线程中
        输入一个broker控制器对象, 该对象包含了broker实例和一些操作方法,
        通过该控制器实现对broker的状态维护
        """
        token = cls._get_token()
        uri = cls.p.uri + "?auth=" + token
        cls._ws_app = websocket.WebSocketApp(
            uri,
            on_message=cls._get_on_message(broker_controller),
            on_open=cls._get_on_open(),
            on_close=cls._get_on_close(),
            on_error=cls._get_on_error(),
        )
        cls._connect()
        return cls._ws_app

    @classmethod
    def _get_token(cls):
        resp = requests.post(
            cls.p.login_uri,
            data={
                "phone": cls.p.phone,
                "passwd": cls.p.passwd,
            },
        ).json()
        assert resp["message"] == "ok", f"请求风控token失败, {resp.text()}"
        return resp["data"]["access_token"]

    @classmethod
    def _get_on_message(cls, controller):
        def on_message(ws, message):
            try:
                logger.info("risk ws 接收信息: %s", message)
                # 通过调用broker中的方法来更新broker
                message = json.loads(message)
                if message.get("event", None) == "ping":
                    ws.send(json.dumps({"event": "pong"}))
                    return
                if message["type"] == "stoptrade":
                    controller.trigger_risk()
                    logger.warning("账号 %s 触发风控", cls.phone)
                    return
                if message["type"] == "allowtrade":
                    controller.releas_risk()
                    logger.warning("账号 %s 解除风控", cls.phone)
                    return
            except Exception as e:
                logger.exception(e)

        return on_message


class MultiKLineWSManager(WSManger):
    """
    多组k线websocket管理器
    全局只保存一个ws对象
    """

    ConfigKey = "KLINE_WS"

    params = {
        "uri": None,
    }

    CHANNEL_QUEUE = {}  # 队列字典
    OPENED_EVENTS = []  # 事件列表

    def __exit__(self, exc_type, exc_value, traceback):
        super().__exit__(exc_type, exc_value, traceback)
        cls = self.__class__
        cls.CHANNEL_QUEUE = {}
        cls.OPENED_EVENTS = []

    @classmethod
    def get_ws(
        cls, channel: str, kline_queue: queue.Queue, opened_event: threading.Event
    ):
        """
        获取ws对象, 如果已经有一个ws对象, 则返回现有的ws对象
        ws对象启动在一个单独的线程中
        输入一个线程队列, 通过on_message方法将信息放入队列中
        Args:
            channel: 频道名, 用于区分队列对象
            kline_queue: 队列对象
            opened_event: 线程事件
        """
        if channel in cls.CHANNEL_QUEUE:
            raise KeyError(f"{channel} 已存在于ws现有队列, 检查数据源是否重复设置")
        cls.CHANNEL_QUEUE[channel] = kline_queue
        cls.OPENED_EVENTS.append(opened_event)
        if cls._ws_app:
            # 已存在则直接返回一个ws
            opened_event.set()
            return cls._ws_app
        else:
            on_message = cls._get_on_message()
            cls._ws_app = websocket.WebSocketApp(
                cls.p.uri,
                on_open=lambda ws: [event.set() for event in cls.OPENED_EVENTS],
                on_message=on_message,
            )
            cls._thread = threading.Thread(target=cls._ws_app.run_forever)
            cls._thread.daemon = True  # 设置为守护线程，确保主程序退出时线程也会退出
            cls._thread.start()
            logger.info("ws线程启动完成")
        return cls._ws_app

    @classmethod
    def _get_on_message(cls):
        """生成on_message方法"""

        def on_message(ws, message):
            try:
                if message == "pong":
                    return
                if message == "ping":
                    ws.send("pong")
                    return
                resp = json.loads(message)
                channel = resp["channel"]
                kline_queue = cls.CHANNEL_QUEUE[channel]
                for d in resp["data"]:
                    kline_queue.put(d)
                if resp["msg"] == "success":
                    # 结尾放入None
                    if kline_queue.empty():
                        logger.warning("无历史数据传输")
                    kline_queue.put(None)
                    # 接收完毕, 关闭ws
                    # ws.close()
                    return
            except Exception as e:
                logger.exception(e)
                traceback.print_exc()

        return on_message
