### 订单相关异常



class OrderRespError(Exception):
    """
    订单响应异常
    """

    MESSAGE = 'Order Response Error'

    def __init__(self, code):
        super().__init__(self.MESSAGE)
        self.code = code

    def __str__(self):
        return f"[Error Code {self.code}] {self.args[0]}"
    

class MarginOrderRespError(OrderRespError):
    """
    订单保证金不足
    """
    MESSAGE = '保证金不足'


class ReduceOnlyRejectOrderRespError(OrderRespError):
    """
    减仓订单拒绝
    """
    MESSAGE = '减仓订单被拒绝, 检查是否已经平仓'


class CancelRejectOrderRespError(OrderRespError):
    """
    取消订单拒绝
    """
    MESSAGE = '取消订单被拒绝, 检查订单当前状态'


class MaxLeverageRatioOrderRespError(OrderRespError):
    """
    杠杆超限
    """
    MESSAGE = '挂单或持仓超出当前初始杠杆下的最大值'