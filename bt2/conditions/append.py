## 追单

from bt2.condition import Condition
import backtrader as bt
from ..indicators import *


class AppendCond(Condition):
    """
    追单逻辑
    """

    alias = ("AppendCond",)

    plotinfo = {
        "plot": False,
        "b_plot": False,
    }


class SARAboveAppendCond(AppendCond):
    """
    sar反转于价格之上, 且当前价值不超过最大价值限制时追仓, 仓位根据当前价和开仓价调整
    """

    params = (("open_value_rate", 50),)

    def __init__(self):
        self.sar = bt.indicators.ParabolicSAR()
        self.sar_switch = Switch(Lt(self.data, self.sar))
        self.bar_opened = BarOpened()

    def next(self):
        cashbalance = self.broker.get_cashbalance()
        cash = self.broker.get_cash()
        if cashbalance == 0 or cash / cashbalance * 100 < 100 - self.p.open_value_rate:
            return False

        open_size = self.broker.get_size()
        if (
            self.sar_switch[0]
            and not self.bar_opened[0]
            and open_size
            and self.broker.no_order()
        ):
            if self.broker.get_open_price() < self.data.close[0]:
                # 亏损
                size = open_size / 2
            else:
                # 盈利
                size = open_size / 4
            self._owner.buy(
                side="short",
                size=size,
                exectype="Limit",
                priceMatch="OPPONENT_5",
                timeInForce="IOC",
            )
            return True
        return False
