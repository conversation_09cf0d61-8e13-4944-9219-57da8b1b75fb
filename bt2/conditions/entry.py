## 进入条件

from bt2.condition import Condition
from ..indicators import *


class EntryCond(Condition):
    """进入条件
    Args:
        check_order (bool): 检查是否有存活订单
        check_pos (bool):  检查是否有持仓
        check_live (bool): 实时数据进行计算的进入条件, 当数据源为实时数据源且状态为读取实时数据时才进行判断, 否则视为满足条件
    """

    alias = ("EntryCond",)  # 别名

    params = (
        ("check_order", False),
        ("check_pos", False),
        ("check_live", False),
    )

    no_log_key = {
        "side",
        "simulate",
        "check_order",
        "check_pos",
        "check_live",
    }

    def submit_open_order(self):
        # 提交开仓订单
        # 有订单提交返回True, 否则返回False
        return False

    def open_log_string(self):
        return f"open_type: {self.get_name()}"

    def entry_log_string(self, **kwargs):
        # 默认log字符串, 不主动打印
        ind_dict = self.get_indicator_dict()
        ind_value_string = [f"{attr}: {ind[0]}" for attr, ind in ind_dict.items()]
        # params
        params_args = [
            f"{key}: {value}"
            for key, value in self.params._getkwargs().items()
            if key not in self.no_log_key
        ]
        extra_args_string = [
            f"{key}: {value}" for key, value in self._extra_args.items()
        ]
        other_args = [f"{key}: {value}" for key, value in kwargs.items()]
        return ", ".join(
            ind_value_string + extra_args_string + other_args + params_args
        )

    def trigger_next(self):
        if self.p.check_live and not (
            self.data.islive() and self.data.is_live_status()
        ):  # 实时数据检查 & 非实时数据, 跳过
            return True
        if self.p.check_order and not self.broker.no_order():  # 订单检查 & 有订单, 跳过
            return False
        if (
            self.p.check_pos and not self.broker.no_open_position()
        ):  # 仓位检查 & 有仓位, 跳过
            return False
        return self.next()


class ManualEntryCond(Condition):
    """进入条件"""

    alias = ("ManualEntryCond",)  # 别名

    def submit_open_order(self):
        # 提交开仓订单
        # 有订单提交返回True, 否则返回False
        return False

    def open_log_string(self):
        return f"open_type: {self.get_name()}"

    def entry_log_string(self, **kwargs):
        # 默认log字符串, 不主动打印
        ind_dict = self.get_indicator_dict()
        ind_value_string = [f"{attr}: {ind[0]}" for attr, ind in ind_dict.items()]
        # params
        params_args = [
            f"{key}: {value}" for key, value in self.params._getkwargs().items()
        ]
        extra_args_string = [
            f"{key}: {value}" for key, value in self._extra_args.items()
        ]
        other_args = [f"{key}: {value}" for key, value in kwargs.items()]
        return ", ".join(
            ind_value_string + extra_args_string + other_args + params_args
        )

    def trigger_next(self):
        if self.broker.is_open_position():  # 有仓位
            res = self.next()
        else:
            res = False
        return res


class StackedEntryCond(EntryCond):
    """允许多次开仓"""

    alias = ("StackedEntryCond",)

    params = (("check_pos", False),)


class LiveEntryCond(EntryCond):
    """
    实时数据进入条件
    """

    alias = ("LiveEntryCond",)  # 别名

    params = (("check_live", True),)


class MACrossShortEntryCond(EntryCond):
    """
    均线空头,并且近期呈发散排列
    """

    alias = ("MACrossShortEntryCond",)

    params = (("ma_periods", (3, 5, 7)),)

    def __init__(self):
        self.mas = [
            bt.indicators.SMA(self.data, period=period) for period in self.p.ma_periods
        ]
        self.ma_order = MultiDatasOrder(*self.mas, asc=True)

    def next(self):
        if len(self.mas) != 3:
            return False
        ma5 = self.mas[0]
        ma20 = self.mas[1]
        ma60 = self.mas[2]
        return (
            self.ma_order[0]
            and ma5[-1] > ma20[-1]
            and ma20[-1] < ma60[-1]
            and ma5[-2] > ma20[-2]
            and ma20[-2] < ma60[-2]
            and ma5[-3] > ma20[-3]
            and ma20[-3] < ma60[-3]
            and ma5[-4] > ma20[-4]
            and ma20[-4] < ma60[-4]
            and ma5[-4] > ma5[-3] > ma5[-2] > ma5[-1] > ma5[0]
        )
