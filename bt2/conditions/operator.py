"""
逻辑条件
"""

from bt2.condition import Condition
from ..indicators import *


class AndCond(Condition):
    """
    and
    """

    def __init__(self):
        self.lines.value = And(*self.datas)

    def next(self):
        return self.lines.value[0]

    def entry_log_string(self, **kwargs):
        # 默认log字符串, 不主动打印
        strings = [data.entry_log_string(**kwargs) for data in self.datas]
        return ", ".join(strings)

    def exit_log_string(self, **kwargs):
        # 默认log字符串, 不主动打印
        strings = [data.exit_log_string(**kwargs) for data in self.datas]
        return ", ".join(strings)


class OrCond(Condition):
    """
    or
    """

    def __init__(self):
        self.lines.value = Or(*self.datas)

    def next(self):
        return self.lines.value[0]

    def entry_log_string(self, **kwargs):
        # 默认log字符串, 不主动打印
        for data in self.datas:
            if data[0] == 1:
                return data.entry_log_string(chosen_cond=data.alias[0], **kwargs)
        return "no_chosen"

    def exit_log_string(self, **kwargs):
        # 默认log字符串, 不主动打印
        for data in self.datas:
            if data[0] == 1:
                return data.exit_log_string(chosen_cond=data.alias[0], **kwargs)
        return "no_chosen"
