## 退出条件

from bt2.condition import Condition
from ..indicators import *


class ExitCond(Condition):
    """
    退出条件
    Args:
        check_live (bool): 实时数据源检查
        keep_exit (bool): 持续退出
    """

    alias = ("ExitCond",)  # 别名

    no_log_key = {"side", "simulate", "keep_exit", "check_live"}

    params = (
        ("check_live", False),
        ("keep_exit", False),
    )

    def submit_close_order(self):
        # 提交平仓订单
        # 有订单提交返回True, 否则返回False
        return False

    def exit_log_string(self, **kwargs):
        # 默认log格式
        # trigger, close, open_price, size, lever, holding_period,
        ind_dict = self.get_indicator_dict()
        ind_value_string = ", ".join(
            [f"{attr}: {ind[0]}" for attr, ind in ind_dict.items()]
        )
        open_price = self.broker.get_open_price()
        trigger_log_string = f"trigger_exit_condition: {self.alias[0]}"
        info_log_string = f"close: {self._owner.data.close[0]}, open_price: {open_price}, size: {self.broker.get_size()}, lever: {self.broker.get_lever()}"
        # observer
        observer_args = [
            f"{linealias}: {line[0]}"
            for key, obs in self._owner.stats.getitems()
            for line, linealias in zip(obs.lines, obs.getlinealiases())
        ]
        # params
        params_args = [
            f"{key}: {value}"
            for key, value in self.params._getkwargs().items()
            if key not in self.no_log_key
        ]
        extra_args = [f"{key}: {value}" for key, value in self._extra_args.items()]
        other_args = [f"{key}: {value}" for key, value in kwargs.items()]
        return ", ".join(
            [trigger_log_string, info_log_string, ind_value_string]
            + other_args
            + observer_args
            + params_args
            + extra_args
        )

    def close_log_string(self):
        return f"close_type: {self.get_name()}"

    def trigger_next(self):
        if self.broker.no_open_position():  # 无仓位
            self.update_extra_args(exit=False, keep_exit=False)  # 重置flag
            return False
        if self.p.check_live and not (
            self._owner.data.islive() and self._owner.data.is_live_status()
        ):  # 实时检查
            return False
        if self.p.keep_exit:  # 持续退出逻辑
            if self.get_extra_args("exit"):  # 已触发退出, 持续退出
                self.update_extra_args(keep_exit=True)
                return True
            if self.broker.is_open_position():  # 首次判断退出 且 已开仓
                res = self.next()
            else:  # 未开仓则跳过
                res = False
            self.update_extra_args(exit=res)
            if res == True:  # 首次触发, 取消所有订单
                self.broker.cancel_all_orders()
        else:
            res = self.next()
        return res


class KeepTillCloseExitCond(ExitCond):
    """
    触发后持续退出直至平仓的退出条件
    首次触发会执行取消订单
    """

    params = (("keep_exit", True),)


class LiveKeepTillCloseExitCond(ExitCond):
    """
    实时数据进行计算的退出条件
    当数据源为实时数据源且状态为读取实时数据时才进行判断, 否则视为不满足条件
    减少历史数据源不必要的计算
    """

    params = (
        ("check_live", True),
        ("keep_exit", True),
    )


class LiveExitCond(ExitCond):
    params = (("check_live", True),)
