## 平仓

from bt2.condition import Condition
from ..indicators import *


class CloseOrderCond(Condition):
    """
    平仓
    """

    plotinfo = {
        "plot": False,
        "b_plot": False,
    }

    alias = ("CloseOrderCond",)  # 别名

    def close_log_string(self):
        ind_dict = self.get_indicator_dict()
        ind_value_string = ", ".join(
            [f"{attr}: {ind[0]}" for attr, ind in ind_dict.items()]
        )
        return f"close_type: {self.alias[0]}, {ind_value_string}, close_order: {self.order}"

    def submit_close_order(self, trigger=None):
        NotImplemented
