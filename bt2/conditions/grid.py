## 网格

from . import AppendCond, StopCond
from ..indicators import *
from bt2.utils import num2ts
from queue import deque
import bisect
import time


class GridCondition(AppendCond):
    """
    网格策略条件
    针对网格参数进行下单, 包括开仓单和平仓单
    Args:
        value_ratio (float): 占用资金比例
        upper_price (float): 价格上限
        lower_price (float): 价格下限
        num (int): 网格数量
        mode (arithmetic|geometric)
        movable (bool): 是否移动网格
    TODO:
        在一些极端情况下, 会导致双向持仓, 从而引起策略报错
    """

    alias = ("GridCondition",)

    ARITHMETIC, GEOMETRIC = range(2)

    params = (
        ("trigger_price", None),
        ("value_ratio", 0.5),
        ("upper_price", 0),
        ("lower_price", 0),
        ("num", 10),
        ("mode", "arithmetic"),
        ("movable", False),
    )

    def __init__(self):
        # 模式
        self._mode = self._get_mode()
        # 网格数量
        self._grid_num = self.p.num
        # 订单价值
        self._order_value = (
            self.broker.get_cashbalance()
            * self.broker.get_lever()
            * self.p.value_ratio
            / self._grid_num
        )
        # 步长
        self._step: float = 0
        # 网格列表
        self._price_grid: list = []
        if self._mode == self.ARITHMETIC:
            self._step = (self.p.upper_price - self.p.lower_price) / self.p.num
            self._price_grid = [
                self.p.lower_price + self._step * n for n in range(self._grid_num)
            ]
        if self._mode == self.GEOMETRIC:
            self._step = (self.p.upper_price / self.p.lower_price) ** (1 / self.p.num)
            self._price_grid = [
                self.p.lower_price * self._step * n for n in range(self._grid_num)
            ]
        # 网格订单
        self._order_grid = [None for i in range(self.p.num)]
        # 起始索引
        self._start_idx = None
        # 触发价格设置
        if self.p.trigger_price:
            self.trigger_state = LivePriceCrossEqValue(price=self.p.trigger_price)
        else:
            self.trigger_state = KeepState(default=1)

    def next(self):
        if self._start_idx == None and self.trigger_state[0]:
            # 判断是否初始化网格
            self._init_grid()
        else:
            # 执行网格逻辑
            self._fix_grid()
        return False

    def _init_grid(self):
        """
        初始化网格订单
        确保实时性
        """
        if abs(time.time() * 1000 - num2ts(self.data.get_curr_dt())) > 2000:
            self.logger.info("实时数据延迟超过2s, 跳过初始化网格订单")
            return
        curr_price = self.data.close[0]
        # 确定仓位
        self._order_size = self._order_value / curr_price
        # 确定起始线
        idx = bisect.bisect_left(self._price_grid, curr_price)
        if idx == len(self._price_grid):
            self._start_idx = idx - 1
        elif idx == 0:
            self._start_idx = idx
        elif (
            self._price_grid[idx] - curr_price > curr_price - self._price_grid[idx - 1]
        ):
            self._start_idx = idx - 1
        else:
            self._start_idx = idx
        self.logger.info("初始化订单...")
        for i in range(0, self._start_idx):
            # 买单方向
            self._order_grid[i] = self._owner.buy(
                exectype="Limit",
                side="long",
                price=self._price_grid[i],
                size=self._order_size,
                timeInForce="GTC",
                ignore_dt=True,
            )

        for i in range(self._start_idx + 1, self._grid_num):
            # 卖单方向
            self._order_grid[i] = self._owner.sell(
                exectype="Limit",
                side="short",
                price=self._price_grid[i],
                size=self._order_size,
                timeInForce="GTC",
                ignore_dt=True,
            )
        self.logger.info("初始化订单完成")

    def _fix_grid(self):
        """
        填补网格订单
        当网格中的订单成交后, 按照规则填补网格中的订单
        """
        _new_orders = [
            None for i in range(self._grid_num)
        ]  # 新下订单列表, 保存本论下的订单
        for i in range(self._grid_num):
            order = self._order_grid[i]
            if order is None:  # 空闲线
                continue
            if order.alive():  # 存活订单
                continue
            if order.is_completed():
                # 已完成订单, 当前订单位置重置为None
                self._order_grid[i] = None
                if order.is_long() and order.is_buy():
                    # 买多成交, 上方执行卖多平仓挂单
                    _new_orders[i + 1] = new_order = self._owner.sell(
                        exectype="Limit",
                        side="long",
                        price=self._price_grid[i + 1],
                        size=self._order_size,
                        timeInForce="GTC",
                        trigger="grid",
                        ignore_dt=True,
                    )
                if order.is_short() and order.is_sell():
                    # 卖空成交, 下方执行买空平仓订单
                    _new_orders[i - 1] = new_order = self._owner.buy(
                        exectype="Limit",
                        side="short",
                        price=self._price_grid[i - 1],
                        size=self._order_size,
                        timeInForce="GTC",
                        trigger="grid",
                        ignore_dt=True,
                    )
                if order.is_long() and order.is_sell():
                    # 卖多成交, 下方执行买多开仓订单
                    _new_orders[i - 1] = new_order = self._owner.buy(
                        exectype="Limit",
                        side="long",
                        price=self._price_grid[i - 1],
                        size=self._order_size,
                        timeInForce="GTC",
                        ignore_dt=True,
                    )
                if order.is_short() and order.is_buy():
                    # 买空成交, 上方执行卖空开仓订单
                    _new_orders[i + 1] = new_order = self._owner.sell(
                        exectype="Limit",
                        side="short",
                        price=self._price_grid[i + 1],
                        size=self._order_size,
                        timeInForce="GTC",
                        ignore_dt=True,
                    )
                self.logger.info("创建订单: %s", new_order)
                continue
            self.logger.warning("网格订单状态异常, order: %s", order)
            if order.issell():
                self._order_grid[i] = re_order = self._owner.sell(
                    exectype="Limit",
                    side=order.side,
                    price=order.price,
                    size=order.size,
                    timeInForce="GTC",
                    ignore_dt=True,
                )
            else:
                self._order_grid[i] = re_order = self._owner.buy(
                    exectype="Limit",
                    side=order.side,
                    price=order.price,
                    size=order.size,
                    timeInForce="GTC",
                    ignore_dt=True,
                )
            self.logger.info("重新下单: %s", re_order)
        # 合并新旧订单
        for i in range(self._grid_num):
            if self._order_grid[i] is None and _new_orders[i] is not None:
                self._order_grid[i] = _new_orders[i]

    def _get_mode(self):
        if self.p.mode.lower() == "arithmetic":
            return self.ARITHMETIC
        elif self.p.mode.lower() == "geometric":
            return self.GEOMETRIC
        raise KeyError("invalid mode: %s", self.p.mode)


class MovableGridCondition(AppendCond):
    """
    移动网格策略条件
    针对网格参数进行下单, 包括开仓单和平仓单
    Args:
        value_base (float): 资金取整基数, 计算仓位时按照基数取整
        value_ratio (float): 占用资金比例
        upper_price (float): 价格上限
        lower_price (float): 价格下限
        mid_price (float|None): 中性价格. 未设置中性价格时, 当前价格为中性价格
        num (int): 网格数量
        order_num (int|None): 下单数量. 未设置下单数量时, 下单数量根据网格数量决定
        mode (arithmetic|geometric)
        movable (bool): 是否移动网格, True时, 突破网格会增加网格线; False时, 突破网格会直接平仓终止程序
    """

    alias = ("MovableGridCondition",)

    ARITHMETIC, GEOMETRIC = range(2)

    params = (
        ("used_margin", None),
        ("order_value", 100),
        ("value_base", 10),
        ("value_ratio", 0.5),
        ("upper_price", 0),
        ("lower_price", 0),
        ("mid_price", 0),
        ("trigger_price", None),
        ("num", 10),
        ("order_num", 10),
        ("mode", "arithmetic"),
        ("movable", False),
    )

    def __init__(self):
        # 模式
        self._mode = self._get_mode()
        # 初始化完成标志
        self._init_finish = False
        # 结束标志
        self._stop_flag = False
        # 无效订单标志
        self._except_order = None
        # 网格有效上下限
        self._start_idx, self._end_idx = 0, self.p.order_num
        # 订单仓位
        self._order_size = 0
        # 调整价格函数
        self._f_raise, self._f_reduse = None, None
        # 中性价格, 所处位置
        self._mid_price, self._mid_idx = 0, 0
        # 下单价格列表
        self._price_grid = deque([self.p.lower_price])
        # 订单列表, 对应初始下单价格列表
        # None表示空位, True表示占位, Order为实际订单
        self._order_grid = deque([None for i in range(self.p.num)])
        self._prev_price = None

    def next(self):
        if not self._init_finish:
            # 初始化网格
            self._start_init()
            return False
        if not self._stop_flag:
            # 调整网格
            self._move_grid()
            # 执行网格逻辑
            self._fix_grid()
            return False
        if self._stop_flag:
            # 停止策略
            self._stop_grid()
            return False
        return False

    def _start_init(self):
        """
        初始化网格
        确保实时性
        """
        if abs(time.time() * 1000 - num2ts(self.data.get_curr_dt())) > 2000:
            self.logger.info("实时数据延迟超过2s, 跳过初始化网格订单")
            return
        if self.p.trigger_price and not self.broker.get_size():
            # 设置了触发价格且无仓位
            if not self._prev_price:
                self._prev_price = self.data.close[0]
                return
            if (self.p.trigger_price - self.data.close[0]) * (
                self.p.trigger_price - self._prev_price
            ) > 0:
                self._prev_price = self.data.close[0]
                return
        # 中性价格
        self._init_mid_price()
        # 订单仓位
        self._init_order_size()
        # 调整价格函数
        self._init_step_func()
        # 实际下单数量
        self._order_num = self.p.order_num if self.p.order_num else self.p.num
        # 初始下单价格列表
        self._init_price_grid()
        # 中性价格所处位置
        self._mid_idx = min(
            bisect.bisect_left(self._price_grid, self._mid_price),
            len(self._price_grid) - 1,
        )
        # 调整仓位
        self._init_open_size()
        # 创建订单
        self._init_orders()
        # 结束初始化
        self._init_finish = True

    def _init_mid_price(self):
        """
        初始化中性价格
        """
        self._mid_price = self.p.mid_price if self.p.mid_price else self.data.close[0]
        return

    def _init_order_size(self):
        """
        初始化订单仓位
        """
        if self.p.used_margin:  # 占用保证金模式
            order_value = self.p.used_margin * self.broker.get_lever() / self.p.num
        elif self.p.order_value:  # 指定订单价值模式
            order_value = self.p.order_value
        else:  # 保证金占用资金模式
            cashbalance = (
                self.broker.get_cashbalance() // self.p.value_base * self.p.value_base
            )  # 取整
            order_value = (
                cashbalance * self.broker.get_lever() * self.p.value_ratio / self.p.num
            )
        self._order_size = order_value / self._mid_price
        self.logger.info("初始化订单价值: %s, 仓位: %s", order_value, self._order_size)
        return

    def _init_step_func(self):
        # 计算函数
        if self._mode == self.ARITHMETIC:
            self._f_raise, self._f_reduse = (
                lambda x: x + (self.p.upper_price - self.p.lower_price) / self.p.num,
                lambda x: x - (self.p.upper_price - self.p.lower_price) / self.p.num,
            )
            return
        if self._mode == self.GEOMETRIC:
            self._f_raise, self._f_reduse = (
                lambda x: x
                * (self.p.upper_price / self.p.lower_price) ** (1 / self.p.num),
                lambda x: x
                / (self.p.upper_price / self.p.lower_price) ** (1 / self.p.num),
            )
            return

    def _init_price_grid(self):
        """
        初始化价格网格
        """
        for i in range(1, self.p.num):
            self._price_grid.append(self._f_raise(self._price_grid[-1]))
        return

    def _init_open_size(self):
        """
        初始化仓位
        采用对手价调整, 可能会存在部分交易
        只调整一次
        """
        self.logger.info("初始化仓位")
        curr_price = self.data.close[0]
        # 确定当前价格所处位置
        curr_idx = self._get_price_idx(curr_price)
        # 当前仓位
        curr_size = self.broker.get_size()
        # 理论仓位, 带正负(多空)
        open_size = self._order_size * (self._mid_idx - curr_idx)
        # 待调整仓位
        adjust_size = open_size - curr_size
        kwargs = dict(
            priceMatch="OPPONENT_20",
            exectype="Limit",
            timeInForce="IOC",
            ignore_dt=True,
        )
        if curr_size >= 0 and adjust_size > 0:
            ## Long 加仓
            self._owner.buy(
                side="long",
                size=adjust_size,
                **kwargs,
            )
        if curr_size <= 0 and adjust_size < 0:
            ## Short 加仓
            self._owner.sell(
                side="short",
                size=adjust_size,
                **kwargs,
            )
        if curr_size > 0 and adjust_size < 0:
            ## Long 减仓, 可能反向加仓
            if abs(curr_size) >= abs(adjust_size):
                # 只减仓
                self._owner.sell(
                    side="long",
                    size=adjust_size,
                    **kwargs,
                )
            if abs(curr_size) < abs(adjust_size):
                # 反向加仓
                self._owner.close(ignore_dt=True)
                self._owner.sell(
                    side="short",
                    size=open_size,
                    **kwargs,
                )
        if curr_size < 0 and adjust_size > 0:
            ## Short 减仓, 可能反向加仓
            if abs(curr_size) >= abs(adjust_size):
                # 只减仓
                self._owner.buy(
                    side="short",
                    size=adjust_size,
                    **kwargs,
                )
            if abs(curr_size) < abs(adjust_size):
                # 反向加仓
                self._owner.close(ignore_dt=True)
                self._owner.buy(
                    side="long",
                    size=open_size,
                    **kwargs,
                )
        return

    def _init_orders(self):
        """
        初始化订单
        """
        self.logger.info("初始化订单")
        ## 撤销现有订单
        self.broker.cancel_all_orders()
        ## 针对当前价格创建网格订单
        self._init_order_grid()

    def _init_order_grid(self):
        """
        初始化网格订单
        """
        # 调整索引
        curr_idx = self._adjust_grid_idx()
        ## 创建有效索引内的订单
        for i in range(self._start_idx, self._end_idx):
            self._idx_order(i, curr_idx)

    def _adjust_grid_idx(self):
        """
        调整有效索引, 返回当前价格索引
        """
        curr_price = self.data.close[0]
        curr_idx = self._get_price_idx(curr_price)
        ## 有效订单索引
        start_idx = curr_idx - self._order_num // 2
        end_idx = start_idx + self._order_num
        # 调整为真实索引
        self._start_idx = max(0, start_idx)
        self._end_idx = min(len(self._price_grid), end_idx)
        return curr_idx

    def _idx_order(self, i, curr_idx):
        """
        根据索引下单, 通常用于初始化下单和移动网格后下单
        """
        kwargs = dict(
            price=self._price_grid[i],
            size=self._order_size,
            exectype="Limit",
            timeInForce="GTC",
            ignore_dt=True,
        )
        if i == curr_idx:
            # 价格当前位置, 置为占位
            self._order_grid[i] = True
            return
        if i <= min(curr_idx, self._mid_idx):
            # 开多订单
            self._order_grid[i] = self._owner.buy(
                side="long",
                **kwargs,
            )
            self.logger.info(
                "开多订单:%s, i:%s, curr_idx:%s, mid_idx:%s, mid_price:%s",
                self._order_grid[i].ref,
                i,
                curr_idx,
                self._mid_idx,
                self._price_grid[self._mid_idx],
            )
            return
        if i >= max(curr_idx, self._mid_idx):
            # 开空订单
            self._order_grid[i] = self._owner.sell(
                side="short",
                **kwargs,
            )
            self.logger.info(
                "开空订单:%s, i:%s, curr_idx:%s, mid_idx:%s, mid_price:%s",
                self._order_grid[i].ref,
                i,
                curr_idx,
                self._mid_idx,
                self._price_grid[self._mid_idx],
            )
            return
        if curr_idx < i <= self._mid_idx:
            # 平多订单
            self._order_grid[i] = self._owner.sell(
                side="long",
                **kwargs,
            )
            self.logger.info(
                "平多订单:%s, i:%s, curr_idx:%s, mid_idx:%s, mid_price:%s",
                self._order_grid[i].ref,
                i,
                curr_idx,
                self._mid_idx,
                self._price_grid[self._mid_idx],
            )
            return
        if curr_idx > i >= self._mid_idx:
            # 平空订单
            self._order_grid[i] = self._owner.buy(
                side="short",
                **kwargs,
            )
            self.logger.info(
                "平空订单:%s, i:%s, curr_idx:%s, mid_idx:%s, mid_price:%s",
                self._order_grid[i].ref,
                i,
                curr_idx,
                self._mid_idx,
                self._price_grid[self._mid_idx],
            )
            return

    def _move_grid(self):
        """
        移动网格
        """
        curr_price = self.data.close[0]
        if curr_price < self._price_grid[0]:
            if not self.p.movable:
                self._stop_flag = True
                return False
            # 下移网格
            self._price_grid.appendleft(self._f_reduse(self._price_grid[0]))
            self._order_grid.appendleft(None)
            self._mid_idx += 1
            return True
        if curr_price > self._price_grid[-1]:
            if not self.p.movable:
                self._stop_flag = True
                return False
            # 上移网格
            self._price_grid.append(self._f_raise(self._price_grid[-1]))
            self._order_grid.append(None)
            return True
        return None

    def _fix_grid(self):
        """
        修补网格订单
        每次只修补一个订单, 防止双向开仓
        """
        # 调整索引
        curr_idx = self._adjust_grid_idx()
        # 撤销索引外的订单
        for i in range(0, self._start_idx):
            order = self._order_grid[i]
            if order is not None and order is not True and order.alive():
                self._owner.cancel(order)
                self._order_grid[i] = None
        for i in range(self._end_idx, len(self._order_grid)):
            order = self._order_grid[i]
            if order is not None and order is not True and order.alive():
                self._owner.cancel(order)
                self._order_grid[i] = None
        # 根据已成交的订单下新的订单
        kwargs = dict(
            exectype="Limit",
            size=self._order_size,
            timeInForce="GTC",
            ignore_dt=True,
        )
        length = len(self._order_grid)
        for i in range(self._start_idx, self._end_idx):
            order = self._order_grid[i]
            if order is True:  # 占位
                continue
            if order is None:  # 空位, 下新单
                self._idx_order(i, curr_idx)
                continue
            if order.alive():  # 存活订单
                continue
            # if order.is_rejected() or order.is_expired(): # 无效订单或拒绝订单
            #     # 无效订单通常意味着仓位出现问题, 标记异常
            #     self.logger.error('出现异常订单: %s', order)
            #     self._except_order = order.ref
            #     return
            if order.is_completed():
                # 已完成订单, 当前订单位置置为True
                self._order_grid[i] = True
                if order.is_long() and order.is_buy():
                    if i >= length - 1:
                        self.logger.warning(
                            "网格长度不足, 当前索引: %s, 网格长度: %s", i, length
                        )
                    # 买多成交, 上方执行卖多平仓挂单
                    self._order_grid[i + 1] = new_order = self._owner.sell(
                        side="long",
                        price=self._price_grid[i + 1],
                        **kwargs,
                    )
                    self.logger.info("平多订单:%s", new_order)
                if order.is_short() and order.is_sell():
                    if i <= 0:
                        self.logger.warning(
                            "网格长度不足, 当前索引: %s, 网格长度: %s", i, length
                        )
                    # 卖空成交, 下方执行买空平仓订单
                    self._order_grid[i - 1] = new_order = self._owner.buy(
                        side="short",
                        price=self._price_grid[i - 1],
                        **kwargs,
                    )
                    self.logger.info("平空订单:%s", new_order)
                if order.is_long() and order.is_sell():
                    if i <= 0:
                        self.logger.warning(
                            "网格长度不足, 当前索引: %s, 网格长度: %s", i, length
                        )
                    # 卖多成交, 下方执行买多开仓订单
                    self._order_grid[i - 1] = new_order = self._owner.buy(
                        side="long",
                        price=self._price_grid[i - 1],
                        **kwargs,
                    )
                    self.logger.info("开多订单:%s", new_order)
                if order.is_short() and order.is_buy():
                    if i >= length - 1:
                        self.logger.warning(
                            "网格长度不足, 当前索引: %s, 网格长度: %s", i, length
                        )
                    # 买空成交, 上方执行卖空开仓订单
                    self._order_grid[i + 1] = new_order = self._owner.sell(
                        side="short",
                        price=self._price_grid[i + 1],
                        **kwargs,
                    )
                    self.logger.info("开空订单:%s", new_order)
                return
            self.logger.warning("无效订单: %s", order)
            self._order_grid[i] = None
            # self._stop_flag = True
            return

    def _stop_grid(self):
        pass

    def _get_mode(self):
        """获取模式"""
        if self.p.mode.lower() == "arithmetic":
            return self.ARITHMETIC
        elif self.p.mode.lower() == "geometric":
            return self.GEOMETRIC
        raise KeyError("invalid mode: %s", self.p.mode)

    def _get_price_idx(self, price):
        """
        获取价格所处位置
        """
        # 当前价格所处位置
        price_idx = min(
            bisect.bisect_left(self._price_grid, price), len(self._price_grid) - 1
        )
        if price_idx > self._mid_idx and self._price_grid[price_idx] != price:
            price_idx -= 1
        return price_idx


class MovableGridStopCond(StopCond):
    """
    网格用停止任务
    data: MovableGridCondition
    """

    alias = ("MovableGridStopCond",)

    def next(self):
        if self.data._stop_flag == True:
            self.logger.info("撤销订单, 平仓")
            ## 撤销现有订单
            self.broker.cancel_all_orders()
            # 平仓
            self._owner.close_all_positions()
            self.logger.error("======网格策略破网停止运行, 注意观察=======")
            return True

        if self.data._except_order:
            ## 撤销现有订单
            self.broker.cancel_all_orders()
            self.logger.error("无效订单: %s, 撤单并停止运行", self.data._except_order)
            return True
