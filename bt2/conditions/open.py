## 开仓

from bt2.condition import Condition
from ..indicators import *


class OpenOrderCond(Condition):
    """
    开仓
    """

    plotinfo = {
        "plot": False,
        "b_plot": False,
    }

    alias = ("OpenOrderCond",)  # 别名

    def __init__(self):
        self.order = None

    def open_log_string(self):
        ind_dict = self.get_indicator_dict()
        ind_value_string = ", ".join(
            [f"{attr}: {ind[0]}" for attr, ind in ind_dict.items()]
        )
        return f"open_type: {self.alias[0]}, {ind_value_string}"
