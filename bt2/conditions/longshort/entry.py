## 进入条件

from ..entry import EntryCond, LiveEntryCond, ManualEntryCond, StackedEntryCond
import backtrader as bt
from ...indicators import *
from ...utils import num2ts, num2date
import time
from collections import deque
import numpy as np


class NoPosEntryCond(EntryCond):
    """无仓位"""

    alias = ("NoPosEntryCond",)  # 别名

    plotinfo = (
        ("plot", False),
        ("b_plot", False),
    )

    def next(self):
        return self.broker.no_open_position()


class NoOrderEntryCond(EntryCond):
    """无委托单"""

    alias = ("NoOrderEntryCond",)  # 别名

    plotinfo = (
        ("plot", False),
        ("b_plot", False),
    )

    def next(self):
        return self.broker.no_order()


class LiveDataCond(EntryCond):
    """实时数据"""

    alias = ("LiveDataCond",)  # 别名

    plotinfo = (
        ("plot", False),
        ("b_plot", False),
    )

    def next(self):
        if not self._owner.data.is_live_status():
            return False
        delay = time.time() - num2ts(self._owner.data.get_curr_dt()) // 1000
        if delay > 5:
            self.logger.warning(
                "live time delay over 2s. data: %s", num2date(self.data.get_curr_dt())
            )
            return False
        return True


class RunStateCond(EntryCond):
    """运行状态"""

    alias = ("RunStateCond",)  # 别名

    plotinfo = (
        ("plot", False),
        ("b_plot", False),
    )

    def __init__(self):
        self._pausing = False

    def next(self):
        if self.broker.is_run() and self._pausing:
            self.logger.info("task run")
            self._pausing = False
        if self.broker.is_pause() and not self._pausing:
            self.logger.info("task pause")
            self._pausing = True
        return self.broker.is_run() or self.broker.is_pause()


class NoEntryCond(EntryCond):
    """不进入"""

    alias = ("NoEntryCond",)

    plotinfo = (
        ("plot", False),
        ("b_plot", False),
    )

    def next(self):
        return False


class NoBrakeEntryCond(LiveEntryCond):
    """
    无刹车进入
    """

    alias = ("NoBrakeEntryCond",)

    plotinfo = (
        ("plot", False),
        ("b_plot", False),
    )

    def __init__(self):
        self.bs = BnBrakePlatInd()

    def update_status(self):
        pass

    def next(self):
        if self.bs[0] * self.p.side != -1:
            return True
        return False


class PosValueBelowEntryCond(StackedEntryCond):
    """
    仓位价值小于总价值一定比例时开仓
    """

    alias = ("PosValueBelowEntryCond",)

    params = (("upper_rate", 0.4),)

    plotinfo = (
        ("plot", False),
        ("b_plot", False),
    )

    def next(self):
        cashbalance = self.broker.get_cashbalance()
        cash = self.broker.get_cash()
        if cash / cashbalance > 1 - self.p.upper_rate:
            return True
        return False


class RSIUpEntryCond(EntryCond):
    """
    rsi大于阈值进入
    """

    alias = ("RSIUpEntryCond",)  # 别名

    params = (
        ("rsi_period", 5),
        ("rsi_up_thres", 70),
    )

    def __init__(self):
        self.rsi = bt.indicators.RSI_Safe(self.data, period=self.p.rsi_period)

    def next(self):
        if self.rsi[0] >= self.p.rsi_up_thres:
            return True
        return False


class PriceSoftEntryCond(EntryCond):
    """
    价格变化温和的上涨
    """

    alias = ("PriceSoftEntryCond",)  # 别名

    params = (
        ("period", 14),
        ("rsi_low_thres", 55),
        ("rsi_high_thres", 65),
        ("pct_low_thres", 0.005),
        ("pct_high_thres", 0.05),
    )

    def __init__(self):
        self.rsi = bt.indicators.RSI_Safe(self.data, period=self.p.period)
        self.pct_change = bt.indicators.PercentChange(
            self.data, period=self.p.period
        )  # 涨幅

    def next(self):
        if self.p.rsi_low_thres < self.rsi[0] <= self.p.rsi_high_thres:
            if self.p.side == self.LONG:
                if (
                    self.rsi[0] > self.rsi[-1]
                    and self.p.pct_high_thres
                    > self.pct_change[0]
                    > self.p.pct_low_thres
                ):
                    return True
            else:
                if self.rsi[0] < self.rsi[-1] and self.p.pct_high_thres * (
                    -1
                ) < self.pct_change[0] < self.p.pct_low_thres * (-1):
                    return True
        return False


class RSIDownEntryCond(EntryCond):
    """
    rsi小于阈值进入
    """

    alias = ("RSIDownEntryCond",)  # 别名

    params = (
        ("rsi_period", 5),
        ("rsi_down_thres", 30),
    )

    def __init__(self):
        self.rsi = bt.indicators.RSI_Safe(self.data, period=self.p.rsi_period)

    def next(self):
        if self.rsi[0] <= self.p.rsi_down_thres:
            return True
        return False


class RSIMinDownEntryCond(EntryCond):
    """
    过去一段时间rsi最小值小于阈值进入
    """

    alias = ("RSIMinDownEntryCond",)  # 别名

    params = (
        ("rsi_period", 5),
        ("rsi_num", 2),
        ("rsi_down_thres", 30),
    )

    def __init__(self):
        self.rsi = bt.indicators.RSI_Safe(self.data, period=self.p.rsi_period)
        self.min_rsi = bt.indicators.Lowest(self.rsi, period=self.p.rsi_num)

    def next(self):
        if self.min_rsi[0] <= self.p.rsi_down_thres:
            return True
        return False


class BarOpenOnceEntryCond(EntryCond):
    """
    一个bar上只开仓一次
    """

    alias = ("BarOpenOnceEntryCond",)

    plotinfo = (
        ("plot", False),
        ("b_plot", False),
    )

    def __init__(self):
        # 当前bar为新的bar
        self.bar_opend = BarOpened()

    def next(self):
        return not self.bar_opend[0]


class BarsOpenOnceEntryCond(EntryCond):
    """
    N个bar上只开仓一次
    """

    alias = ("BarsOpenOnceEntryCond",)

    plotinfo = (
        ("plot", False),
        ("b_plot", False),
    )

    params = (("bar_num", 1),)

    def __init__(self):
        # 当前bar为新的bar
        self.bar_opend = BarOpened()
        self.nbar_opend = Accum(self.bar_opend, period=self.p.bar_num)

    def next(self):
        return self.nbar_opend[0] < 1


class HighAmpEntryCond(EntryCond):
    """
    当前bar被判定为大振幅
    """

    alias = ("HighAmpEntryCond",)

    params = (("amp_thres", 2),)

    def __init__(self):
        self.amp = AMP()
        self.high_amp = GtValue(self.amp, num=self.p.amp_thres)

    def next(self):
        return self.high_amp[0]


class NearHighAmpEntryCond(EntryCond):
    """
    当前bar振幅接近日新高
    """

    alias = ("NearHighAmpEntryCond",)
    params = (("near_ratio", 1),)

    def __init__(self):
        self.amp = AMP()
        self.highest_amp = bt.ind.Highest(self.amp, period=self.day_period)

    def next(self):
        if self.amp[0] >= self.highest_amp[0] * self.p.near_ratio:
            return True
        return False


class AmpOverMeanEntryCond(EntryCond):
    """
    当前bar振幅大于过去一段时间的平均振幅
    """

    alias = ("AmpOverMeanEntryCond",)
    params = (
        ("amp_rate", 1),
        ("min_amp", 2),
    )

    def __init__(self):
        self.amp = AMP()
        self.mean_amp = TrimMean(
            self.amp, period=self.day_period, top=1, num=7, plot=True
        )
        # self.gt = Gt(self.amp, self.mean_amp * self.p.amp_rate)

    def next(self):
        return self.amp[0] >= max(self.mean_amp[0], self.p.min_amp)


class VolumeOverEMA(EntryCond):
    """
    交易量大于其均线的倍数
    """

    alias = ("VolumeOverEMA",)

    params = (("ema_period", 5), ("vol_ratio", 5))

    def __init__(self):
        self.vol_ema = bt.indicators.EMA(self.data.volume, period=self.p.ema_period)
        self.vol_over_ema = Gt(self.data.volume, self.vol_ema * self.p.vol_ratio)

    def next(self):
        return self.vol_over_ema[0]


class VolumeSurgeEntryCond(EntryCond):
    """
    volume上涨
    """

    alias = ("VolumeSurgeEntryCond",)  # 别名

    params = (("vol_period", 10),)

    def __init__(self):
        # 均交易量
        self.avg_vol = bt.indicators.EMA(
            Lag(self.data.volume), period=self.p.vol_period
        )
        # 当前bar时间
        self.bar_second = BarOpenSecond(self.data)
        # 交易量阈值
        self.vol_thres = LinePiece(
            self.bar_second,
            self.avg_vol,
            xs=[10, 30],
            ys=[1.5, 3],
        )

    def next(self):
        if self.data.volume[0] > self.vol_thres[0]:
            self.update_extra_args(
                volume=self.data.volume[0],
            )
            return True
        return False


class VolumeOverMax(EntryCond):
    """
    交易量大于过去最大交易量的倍数
    """

    alias = ("VolumeOverMax",)

    params = (("vol_period", 5), ("vol_ratio", 3))

    def __init__(self):
        self.max_vol_lag = bt.indicators.Highest(
            Lag(self.data.volume), period=self.p.vol_period
        )
        self.vol_over_max = Gt(self.data.volume, self.max_vol_lag * self.p.vol_ratio)

    def next(self):
        return self.vol_over_max[0]


class ManualFollowEntryCond(ManualEntryCond):
    """
    人工进入
    """

    alias = ("ManualFollowEntryCond",)

    def __init__(self):
        self.last_position_size = 0

    def next(self):
        position_size = self.broker.get_size()
        open_price = self.broker.get_open_price()
        if position_size != self.last_position_size:
            self.update_extra_args(
                position_size=position_size,
                open_price=open_price,
            )
            self.last_position_size = position_size
            return True
        return False


class VelocityEntryCond(EntryCond):
    """
    v(n)连续涨或跌
    """

    alias = ("VelocityEntryCond",)  # 别名
    params = (
        ("period", 5),
        ("ratio", 0.005),  # 高低点接近比例
    )

    def next(self):
        if len(self.data.close) < self.p.period:
            return False

        for i in range(1, (self.p.period - 1)):
            data2 = {"high": self.data.high[-i + 1], "low": self.data.low[-i + 1]}
            data1 = {"high": self.data.high[-i], "low": self.data.low[-i]}
            if self.p.side == self.LONG and not self.higher(data1, data2):
                return False
            if self.p.side == self.SHORT and not self.lower(data1, data2):
                return False
        return True

    @staticmethod
    def lower(data1, data2):
        if data2["high"] < data1["high"] or data2["low"] < data1["low"]:
            return True
        return False

    @staticmethod
    def higher(data1, data2):
        if data2["high"] > data1["high"] or data2["low"] > data1["low"]:
            return True
        return False


class AccelerationEntryCond(EntryCond):
    """
    v(n)加速度大于阈值
    """

    alias = ("AccelerationEntryCond",)  # 别名
    params = (("period", 5),)

    def next(self):
        if len(self.data.close) < self.p.period:
            return False

        v_start = self.data.close[0] - self.data.close[-1]
        v_end = (
            self.data.close[-(self.p.period - 2)]
            - self.data.close[-(self.p.period - 1)]
        )
        acc_all_is_true = (v_start - v_end) * self.p.side > 0

        count_good = 0
        for i in range(0, (self.p.period - 1)):
            v_temp = self.data.close[-i] - self.data.close[-i - 1]
            if v_temp * self.p.side > 0:
                count_good += 1

        # count_is_good = count_good >= round(self.p.period*0.5)
        count_is_good = True

        return acc_all_is_true and count_is_good


class BarVolumeDeclineEntryCond(LiveEntryCond):
    """
    一个bar内交易量逐步下降
    """

    alias = ("BarVolumeDeclineEntryCond",)

    params = (("count", 3),)

    def __init__(self):
        self.init_queue()
        self.bar_start = BarStart()

    def next(self):
        if self.bar_start[0]:
            self.init_queue()
        self.add_volume()
        return self.volume_decline()

    def init_queue(self):
        self.volume_queue = deque(maxlen=self.p.count + 1)
        self.volume_queue.append(0)

    def add_volume(self):
        self.volume_queue.append(self.data.volume[0])

    def volume_decline(self):
        tick_vol = np.diff(self.volume_queue)
        tick_inc = np.diff(tick_vol)
        if all(v < 0 for v in tick_inc):
            return True
        return False


class VolumeOverEMAEntryCond(EntryCond):
    """
    当前预测交易量超过交易量均线数倍
    """

    alias = ("VolumeOverEMAEntryCond",)

    params = (("vol_period", 5), ("vol_ratio", 1.5))

    def __init__(self):
        self.vol_ema = bt.indicators.EMA(
            Lag(self.data.volume), period=self.p.vol_period
        )
        self.expect_vol = LinearBarVolume()
        self.vol_gt = Gt(self.expect_vol, self.vol_ema * self.p.vol_ratio)

    def next(self):
        return self.vol_gt[0]
