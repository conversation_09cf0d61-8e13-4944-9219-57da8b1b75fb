## 追单

from ...indicators import *
from ..append import AppendCond


class CancelTimeOutAppendCond(AppendCond):
    """
    撤销超时订单
    """

    alias = ("CancelTimeOutAppendCond",)

    def next(self):
        orders = self.broker.get_alive_orders()
        for order in orders:
            if order and order.is_timeout():
                # 检查是否超时
                self.broker.cancel_no_wait(order)
                self.logger.info("trigger_append_condition: %s", self.alias[0])
                return True
        return False


class CancelAmplitudeLimitAppendCond(AppendCond):
    """
    针对振幅摆单的订单撤单
    data0: 振幅摆单条件
    data1: k线
    """

    alias = ("CancelAmplitudeLimitAppendCond",)

    def next(self):
        res = False
        order1 = self.datas[0].order1
        order2 = self.datas[0].order2
        if order1 and order1.alive():
            self.cancel_order(order1)
            self.datas[0].order1 = None
            res = True
        if order2 and order2.alive():
            self.cancel_order(order2)
            self.datas[0].order2 = None
            res = True
        return res

    def cancel_order(self, order):
        if self.broker.no_open_position():
            if self.p.side == self.LONG:
                trigger = self.datas[1].close[0] > self.datas[0].ordered_price * (
                    1 + self.datas[0].ordered_max_amp * 1
                )
            if self.p.side == self.SHORT:
                trigger = self.datas[1].close[0] < self.datas[0].ordered_price * (
                    1 - self.datas[0].ordered_max_amp * 1
                )
            if trigger:
                # 取消订单
                self.logger.info("trigger_append_condition: %s", self.alias[0])
                self.broker.cancel_no_wait(order)
