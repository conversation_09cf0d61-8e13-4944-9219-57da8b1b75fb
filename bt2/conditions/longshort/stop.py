## 停止条件

from ..stop import StopCond


class StopStateCond(StopCond):
    """
    停止命令
    """

    alias = ("StopStateCond",)

    def next(self):
        if self.broker.is_stop():
            # 任务停止
            self.logger.info("task stop")
            return True
        return False


class PauseNoPosStateCond(StopCond):
    """
    任务暂停且无仓位
    """

    alias = ("PauseNoPosStateCond",)

    def next(self):
        if (
            self.broker.is_pause()
            and self.broker.no_open_position()
            and self.broker.no_order()
        ):
            # 任务暂停且无仓位和订单
            self.logger.info("task stop")
            return True
        return False


class RiskStopCond(StopCond):
    """
    风控
    """

    alias = ("RiskStopCond",)

    def next(self):
        if self.broker.risked():  # 风控跳过
            self.logger.warning("account risk!!")
            return True
        return False


class LosingStreakStopCond(StopCond):
    """
    连续亏损停止条件
    """

    alias = ("LosingStreakStopCond",)

    params = (("count", 2),)

    def next(self):
        lose = 0
        try:
            trades = self._owner._trades[self._owner.data][0][::-1]
        except:
            trades = []
        for trade in trades:
            if trade.isclosed and trade.pnl > 0:
                return False
            if trade.isclosed and trade.pnl <= 0:
                lose += 1
            if lose >= 2:
                return True
        return False
