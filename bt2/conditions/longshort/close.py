## 平仓

from ..close import CloseOrderCond
import backtrader as bt
from ...indicators import *
import datetime


class StopProfitQueueCond(CloseOrderCond):
    """
    止盈挂单, 盘口价
    """

    alias = ("StopProfitQueueCond",)  # 别名

    params = (
        ("vol_period", 10),
        ("size_ratio", 0.2),
        ("open_price_ratio", 0.005),
    )

    def __init__(self):
        self.order = None

    def submit_close_order(self, trigger=None):
        """提交止盈订单"""
        open_price = self.broker.get_open_price()
        if open_price and self.p.side * (self.data.close[0] - open_price) > 0:
            order = self.broker.get_latest_alive_order()
            if order:  # 有存活订单
                # 检查是否超时
                if not order.is_timeout():
                    self.logger.info(f"order {order} is not timeout")
                    return True
                self.logger.info(f"order {order} is timeout, cancel order")
                self.broker.cancel_no_wait(order)
            # 创建并提交新订单
            self.order = self._owner.close(
                exectype="Limit",
                valid=bt.num2date(self.data.get_curr_dt())
                + datetime.timedelta(seconds=2),
                priceMatch="OPPONENT_5",
                timeInForce="GTC",
                trigger=trigger.get_name(),
            )
            self.logger.info(f"create new order {self.order}")
            return True
        return False


class StopLossMarketCond(CloseOrderCond):
    """
    止损订单, 市价
    """

    alias = ("StopLossMarketCond",)  # 别名

    params = (("close_price_ratio", 0.01),)

    def __init__(self):
        self.order = None

    def submit_close_order(self, trigger=None):
        open_price = self.broker.get_open_price()
        if open_price and self.p.side * (self.data.close[0] - open_price) <= 0:
            order = self.broker.get_latest_alive_order()
            if order:  # 有存活订单
                # 检查是否超时
                if not order.is_timeout():
                    self.logger.info(f"order {order} is not timeout")
                    return True
                self.logger.info(f"order {order} is timeout, cancel order")
                self.broker.cancel_no_wait(order)
            # 创建并提交新订单
            self.order = self._owner.close(
                data=self.data,
                exectype="Limit",
                priceMatch="OPPONENT_5",
                timeInForce="IOC",
                trigger=trigger.get_name(),
                valid=bt.num2date(self.data.get_curr_dt())
                + datetime.timedelta(seconds=10),
            )
            self.logger.info(f"create new order {self.order}")
            return True
        return False


class StopLossMarketCond2(CloseOrderCond):
    """
    止损订单, 不判断是否有其他订单存活
    """

    alias = ("StopLossMarketCond2",)  # 别名

    params = (("close_price_ratio", 0.01),)

    def __init__(self):
        self.order = None

    def submit_close_order(self, trigger=None):
        open_price = self.broker.get_open_price()
        if open_price and self.p.side * (self.data.close[0] - open_price) <= 0:
            # 创建并提交新订单
            self.order = self._owner.close(
                data=self.data,
                exectype="Limit",
                priceMatch="OPPONENT_5",
                timeInForce="IOC",
                trigger=trigger.get_name(),
                valid=bt.num2date(self.data.get_curr_dt())
                + datetime.timedelta(seconds=10),
            )
            self.logger.info(f"create new order {self.order}")
            return True
        return False
