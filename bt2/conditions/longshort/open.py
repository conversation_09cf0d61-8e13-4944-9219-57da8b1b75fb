## 开仓

from ..open import OpenOrderCond
import backtrader as bt
from ...indicators import *
import datetime


class VolSizeOpenCond(OpenOrderCond):
    """
    根据均交易量计算仓位开仓
    """

    alias = ("VolSizeOpenCond",)  # 别名

    params = (
        ("vol_period", 10),
        ("size_ratio", 0.2),
        ("open_price_ratio", 0.005),
    )

    def __init__(self):
        self.avg_vol = bt.indicators.SMA(
            Lag(self.data.volume), period=self.p.vol_period
        )
        self.order = None

    def submit_open_order(self):
        """提交开仓订单"""
        size = self.avg_vol[0] / 300 * self.p.size_ratio
        price = (
            self.data.close[0]
            + self.data.close[0] * self.p.open_price_ratio * self.p.side
        )
        # 创建并提交新订单
        if self.p.side == self.LONG:
            action = self._owner.buy
            side = "long"
        else:
            action = self._owner.sell
            side = "short"
        self.order = action(
            side=side,
            exectype="Limit",
            price=price,
            valid=bt.num2date(self.data.get_curr_dt()) + datetime.timedelta(seconds=2),
            size=size,
            timeInForce="IOC",
        )
        return True


class BalanceOverPriceOpenCond(OpenOrderCond):
    """
    根据资金计算账户价值开仓, 超价
    """

    alias = ("BalanceOverPriceOpenCond",)  # 别名

    params = (
        ("symbol_count", 50),
        ("value_ratio", 0.5),
        ("price_over_ratio", 0.005),
    )

    def __init__(self):
        self.order = None
        self.lastBar = None

    def submit_open_order(self):
        try:
            self.lastBar = {"high": self.data.high[-1], "low": self.data.low[-1]}
        except:
            self.lastBar = None
        """提交开仓订单"""
        # 创建并提交新订单
        cashbalance = self.broker.get_cashbalance()
        lever = self.broker.get_levers()[self.data]
        value = cashbalance * self.p.value_ratio / self.p.symbol_count * lever
        price = (
            self.data.close[0]
            + self.data.close[0] * self.p.price_over_ratio * self.p.side
        )
        if self.p.side == self.LONG:
            action = self._owner.buy
            side = "long"
        else:
            action = self._owner.sell
            side = "short"
        self.order = action(
            side=side,
            exectype="Limit",
            price=price,
            valid=bt.num2date(self.data.get_curr_dt()) + datetime.timedelta(seconds=2),
            parValue=value,
            timeInForce="IOC",
        )
        return True


class FixOverPriceOpenCond(OpenOrderCond):
    """
    固定价值开仓, 超价
    """

    alias = ("FixOverPriceOpenCond",)  # 别名

    params = (
        ("fix_value", 200),
        ("price_over_ratio", 0.005),
    )

    def submit_open_order(self):
        """提交开仓订单"""
        # 创建并提交新订单
        value = self.p.fix_value
        price = (
            self.data.close[0]
            + self.data.close[0] * self.p.price_over_ratio * self.p.side
        )
        if self.p.side == self.LONG:
            action = self._owner.buy
            side = "long"
        else:
            action = self._owner.sell
            side = "short"
        self.order = action(
            side=side,
            exectype="Limit",
            price=price,
            valid=bt.num2date(self.data.get_curr_dt()) + datetime.timedelta(seconds=2),
            parValue=value,
            timeInForce="IOC",
        )
        return True


class StepOverPriceOpenCond(OpenOrderCond):
    """
    阶梯价值开仓, 超价
    """

    alias = ("StepOverPriceOpenCond",)  # 别名

    params = (
        ("base_value", 800),
        ("step", 2000),
        ("price_over_ratio", 0.005),
    )

    def submit_open_order(self):
        """提交开仓订单"""
        # 创建并提交新订单
        base_value = self.p.base_value
        cashbalance = self.broker.get_cashbalance()
        ratio = cashbalance // self.p.step + 1
        value = base_value * ratio
        order_args = dict(
            exectype="Limit",
            parValue=value,
            timeInForce="IOC",
        )
        if self.p.price_over_ratio != None:
            order_args["price"] = (
                self.data.close[0]
                + self.data.close[0] * self.p.price_over_ratio * self.p.side
            )
        else:
            order_args["priceMatch"] = "OPPONENT_5"
        if self.p.side == self.LONG:
            action = self._owner.buy
            order_args["side"] = "long"
        else:
            action = self._owner.sell
            order_args["side"] = "short"
        self.order = action(**order_args)
        return True


class NoOpenCond(OpenOrderCond):
    """
    不需要开仓
    """

    alias = ("NoOpenCond",)  # 别名

    def submit_open_order(self):
        return True


class BalanceRatioOpenCond(OpenOrderCond):
    """
    根据资金系数计算账户价值开仓, 对手价
    """

    alias = ("BalanceRatioOpenCond",)  # 别名

    params = (("value_ratio", 0.5),)

    def submit_open_order(self):
        """提交开仓订单"""
        # 创建并提交新订单
        cashbalance = self.broker.get_cashbalance()
        value = cashbalance * self.p.value_ratio
        if self.p.side == self.LONG:
            action = self._owner.buy
            side = "long"
        else:
            action = self._owner.sell
            side = "short"
        self.order = action(
            side=side,
            exectype="Limit",
            priceMatch="OPPONENT_5",
            parValue=value,
            timeInForce="IOC",
        )
        return True


class CmpStepOverPriceOpenCond(OpenOrderCond):
    """
    根据对比品种阶梯开仓
    """

    alias = ("CmpStepOverPriceOpenCond",)  # 别名

    plotinfo = (("b_plot", True),)

    params = (
        ("base_value", 1200),
        ("step", 2000),
        ("price_over_ratio", 0.005),
        ("change_thres", 2),
        ("value_ratio", 0.2),
        ("ma_periods", (10, 20, 60)),  # 指数标的，多周期均线，作为趋势的判断线
    )

    def __init__(self):
        self.cmp_change = PriceChangeRate(self.datas[2], period=4, b_plot=True)
        self.mas = [
            bt.indicators.SMA(self.datas[1], period=period)
            for period in self.p.ma_periods
        ]
        self.ma_order = MultiDatasOrder(*self.mas, asc=True)

    def submit_open_order(self):
        """提交开仓订单"""
        # 创建并提交新订单
        base_value = self.p.base_value
        cashbalance = self.broker.get_cashbalance()
        ratio = cashbalance // self.p.step + 1
        value = base_value * ratio

        # 趋势影响开仓
        if self.p.side == self.LONG:
            if self.ma_order[0] is True:  # 近周期价格 < 远周期价格，趋势为空头
                trend_ratio = 0.25
            elif self.ma_order[0] is False:  # 近周期价格 > 远周期价格，趋势为多头
                trend_ratio = 1
            else:
                trend_ratio = 0.5
        else:
            if self.ma_order[0] is True:  # 近周期价格 < 远周期价格，趋势为空头
                trend_ratio = 1
            elif self.ma_order[0] is False:  # 近周期价格 > 远周期价格，趋势为多头
                trend_ratio = 0.25
            else:
                trend_ratio = 0.5
        value = value * trend_ratio  # 根据趋势影响开仓，顺势开大仓，逆势开小仓

        # BTCDOM 影响开仓
        if self.cmp_change[0] > self.p.change_thres:
            value = value * self.p.value_ratio
        elif self.cmp_change[0] < -1 * self.p.change_thres:
            value = value
        else:
            value = value * 0.5
        order_args = dict(
            exectype="Limit",
            parValue=value,
            timeInForce="IOC",
        )
        if self.p.price_over_ratio != None:
            order_args["price"] = (
                self.datas[0].close[0]
                + self.datas[0].close[0] * self.p.price_over_ratio * self.p.side
            )
        else:
            order_args["priceMatch"] = "OPPONENT_5"
        if self.p.side == self.LONG:
            action = self._owner.buy
            order_args["side"] = "long"
        else:
            action = self._owner.sell
            order_args["side"] = "short"
        self.order = action(**order_args)
        return True
