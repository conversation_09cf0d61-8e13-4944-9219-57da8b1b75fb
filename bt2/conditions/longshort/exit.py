## 退出条件

from ..exit import KeepTillCloseExitCond, LiveKeepTillCloseExitCond, ExitCond
import backtrader as bt
from ...indicators import *
import datetime


class StopLossExitCond(KeepTillCloseExitCond):
    """
    止损退出. 当前价低于某个值时退出
    """

    alias = ("StopLossExitCond",)

    params = (("loss_rate", 0.005),)

    def __init__(self):
        self._num = 0

    def next(self):
        open_price = self.broker.get_open_price(self.data)
        low_price = (
            self.data.close[0] if self.data.is_live_status() else self.data.low[0]
        )
        # 每执行10次打印一次log
        if open_price:
            if self._num == 10:
                self.logger.info(
                    f"open_price: {open_price}, low_price: {low_price}, loss_rate: {self.p.loss_rate}, side: {self.p.side}"
                )
                self._num = 0
            self._num += 1
        if (
            open_price
            and (open_price - low_price) * self.p.side >= open_price * self.p.loss_rate
        ):
            # 当前价和成交价的差值大于一定值止损
            self.update_extra_args(
                stop_price=open_price * (1 - self.p.loss_rate * self.p.side)
            )
            return True
        return False

    def submit_close_order(self):
        # 提交平仓订单
        self.broker.cancel_all_orders()
        # 创建并提交新订单
        open_size = self.broker.query_open_position()
        self.order = self._owner.close(
            data=self.data,
            exectype="Limit",
            priceMatch="OPPONENT_20",
            timeInForce="IOC",
            size=open_size,
            trigger=self.get_name(),
            valid=bt.num2date(self.data.get_curr_dt()) + datetime.timedelta(seconds=10),
        )
        self.logger.info(f"create new order {self.order}")
        return True


class TrailingStopExitCond(KeepTillCloseExitCond):
    """
    移动止盈
    """

    alias = ("TrailingStopExitCond",)

    params = (
        ("trigger_profit_rate", 0.01),
        ("stop_profit_rate", 0.6),
    )

    def __init__(self):
        self.open_highest_price = KeepState(default=float("-inf"))
        self.open_lowest_price = KeepState(default=float("inf"))

    def update_status(self):
        if self.broker.no_order() and self.broker.no_open_position():  # 无仓位无订单
            self.open_highest_price.reset_value()
            self.open_lowest_price.reset_value()
        if self.broker.is_open_position():
            self.open_highest_price.max_value(self.data.close[0])
            self.open_lowest_price.min_value(self.data.close[0])

    def next(self):
        open_price = self.broker.get_open_price()
        if open_price:
            if self.p.side == self.LONG:  # do long
                if self.open_highest_price[0] >= open_price * (
                    1 + self.p.trigger_profit_rate
                ):
                    stop_price = (
                        open_price
                        + (self.open_highest_price[0] - open_price)
                        * self.p.stop_profit_rate
                    )
                else:
                    stop_price = None
                if stop_price and self.data.close[0] <= stop_price:
                    # 当前价和成交价的差值大于一定值止损
                    self.update_extra_args(stop_price=stop_price)
                    return True

            if self.p.side == self.SHORT:  # do short
                if self.open_lowest_price[0] <= open_price * (
                    1 - self.p.trigger_profit_rate
                ):
                    stop_price = (
                        open_price
                        - (open_price - self.open_lowest_price[0])
                        * self.p.stop_profit_rate
                    )
                else:
                    stop_price = None
                if stop_price and self.data.close[0] >= stop_price:
                    # 当前价和成交价的差值大于一定值止损
                    self.update_extra_args(stop_price=stop_price)
                    return True
        return False


class TrailingStopLiveExitCond(KeepTillCloseExitCond):
    """
    实时根据最新开仓更新高价低价移动止盈
    """

    alias = ("TrailingStopLiveExitCond",)

    params = (
        ("trigger_profit_rate", 0.01),
        ("stop_profit_rate", 0.6),
    )

    def __init__(self):
        self.open_highest_price = KeepState(default=float("-inf"))
        self.open_lowest_price = KeepState(default=float("inf"))
        self.last_open_price = KeepState(default=float(0))

    def update_status(self):
        if self.broker.no_order() and self.broker.no_open_position():  # 无仓位无订单
            self.open_highest_price.reset_value()
            self.open_lowest_price.reset_value()
        else:
            open_price = self.broker.get_open_price()
            if self.last_open_price[0] != open_price:
                self.open_highest_price.set_value(self.data.close[0])
                self.open_lowest_price.set_value(self.data.close[0])
                self.last_open_price.set_value(open_price)
        if self.broker.is_open_position():
            self.open_highest_price.max_value(self.data.close[0])
            self.open_lowest_price.min_value(self.data.close[0])

    def next(self):
        open_price = self.broker.get_open_price()
        if open_price:
            if self.p.side == self.LONG:  # do long
                if self.open_highest_price[0] >= open_price * (
                    1 + self.p.trigger_profit_rate
                ):
                    stop_price = (
                        open_price
                        + (self.open_highest_price[0] - open_price)
                        * self.p.stop_profit_rate
                    )
                else:
                    stop_price = None
                if stop_price and self.data.close[0] <= stop_price:
                    # 当前价和成交价的差值大于一定值止损
                    self.update_extra_args(stop_price=stop_price)
                    return True

            if self.p.side == self.SHORT:  # do short
                if self.open_lowest_price[0] <= open_price * (
                    1 - self.p.trigger_profit_rate
                ):
                    stop_price = (
                        open_price
                        - (open_price - self.open_lowest_price[0])
                        * self.p.stop_profit_rate
                    )
                else:
                    stop_price = None
                if stop_price and self.data.close[0] >= stop_price:
                    # 当前价和成交价的差值大于一定值止损
                    self.update_extra_args(stop_price=stop_price)
                    return True

        return False


class StepTrailingStopExitCond(KeepTillCloseExitCond):
    """
    分段移动止盈
    """

    alias = ("StepTrailingStopExitCond",)

    params = (
        ("trigger_rates", (0.05, 0.1)),
        ("stop_rates", (0.6, 0.5)),
    )

    def __init__(self):
        self.open_highest_price = KeepState(default=float("-inf"))
        self.open_lowest_price = KeepState(default=float("inf"))

    def update_status(self):
        if self.broker.no_order() and self.broker.no_open_position():  # 无仓位无订单
            self.open_highest_price.reset_value()
            self.open_lowest_price.reset_value()
        if self.broker.is_open_position():
            self.open_highest_price.max_value(self.data.close[0])
            self.open_lowest_price.min_value(self.data.close[0])

    def next(self):
        open_price = self.broker.get_open_price()
        if open_price:
            if self.p.side == self.LONG:  # do long
                price_diff = self.open_highest_price[0] - open_price  # 价差
                price_rate = price_diff / open_price  # 价差比例
                stop_price_rate = None  # 止盈价格比例
                stop_price = None  # 止盈价格
                for trigger, stop_rate in zip(self.p.trigger_rates, self.p.stop_rates):
                    if price_rate >= trigger:
                        stop_price_rate = stop_rate
                if stop_price_rate != None:
                    stop_price = open_price + price_diff * stop_price_rate
                if stop_price != None and self.data.close[0] <= stop_price:
                    # 当前价和成交价的差值大于一定值止损
                    self.update_extra_args(stop_price=stop_price)
                    return True

            if self.p.side == self.SHORT:  # do short
                price_diff = open_price - self.open_lowest_price[0]  # 价差
                price_rate = price_diff / open_price  # 价差比例
                stop_price_rate = None  # 止盈价格比例
                stop_price = None  # 止盈价格
                for trigger, stop_rate in zip(self.p.trigger_rates, self.p.stop_rates):
                    if price_rate >= trigger:
                        stop_price_rate = stop_rate
                if stop_price_rate != None:
                    stop_price = open_price - price_diff * stop_price_rate
                if stop_price != None and self.data.close[0] >= stop_price:
                    # 当前价和成交价的差值大于一定值止损
                    self.update_extra_args(stop_price=stop_price)
                    return True
        return False


class BreakevenExitCond(KeepTillCloseExitCond):
    """
    保本退出
    """

    alias = ("BreakevenExitCond",)

    params = (
        ("upper_limit", 0.01),
        ("lower_limit", 0.005),
        ("stop_rate", 0.001),
    )

    def __init__(self):
        self.open_highest_price = KeepState(default=float("-inf"))
        self.open_lowest_price = KeepState(default=float("inf"))

    def update_status(self):
        if self.broker.no_order() and self.broker.no_open_position():  # 无仓位无订单
            self.open_highest_price.reset_value()
            self.open_lowest_price.reset_value()
        if self.broker.is_open_position():
            self.open_highest_price.max_value(self.data.close[0])
            self.open_lowest_price.min_value(self.data.close[0])

    def next(self):
        open_price = self.broker.get_open_price()
        if open_price:
            if self.p.side == self.LONG:
                if (
                    open_price * (1 + self.p.lower_limit)
                    <= self.open_highest_price[0]
                    < open_price * (1 + self.p.upper_limit)
                ):
                    stop_price = open_price * (1 + self.p.stop_rate)
                else:
                    stop_price = None
                if stop_price and self.data.close[0] <= stop_price:
                    # 当前价和成交价的差值大于一定值止损
                    self.update_extra_args(stop_price=stop_price)
                    return True
            else:
                if (
                    open_price * (1 - self.p.lower_limit)
                    >= self.open_lowest_price[0]
                    > open_price * (1 - self.p.upper_limit)
                ):
                    stop_price = open_price * (1 - self.p.stop_rate)
                else:
                    stop_price = None
                if stop_price and self.data.close[0] >= stop_price:
                    # 当前价和成交价的差值大于一定值止损
                    self.update_extra_args(stop_price=stop_price)
                    return True
        return False


class BreakevenLiveExitCond(KeepTillCloseExitCond):
    """
    实时根据最新开仓更新高价低价保本退出
    """

    alias = ("BreakevenLiveExitCond",)

    params = (
        ("upper_limit", 0.01),
        ("lower_limit", 0.005),
        ("stop_rate", 0.001),
    )

    def __init__(self):
        self.open_highest_price = KeepState(default=float("-inf"))
        self.open_lowest_price = KeepState(default=float("inf"))
        self.last_open_price = KeepState(default=float(0))

    def update_status(self):
        if self.broker.no_order() and self.broker.no_open_position():  # 无仓位无订单
            self.open_highest_price.reset_value()
            self.open_lowest_price.reset_value()
        else:
            open_price = self.broker.get_open_price()
            if self.last_open_price != open_price:
                self.open_highest_price.reset_value()
                self.open_lowest_price.reset_value()
                self.last_open_price.set_value(open_price)
        if self.broker.is_open_position():
            self.open_highest_price.max_value(self.data.close[0])
            self.open_lowest_price.min_value(self.data.close[0])

    def next(self):
        open_price = self.broker.get_open_price()
        if open_price:
            if self.p.side == self.LONG:
                if (
                    open_price * (1 + self.p.lower_limit)
                    <= self.open_highest_price[0]
                    < open_price * (1 + self.p.upper_limit)
                ):
                    stop_price = open_price * (1 + self.p.stop_rate)
                else:
                    stop_price = None
                if stop_price and self.data.close[0] <= stop_price:
                    # 当前价和成交价的差值大于一定值止损
                    self.update_extra_args(stop_price=stop_price)
                    return True
            else:
                if (
                    open_price * (1 - self.p.lower_limit)
                    >= self.open_lowest_price[0]
                    > open_price * (1 - self.p.upper_limit)
                ):
                    stop_price = open_price * (1 - self.p.stop_rate)
                else:
                    stop_price = None
                if stop_price and self.data.close[0] >= stop_price:
                    # 当前价和成交价的差值大于一定值止损
                    self.update_extra_args(stop_price=stop_price)
                    return True
        return False


class BreakevenExitCond2(BreakevenExitCond):
    """
    保本退出2档
    """

    alias = ("BreakevenExitCond2",)


class ChandelierExitCond(KeepTillCloseExitCond):
    """
    吊灯退出
    """

    alias = ("ChandelierExitCond",)

    params = (
        ("atr_period", 5),
        ("atr_ratio", 1.5),
    )

    def __init__(self):
        self.atr = bt.indicators.ATR(self.data, period=self.p.atr_period)
        self.open_highest_price = KeepState(default=float("-inf"))
        self.open_lowest_price = KeepState(default=float("inf"))

    def update_status(self):
        if self.broker.no_order() and self.broker.no_open_position():  # 无仓位无订单
            self.open_highest_price.reset_value()
            self.open_lowest_price.reset_value()
        if self.broker.is_open_position():
            self.open_highest_price.max_value(self.data.close[0])
            self.open_lowest_price.min_value(self.data.close[0])

    def next(self):
        if self.p.side == self.LONG:
            if (
                self.open_highest_price[0] - self.data.close[0]
                >= self.p.atr_ratio * self.atr[0]
            ):
                return True
        if self.p.side == self.SHORT:
            if (
                self.data.close[0] - self.open_lowest_price[0]
                >= self.p.atr_ratio * self.atr[0]
            ):
                return True
        return False


class BrakeExitCond(LiveKeepTillCloseExitCond):
    """
    刹车退出
    """

    alias = ("BrakeExitCond",)

    def __init__(self):
        self.bs = BnBrakePlatInd()

    def next(self):
        if self.bs[0] * self.p.side == -1:
            return True
        return False


class TimedLossExitCond(KeepTillCloseExitCond):
    """
    超时亏损退出
    """

    alias = ("TimedLossExitCond",)

    params = (("timeout", 120),)

    def __init__(self):
        self.open_highest_price = KeepState()
        self.open_lowest_price = KeepState()
        self.open_t = KeepState(default=0)
        self.open_dt = None
        # 超时条件是否触发过
        self.is_trigger = False

    def update_status(self):
        if self.broker.no_order() and self.broker.no_open_position():  # 无仓位无订单
            self.open_highest_price.reset_value()
            self.open_lowest_price.reset_value()
            self.open_t.reset_value()
            self.open_dt = None

        if self.broker.is_open_position():
            if self.open_highest_price.isnan():
                self.open_highest_price.set_value(self.data.high[0])
            if self.open_lowest_price.isnan():
                self.open_lowest_price.set_value(self.data.low[0])
            if self.open_dt == None:
                self.open_dt = self.data.get_curr_dt()
            seconds = (
                bt.num2date(self.data.get_curr_dt()) - bt.num2date(self.open_dt)
            ).total_seconds()
            self.open_t.set_value(seconds)

    def next(self):
        open_price = self.broker.get_open_price()
        if open_price and self.open_t[0] > self.p.timeout and not self.is_trigger:
            self.is_trigger = True
            aver_v5 = (self.data.close[-1] - self.data.open[-5]) / 5
            if self.p.side == self.LONG:
                aver_v5 = max(aver_v5, 0)
                self.update_extra_args(aver_v5=aver_v5)
                if self.data.close[0] < open_price + aver_v5 * 2:
                    return True
            elif self.p.side == self.SHORT:
                aver_v5 = min(aver_v5, 0)
                self.update_extra_args(aver_v5=aver_v5)
                if self.data.close[0] > open_price + aver_v5 * 2:
                    return True
        return False

    def submit_close_order(self):
        # 创建并提交新订单
        open_price = self.broker.get_open_price()
        if self.p.side == self.SHORT:
            if self.data.close[0] < open_price:
                self._owner.close(
                    exectype="Limit",
                    valid=bt.num2date(self.data.get_curr_dt())
                    + datetime.timedelta(seconds=2),
                    price=self.data.close[0] * 1.001,
                    timeInForce="GTC",
                    trigger=self.get_name(),
                )
                return True
        if self.p.side == self.LONG:
            if self.data.close[0] > open_price:
                self._owner.close(
                    exectype="Limit",
                    valid=bt.num2date(self.data.get_curr_dt())
                    + datetime.timedelta(seconds=2),
                    price=self.data.close[0] * 0.999,
                    timeInForce="GTC",
                    trigger=self.get_name(),
                )
                return True
        return False


class TimeOutExitCond(KeepTillCloseExitCond):
    """
    超时退出
    """

    alias = ("TimeOutExitCond",)

    params = (("timeout", 3600 * 24),)

    def __init__(self):
        self.open_t = KeepState(default=0)
        self.open_dt = None

    def update_status(self):
        if self.broker.no_order() and self.broker.no_open_position():  # 无仓位无订单
            self.open_t.reset_value()
            self.open_dt = None

        if self.broker.is_open_position():
            if self.open_dt == None:
                self.open_dt = self.data.get_curr_dt()
            seconds = (
                bt.num2date(self.data.get_curr_dt()) - bt.num2date(self.open_dt)
            ).total_seconds()
            self.open_t.set_value(seconds)

    def next(self):
        if self.open_t[0] > self.p.timeout:
            return True
        return False

    def submit_close_order(self):
        # 创建并提交新订单
        self._owner.close(
            exectype="Limit",
            valid=bt.num2date(self.data.get_curr_dt()) + datetime.timedelta(seconds=2),
            priceMatch="OPPONENT",
            timeInForce="GTC",
            trigger=self.get_name(),
        )
        return True


class ATRStopProfitExitCond(ExitCond):
    """
    ATR退出
    减仓退出
    """

    alias = ("ATRStopProfitExitCond",)

    params = (
        ("atr_period", 5),
        ("atr_ratio", 2),
    )

    def __init__(self):
        self.atr = bt.indicators.ATR(self.data, period=self.p.atr_period)
        self.dlaghigh = bt.ind.Highest(Lag(self.data.high), period=self.day_period)
        self.dlaglow = bt.ind.Lowest(Lag(self.data.low), period=self.day_period)
        self.bs = BnBrakePlatInd()
        self.close_stage = 1  # 门槛系数
        self.order = None  # 止盈订单

    def update_status(self):
        if self.broker.no_open_position():
            self.close_stage = 1

    def raise_close_stage(self):
        if self.close_stage < 3:
            self.close_stage += 1

    @property
    def stage_price_ratio(self):
        d = {1: 1, 2: 1.5, 3: 5}
        return d.get(self.close_stage)

    @property
    def stage_size_ratio(self):
        d = {1: 2 / 3, 2: 3 / 4, 3: 1}
        return d.get(self.close_stage)

    def next(self):
        open_price = self.broker.get_open_price()
        if open_price:
            bs = self.bs[0]
            rate_long = 2 if bs == 1 else 1
            rate_short = 2 if bs == -1 else 1
            if (
                self.p.side == self.LONG
                and self.data.close[0]
                >= open_price
                + self.p.atr_ratio * self.atr[0] * self.stage_price_ratio * rate_long
            ):
                if self.data.close[0] < self.dlaghigh * 0.999:
                    return True
            if (
                self.p.side == self.SHORT
                and self.data.close[0]
                <= open_price
                - self.p.atr_ratio * self.atr[0] * self.stage_price_ratio * rate_short
            ):
                if self.data.close[0] > self.dlaglow * 1.001:
                    return True
        return False

    def submit_close_order(self):
        """
        提交止盈订单
        部分止盈
        """
        if self.order is not None and self.order.alive():  # 订单存活
            return True
        size = self.broker.get_size() * self.stage_size_ratio
        # 创建并提交新订单
        self.order = self._owner.close(
            size=size,
            exectype="Limit",
            valid=bt.num2date(self.data.get_curr_dt()) + datetime.timedelta(seconds=2),
            priceMatch="QUEUE",
            timeInForce="GTC",
            trigger=self.get_name(),
        )
        self.raise_close_stage()
        return True

    def close_log_string(self):
        return f"close_type: {self.get_name()}, close stage: {self.close_stage}, order: {self.order}"


class VelocityExitCond(KeepTillCloseExitCond):
    """
    v(n)速度小于阈值
    """

    alias = ("VelocityExitCond",)  # 别名
    params = (("period", 5),)

    def next(self):
        if len(self.data.close) < self.p.period:
            return False
        vel = (self.data.close[0] - self.data.close[-(self.p.period - 1)]) / (
            self.p.period - 1
        )
        if vel * self.p.side < 0:
            return True
        return False


class AccelerationExitCond(KeepTillCloseExitCond):
    """
    v(n)加速度小于等于阈值
    """

    alias = ("AccelerationExitCond",)  # 别名
    params = (("period", 5),)

    def next(self):
        if len(self.data.close) < self.p.period:
            return False
        v_end = self.data.close[0] - self.data.close[-1]
        v_start = (
            self.data.close[-(self.p.period - 2)]
            - self.data.close[-(self.p.period - 1)]
        )
        v_acc = (v_end - v_start) / (self.p.period - 2)
        if v_acc * self.p.side <= 0:
            return True
        return False


class LastBarExtremumExitCond(KeepTillCloseExitCond):
    """
    当前价格低于开仓前第一个bar 极值
    """

    alias = ("LastBarExtremumExitCond",)  # 别名

    def next(self):
        open_cond = self.datas[1]
        if self.p.side == self.LONG and open_cond is not None:
            if self.data.close[0] < open_cond.lastBar["low"]:
                return True

        if self.p.side == self.SHORT and open_cond is not None:
            if self.data.close[0] > open_cond.lastBar["high"]:
                return True
        return False


class MakerProfitExitCond(ExitCond):
    """
    主动止盈仓位退出条件
    有仓位就开，并且只开一次，价格处于open_price与stop_rate1之间开第一档，否则开第二档
    """

    alias = ("MakerProfitExitCond",)  # 别名
    params = (
        ("pos_rate", 0.5),  # 止盈仓位数量
        ("stop_rate1", 0.005),
        ("stop_rate2", 0.025),
    )

    def next(self):
        open_price = self.broker.get_open_price()
        res = open_price > 0 and self.broker.no_order()
        if res:
            if self.broker.no_order():
                self.update_extra_args(exit=True, keep_exit=False)
            else:
                self.update_extra_args(exit=False, keep_exit=False)
        return res

    def submit_close_order(self):
        open_price = self.broker.get_open_price()
        current_price = self.data.close[0]

        if (current_price - open_price) * self.p.side <= (
            open_price * self.p.stop_rate1
        ):
            # 下第一档
            order_price = open_price * (1 + self.p.stop_rate1 * self.p.side)
            pos_size = self.broker.get_size() * self.p.pos_rate
        else:
            # 下第二档
            order_price = open_price * (1 + self.p.stop_rate2 * self.p.side)
            pos_size = self.broker.get_size()

        if self.p.side == self.SHORT:
            self._owner.buy(
                exectype="Limit",
                price=order_price,
                timeInForce="GTC",
                size=pos_size,
                trigger=self.get_name(),
                side="short",
            )
            return True
        if self.p.side == self.LONG:
            self._owner.sell(
                exectype="Limit",
                price=order_price,
                timeInForce="GTC",
                size=pos_size,
                trigger=self.get_name(),
                side="long",
            )
            return True
        return False


class PriceCrossLagMAExitCond(ExitCond):
    """
    价格穿越前N个bar的高/低均值退出
    """

    alias = ("PriceCrossLagMAExitCond",)

    params = (("ma_period", 2),)

    def __init__(self):
        self.crossup = Gt(
            self.data.close,
            Lag(
                bt.indicators.MovingAverageSimple(
                    self.data.high, period=self.p.ma_period
                )
            ),
        )
        self.crossdown = Lt(
            self.data.close,
            Lag(
                bt.indicators.MovingAverageSimple(
                    self.data.low, period=self.p.ma_period
                )
            ),
        )

    def next(self):
        if self.p.side == self.LONG:
            return self.crossdown[0]
        if self.p.side == self.SHORT:
            return self.crossup[0]


class ManualPriceCrossLagMAExitCond(ExitCond):
    """
    价格穿越上一个bar的高/低值退出，适用人工智能策略
    """

    alias = ("ManualPriceCrossLagMAExitCond",)

    params = (("ma_period", 2),)

    def __init__(self):
        self.bar_index = 0
        self.new_bar = BarStart()
        self.crossup = Gt(
            self.data.close,
            Lag(
                bt.indicators.MovingAverageSimple(
                    self.data.high, period=self.p.ma_period
                )
            ),
        )
        self.crossdown = Lt(
            self.data.close,
            Lag(
                bt.indicators.MovingAverageSimple(
                    self.data.low, period=self.p.ma_period
                )
            ),
        )

    def next(self):
        if len(self.data) < self.p.ma_period:
            return False
        size = self.broker.get_size()
        if size == 0:
            self.bar_index = 0
            return False
        if self.new_bar[0] and not self.new_bar[-1]:
            self.bar_index += 1
        if self.bar_index < self.p.ma_period:
            return False
        if self.p.side == self.LONG:
            return self.crossdown[0]
        if self.p.side == self.SHORT:
            return self.crossup[0]


class RSIExitCond(LiveKeepTillCloseExitCond):
    """
    RSI加速退出
    """

    alias = ("RSIExitCond",)

    params = (("rsi_period", 14),)

    def __init__(self):
        self.rsi = bt.indicators.RSI_Safe(self.data, period=self.p.rsi_period)

    def next(self):
        if (self.rsi[0] > 90 and self.p.side == self.LONG) or (
            self.rsi[0] < 10 and self.p.side == self.SHORT
        ):
            return True
        return False
