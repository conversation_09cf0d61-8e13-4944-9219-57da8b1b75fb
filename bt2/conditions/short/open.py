from ..open import OpenOrderCond
import backtrader as bt
from ...indicators import *
import datetime


class NewLowBalanceOverPriceOpenCond(OpenOrderCond):
    """
    根据新低程度和资金计算账户价值开仓, 超价
    """

    alias = ("NewLowBalanceOverPriceOpenCond",)  # 别名

    params = (
        ("symbol_count", 50),
        ("value_ratio", 0.5),
        ("price_over_ratio", 0.005),
    )

    def __init__(self):
        self.dlaglow = bt.ind.Lowest(Lag(self.data.low), period=self.day_period)
        self.d10low = Bn10DayLowPlatInd(self.data)
        self.d30low = Bn30DayLowPlatInd(self.data)
        self.order = None

    def submit_open_order(self):
        """提交开仓订单"""
        # 新低程度
        if self.data.close[0] < self.d30low[0]:
            value_ratio = 4
        elif self.data.close[0] < self.d10low[0]:
            value_ratio = 2
        elif self.data.close[0] < self.dlaglow[0] * 1.001:
            value_ratio = 0.5
        else:
            value_ratio = self.p.value_ratio

        # 创建并提交新订单
        cashbalance = self.broker.get_cashbalance()
        lever = self.broker.get_levers()[self.data]
        value = cashbalance / self.p.symbol_count * lever * value_ratio
        price = (
            self.data.close[0]
            + self.data.close[0] * self.p.price_over_ratio * self.p.side
        )
        if self.p.side == self.LONG:
            action = self._owner.buy
            side = "long"
        else:
            action = self._owner.sell
            side = "short"
        self.order = action(
            side=side,
            exectype="Limit",
            price=price,
            valid=bt.num2date(self.data.get_curr_dt()) + datetime.timedelta(seconds=2),
            parValue=value,
            timeInForce="IOC",
        )
        return True
