from ...exit import ExitCond, KeepTillCloseExitCond
from ....indicators import *
import datetime


class SARSwitchToBelowExitCond(ExitCond):
    """
    sar反转退出, 价格进入上涨空间
    一个bar只执行一次
    """

    alias = ("SARSwitchToBelowExitCond",)

    def __init__(self):
        self.sar = bt.indicators.ParabolicSAR(b_plot=True)
        self.sar_switch = Switch(Gt(self.data, self.sar))
        self.toggle = TogglePositive(BarStart(), self.sar_switch)
        self.trigger = And(self.toggle, self.sar_switch)

    def next(self):
        return self.trigger[0]

    def submit_close_order(self):
        # 创建并提交新订单
        size = self.broker.get_size() * 0.5
        self.order = self._owner.close(
            exectype="Limit",
            valid=bt.num2date(self.data.get_curr_dt()) + datetime.timedelta(seconds=2),
            priceMatch="OPPONENT_5",
            size=size,
            timeInForce="GTC",
            trigger=self.get_name(),
        )
        return True


class SARShortSwitch4ShortExitCond(KeepTillCloseExitCond):
    """
    sar持续过短反转
    """

    alias = ("SARShortSwitch4ShortExitCond",)

    params = (("length", 3),)

    def __init__(self):
        self.sar = bt.indicators.ParabolicSAR(b_plot=True)
        self.sar_above = Lt(self.data, self.sar)
        self.sar_above_len = Step(ValueKeepLength(self.sar_above, value=1), b_plot=True)
        self.sar_above_len_lt = LteValue(self.sar_above_len, num=self.p.length)
        self.sar_below = Gt(self.data, self.sar)
        self.trigger = And(self.sar_above_len_lt, self.sar_below)

    def next(self):
        return self.trigger[0]
