from ....conditions import SAREntryCond
from ....indicators import *


class SARPricePotEntryCond(SAREntryCond):
    """
    sar处于价格下方时, 下跌空间潜力大于一定阈值：abs(潜力幅度)/振幅比例大于阈值
    """

    alias = ("SARPricePotEntryCond",)

    params = (
        ("amp_thres", 50),
        ("b_plot", False),
    )

    def __init__(self):
        self.sar = bt.indicators.ParabolicSAR(
            period=self.p.sar_period, af=self.p.sar_af, b_plot=self.p.b_plot
        )
        self.sar_below = Lt(self.sar, self.data.close, b_plot=self.p.b_plot)
        self.abs_price_pot_ratio = Abs(
            StepZero(DataChangeInRange(self.sar_below, self.data), b_plot=self.p.b_plot)
        )
        self.price_ratio = StepZero(
            DataAmpInRange(self.sar_below, self.data), b_plot=self.p.b_plot
        )
        self.pd_amp_ratio = Div(self.abs_price_pot_ratio, self.price_ratio) * 100
        self.pd_amp_ratio_plot = BPlot(self.pd_amp_ratio)

    def next(self):
        return self.pd_amp_ratio[0] > self.p.amp_thres


class SARBelowAmpOpenintVolumeChangeEntryCond(SAREntryCond):
    """
    价格上涨过程中：sar处于价格下方时, 持仓量、交易量大幅改变
    """

    alias = ("SARBelowAmpOpenintVolumeChangeEntryCond",)

    params = (
        ("price_thres", 5),
        ("openint_thres", 5),
        ("turnover_thres", 1500000),
        ("vol_oi_ratio_thres", 10),
        ("b_plot", False),
    )

    def __init__(self):
        self.sar = bt.indicators.ParabolicSAR(
            period=self.p.sar_period, af=self.p.sar_af, b_plot=self.p.b_plot
        )
        self.sar_below = Lt(self.sar, self.data.close, b_plot=self.p.b_plot)
        self.price_up_ratio = StepZero(
            DataChangeInRange(self.sar_below, self.data), b_plot=self.p.b_plot
        )  # 价格上涨幅度
        self.price_ratio = StepZero(
            DataAmpInRange(self.sar_below, self.data), b_plot=self.p.b_plot
        )  # 价格变化幅度
        self.openint_ratio = StepZero(
            ValueAmpInRange(self.sar_below, self.data.openint), b_plot=self.p.b_plot
        )  # 持仓量变化幅度
        self.vol_openint_ratio = self.data.volume / self.data.openint * 100
        self.vol_openint_ratio_plot = BPlot(
            self.vol_openint_ratio, b_plot=self.p.b_plot
        )
        self.turnover = self.data.close * self.data.volume
        self.turnover_plot = BPlot(self.turnover, b_plot=self.p.b_plot)
        self.large_vol_match = StepZero(
            Filter(
                self.sar_below,
                And(
                    GtValue(self.turnover, num=self.p.turnover_thres),
                    GtValue(self.vol_openint_ratio, num=self.p.vol_oi_ratio_thres),
                    b_plot=self.p.b_plot,
                ),
            )
        )
        self.large_vol_match_plot = BPlot(self.large_vol_match, b_plot=True)
        self.price_ratio_match = GtValue(self.price_ratio, num=self.p.price_thres)
        self.price_ratio_match_plot = BPlot(self.price_ratio_match, b_plot=True)
        self.openint_ratio_match = GtValue(self.openint_ratio, num=self.p.openint_thres)
        self.openint_ratio_match_plot = BPlot(self.openint_ratio_match, b_plot=True)
        self.match = Or(
            self.price_ratio_match,
            self.openint_ratio_match,
            self.large_vol_match,
            b_plot=True,
        )

    def next(self):
        return self.price_up_ratio[0] > 0 and self.match[0]


class SARSwitchToAboveEntryCond(SAREntryCond):
    """
    sar反转到价格下方，开启下跌
    """

    alias = ("SARSwitchToAboveEntryCond",)

    def __init__(self):
        self.sar = bt.indicators.ParabolicSAR(
            self.data, period=self.p.sar_period, af=self.p.sar_af
        )
        self.sar_switch = Switch(Lt(self.data, self.sar))

    def next(self):
        return self.sar_switch[0]
