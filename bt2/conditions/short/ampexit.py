### 最大振幅相关退出

from ..exit import KeepTillCloseExitCond
import backtrader as bt
from ...indicators import *
import datetime


class AmpPriceUpStopExitCond(KeepTillCloseExitCond):
    """
    上涨过大止损

    datas0: 振幅摆单条件
    datas1: 数据源
    """

    alias = ("PriceUpStopExitCond",)

    params = (("stop_ratio", 1),)

    def next(self):
        if self.datas[1].close[0] >= self.datas[0].ordered_price[0] * (
            1 + self.datas[0].ordered_max_amp[0] / 100 * self.p.stop_ratio
        ):
            self.update_extra_args(
                ordered_max_amp=self.datas[0].ordered_max_amp[0],
            )
            return True
        return False

    def submit_close_order(self):
        # 创建并提交新订单
        self._owner.close(
            exectype="Limit",
            valid=bt.num2date(self._owner.data.get_curr_dt())
            + datetime.timedelta(seconds=2),
            priceMatch="OPPONENT",
            timeInForce="IOC",
            trigger=self.get_name(),
        )
        return True


class AmpBreakevenExitCond(KeepTillCloseExitCond):
    """
    小盈利保本退出

    datas0: 振幅摆单条件
    datas1: 数据源
    """

    alias = ("AmpBreakevenExitCond",)

    params = (("stop_ratio", 0.25),)

    def __init__(self):
        self.open_lowest_price = KeepState(default=float("inf"))
        self.stop_price = None
        self.lower = None
        self.upper = None

    def update_status(self):
        if self.broker.no_order() and self.broker.no_open_position():  # 无仓位无订单
            self.open_lowest_price.reset_value()
            self.stop_price = None
        if self.broker.is_open_position():
            self.open_lowest_price.min_value(self.datas[1].close[0])
            open_price = self.broker.get_open_price()
            ordered_max_amp = self.datas[0].ordered_max_amp[0]
            self.lower = open_price * (1 - ordered_max_amp / 100 * 2)
            self.upper = open_price * (1 - ordered_max_amp / 100 * 0.5)
            if self.lower < self.open_lowest_price[0] <= self.upper:
                self.stop_price = open_price * (
                    1 - ordered_max_amp / 100 * self.p.stop_ratio
                )

    def next(self):
        if self.stop_price != None and self.datas[1].close[0] >= self.stop_price:
            self.update_extra_args(
                ordered_max_amp=self.datas[0].ordered_max_amp[0],
                stop_price=self.stop_price,
                lower=self.lower,
                upper=self.upper,
            )
            return True
        return False


class AmpStopProfitExitCond(KeepTillCloseExitCond):
    """
    大盈利止盈退出

    datas0: 振幅摆单条件
    datas1: 数据源
    """

    alias = ("AmpStopProfitExitCond",)

    params = (("amp_ratio", 2),)

    def next(self):
        open_price = self.broker.get_open_price()
        ordered_max_amp = self.datas[0].ordered_max_amp[0]
        stop_price = open_price * (1 - ordered_max_amp / 100 * self.p.amp_ratio)
        if self.datas[1].close[0] <= stop_price:
            self.update_extra_args(
                ordered_max_amp=self.datas[0].ordered_max_amp[0], stop_price=stop_price
            )
            return True
        return False
