## 进入条件

from ..entry import EntryCond, LiveEntryCond
from ...indicators import *


class GtCmpPriceDownEntryCond(EntryCond):
    """
    跌幅大于对比品种
    """

    alias = ("GtCmpPriceDownEntryCond",)

    def __init__(self):
        self.pricedown = Lag(PriceChangeRate(self.datas[0]))
        self.pricedown_cmp = Lag(PriceChangeRate(self.datas[1]))
        self.pricedown_cmp_lt = LtValue(self.pricedown_cmp, num=0)  # 指数价格下跌

        self.gt = Lt(self.pricedown, self.pricedown_cmp)  # 跌幅大于对比品种 ，-3% < -2%

    def next(self):
        return self.gt[0] and self.pricedown_cmp_lt[0]


class PriceBelowEntryCond(EntryCond):
    """
    价格低于最近一个bar的最低价
    """

    alias = ("PriceBelowEntryCond",)

    def __init__(self):
        self.lag_low = Lag(self.data.low)

    def next(self):
        if self.data.close[0] < self.lag_low:
            return True
        return False


class NearLowEntryCond(EntryCond):
    """
    价格逼近最低价, 但没有达到最低价
    """

    alias = ("NearLowEntryCond",)

    def __init__(self):
        self.dlaghigh = bt.ind.Highest(Lag(self.data.high), period=self.day_period)
        self.dlaglow = bt.ind.Lowest(Lag(self.data.low), period=self.day_period)
        self.delta = self.dlaghigh - self.dlaglow

    def next(self):
        if (
            self.dlaglow[0] + self.delta[0] * 0.1
            >= self.data.close[0]
            > self.dlaglow[0] * 1.001
        ):
            return True
        return False


class RSIDownEntryCond(EntryCond):
    """
    rsi小于阈值进入
    """

    alias = ("RSIDownEntryCond",)  # 别名

    params = (
        ("rsi_period", 5),
        ("rsi_up_thres", 30),
    )

    def __init__(self):
        self.rsi = bt.indicators.RSI_Safe(self.data, period=self.p.rsi_period)

    def next(self):
        if self.rsi[0] <= self.p.rsi_up_thres:
            return True
        return False


class PriceDownBreakEntryCond(EntryCond):
    """
    价格下跌满足门槛值和突破值关系
    """

    alias = ("PriceDownBreakEntryCond",)

    def __init__(self):
        # day low
        self.dlaglow = bt.ind.Lowest(Lag(self.data.low), period=self.day_period)
        # 振幅
        self.amp = AmplitudeRate(period=self.day_period)
        # 门槛值
        self.entry_value = None
        # 突破值
        self.break_value = None
        # 尝试次数
        self.try_count = 0

    def next(self):
        if self.entry_value == None:
            if self.p.simulate or self._owner.data.is_live_status():
                self.entry_value = self.dlaglow[0]
                self.break_value = self.entry_value * (1 - 0.1 * self.amp[0] / 100)
                self.update_extra_args(
                    origin_entry_value=self.entry_value,
                    origin_break_value=self.break_value,
                )
        if self.entry_value * 1.001 >= self.data.close[0] > self.break_value * 1.001:
            if self.try_count < 2:
                self.try_count += 1
                self.update_extra_args(
                    first_entry=True,
                    try_count=self.try_count,
                    entry_value=self.entry_value,
                    break_value=self.break_value,
                )
                return True
        if self.data.close[0] <= self.break_value * 1.001:
            self.entry_value = self.break_value
            self.break_value = self.entry_value * (1 - 0.1 * self.amp[0] / 100)
            self.try_count = 1
            self.update_extra_args(
                first_entry=False,
                try_count=self.try_count,
                entry_value=self.entry_value,
                break_value=self.break_value,
            )
            return True
        return False


class Near30DayLowEntryCond(LiveEntryCond):
    """
    价格接近30日新低(包含当日)
    """

    alias = ("Near30DayLowEntryCond",)

    def __init__(self):
        self.mlow = Bn30DayLowPlatInd()
        self.dlaghigh = bt.ind.Highest(Lag(self.data.high), period=self.day_period)
        self.dlaglow = bt.ind.Lowest(Lag(self.data.low), period=self.day_period)
        self.delta = self.dlaghigh - self.dlaglow

    def next(self):
        if (
            min(self.mlow[0], self.dlaglow[0]) + self.delta[0] * 0.1
            >= self.data.close[0]
        ):
            return True
        return False


class Near10DayLowEntryCond(LiveEntryCond):
    """
    价格接近10日新低(包含当日)
    """

    alias = ("Near10DayLowEntryCond",)

    params = (("delta_ratio", 0.15),)

    def __init__(self):
        self.d10low = Bn10DayLowPlatInd()
        self.dlaghigh = bt.ind.Highest(Lag(self.data.high), period=self.day_period)
        self.dlaglow = bt.ind.Lowest(Lag(self.data.low), period=self.day_period)
        self.delta = self.dlaghigh - self.dlaglow

    def next(self):
        if (
            min(self.d10low[0], self.dlaglow[0]) + self.delta[0] * self.p.delta_ratio
            >= self.data.close[0]
        ):
            return True
        return False


class NewLowCountEntryCond(LiveEntryCond):
    """
    24h价格突破新低次数
    """

    alias = ("NewLowCountEntryCond",)

    params = (("brk_count", 2),)

    def __init__(self):
        self.count = Bn24HNewLowCountPlatInd()

    def next(self):
        if self.count[0] >= self.p.brk_count:
            return True
        return False


class MADescendingOrderEntryCond(LiveEntryCond):
    """
    均线空头排列
    """

    alias = ("MADescendingOrderEntryCond",)

    params = (("ma_periods", (3, 5, 7)),)

    def __init__(self):
        self.mas = [
            bt.indicators.SMA(self.data, period=period) for period in self.p.ma_periods
        ]
        self.ma_order = MultiDatasOrder(*self.mas, asc=True)

    def next(self):
        return self.ma_order[0]


class ADXRDecreaseEntryCond(EntryCond):
    """
    adxr连续下跌且大于阈值
    """

    alias = ("ADXRDecreaseEntryCond",)

    params = (
        ("dmi_period", 7),
        ("thres", 30),
    )

    def __init__(self):
        self.adxr = AverageDirectionalMovementIndexRatingSafe(period=self.p.dmi_period)
        self.adxrgt = GtValue(self.adxr, num=self.p.thres)
        self.adxr_inc = ADXRIncrease(dmi_period=self.p.dmi_period)

    def next(self):
        return self.adxr_inc[0] and self.adxrgt[0]


class ADXNegativeEntryCond(EntryCond):
    """
    adxr下跌信号
    """

    alias = ("ADXNegativeEntryCond",)

    params = (("dmi_period", 7),)

    def __init__(self):
        self.adxr_inc = ADXNegative(dmi_period=self.p.dmi_period)

    def next(self):
        return self.adxr_inc[0]
