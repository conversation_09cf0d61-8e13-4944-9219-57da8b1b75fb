## 退出条件

from ..exit import KeepTillCloseExitCond
import backtrader as bt
from ...indicators import *
import datetime


class PriceAboveATRExitCond(KeepTillCloseExitCond):
    """
    价格高于上一个最高价n倍atr退出
    """

    alias = ("PriceAboveATRExitCond",)

    params = (
        ("atr_period", 20),
        ("atr_n", 1),
    )

    def __init__(self):
        self.atr = bt.indicators.ATR(period=self.p.atr_period)
        self.highest = Lag(self.data.high)
        self.higher_price = self.highest + self.atr * self.p.atr_n
        self.trigger = Gt(self.data.close, self.higher_price)

    def next(self):
        return self.trigger[0]


class PriceAboveSARSwitchLowExitCond(KeepTillCloseExitCond):
    """
    价格高于sar反转时最大值退出
    """

    alias = ("PriceAboveSARSwitchLowExitCond",)

    params = (("low_num", 5),)

    def __init__(self):
        self.sar = bt.indicators.PSAR()
        self.highest = bt.indicators.Highest(self.data.low, period=self.p.low_num)
        self.sar_switch = Switch(Lt(self.data, self.sar))
        self.switch_high = Step(Filter(self.sar_switch, self.highest))

        self.price_above_high = Gt(self.data, self.switch_high)

    def next(self):
        return self.price_above_high[0]


class BenchMarkBarPriceUpExitCond(KeepTillCloseExitCond):
    """
    以开仓后的出现的大振幅bar作为标的, 当前价大于标的最高价, 则退出
    """

    alias = ("BenchMarkBarPriceUpExitCond",)

    params = (("benchmark_thres", 2),)

    def __init__(self):
        self.open_bar_num = KeepState(default=-1)  # 开仓经过的bar的数量
        self.benchmark_bar_price_high = KeepState(default=float("inf"))
        self.amplag = AmplitudeRateLag1(self.data)

    def update_status(self):

        if self.broker.is_open_position():
            if self.open_bar_num[0] == -1:
                self.open_bar_num.set_value(0)
            elif self.open_bar_num[0] == self.open_bar_num[-1]:
                self.logger.info(
                    "datetime:%s, open_bar_num:%s",
                    bt.num2date(self.data.datetime[0]),
                    self.open_bar_num[0],
                )
                self.open_bar_num.set_value(self.open_bar_num[0] + 1)
            else:
                pass
        else:
            self.open_bar_num.reset_value()
            self.benchmark_bar_price_high.reset_value()
        if self.open_bar_num[0] >= 2:
            if self.amplag[0] >= self.p.benchmark_thres:
                self.benchmark_bar_price_high.set_value(self.data.high[-1])
            else:
                pass

    def next(self):
        if self.data.close[0] > self.benchmark_bar_price_high[0]:
            return True
        return False


class SARCrossingBelowExitCond(KeepTillCloseExitCond):
    """
    开仓时sar在价格以上, 开仓后sar下穿价格退出
    """

    alias = ("SARCrossingBelowExitCond",)

    def __init__(self):
        self.sar = bt.indicators.PSAR(self.data, af=0.02)
        self.sar_over_price = KeepState(default=0)
        self.no_open_before = True  # never opened before

    def update_status(self):
        if self.broker.no_order() and self.broker.no_open_position():  # 无仓位无订单
            self.no_open_before = True
            self.sar_over_price.set_value(0)
        if self.broker.is_open_position() and self.no_open_before:
            self.no_open_before = False
            if self.sar[0] > self.data.close[0] and self.sar_over_price[0] == 0:
                self.sar_over_price.set_value(1)

    def next(self):
        if self.sar[0] < self.data.close[0] and self.sar_over_price[0]:
            return True
        return False


class VolumeSurgeExitCond(KeepTillCloseExitCond):
    """
    放量退出,仅用于做空！！！
    """

    alias = ("VolumeSurgeExitCond",)

    params = (
        ("vol_period", 3),
        ("vol_ratio", 5),
    )

    def __init__(self):
        self._exit = False
        self.avg_vol = bt.indicators.SMA(
            Lag(self.data.volume), period=self.p.vol_period
        )
        self.dlaglow = bt.ind.Lowest(Lag(self.data.low), period=self.day_period)

    def next(self):
        open_price = self.broker.get_open_price()
        if (
            open_price
            and self.data.volume[0] >= self.avg_vol[0] * self.p.vol_ratio
            and self.dlaglow * 1.001 < self.data.close[0] < open_price * 0.99
        ):
            self.update_extra_args(volume=self.data.volume[0], open_price=open_price)
            return True
        return False

    def submit_close_order(self):
        # 创建并提交新订单
        price = self.data.close[0] * 0.999
        self.order = self._owner.close(
            exectype="Limit",
            valid=bt.num2date(self.data.get_curr_dt()) + datetime.timedelta(seconds=2),
            price=price,
            timeInForce="GTC",
            trigger=self.get_name(),
        )
        return True


class MAAscendingOrderExitCond(KeepTillCloseExitCond):
    """
    均线多头排列
    """

    alias = ("MAAscendingOrderExitCond",)

    params = (("ma_periods", (3, 5)),)

    def __init__(self):
        self.mas = [
            bt.indicators.SMA(self.data, period=period) for period in self.p.ma_periods
        ]
        self.ma_order = MultiDatasOrder(*self.mas)

    def next(self):
        return self.ma_order[0]
