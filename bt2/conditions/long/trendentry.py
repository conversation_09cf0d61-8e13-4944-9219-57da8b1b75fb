# 蓄势相关进入

from ..entry import EntryCond
import backtrader as bt
from ...indicators import *


class TrendRangeEntryCond(EntryCond):
    """
    出现振幅较大的k线后划分价格范围, 定势确定;
    时间足够长, 突破次数足够多, 蓄势确定;
    """

    alias = ("TrendRangeEntryCond",)

    params = (
        ("priceup_thres", 2),  # 定势涨幅
        ("trend_period", 40),  # 定势长度
    )

    plotinfo = (("b_subplot", False),)

    plotlines = dict(
        value=dict(
            b_type="vstrip",
            b_color="bisque",
        )
    )

    def __init__(self):
        self.laglowest = bt.indicators.Lowest(
            Lag(self.data.low, period=self.p.trend_period // 2),
            period=self.p.trend_period // 2,
        )
        self.priceup = (self.data.close - self.laglowest) / self.data.close * 100
        self.priceupgt = GtValue(self.priceup, num=self.p.priceup_thres)
        self.confirm_trend = Switch(Not(self.priceupgt))
        self.trend = Toggle(self.confirm_trend, Switch(self.priceupgt))
        self.highest = bt.indicators.Highest(self.data.high, period=self.p.trend_period)
        self.lowest = bt.indicators.Lowest(self.data.low, period=self.p.trend_period)
        self.highma = bt.indicators.MovingAverageSimple(self.data.high, period=5)
        self.lowma = bt.indicators.MovingAverageSimple(self.data.low, period=5)
        self.range_high = Step(
            Filter(
                self.confirm_trend, self.highest + (self.highest - self.lowest) * 0.1
            )
        )
        self.range_low = Step(
            Filter(self.confirm_trend, self.lowest + (self.highest - self.lowest) * 0.3)
        )
        self.in_range = And(
            Gte(self.lowma, self.range_low), Lte(self.highma, self.range_high)
        )
        self.range_start = And(self.confirm_trend, self.in_range)
        self.range_end = Switch(Not(self.in_range))
        self.true_range = Toggle(self.range_start, self.range_end)
        self.range_len = TriggerSetAdd(
            Or(Not(self.true_range), self.range_start), self.true_range
        )
        self.touch_upper = Switch(Gt(self.data.high, self.highma))
        self.range_touch = TriggerSetAdd(
            Or(Not(self.true_range), self.range_start), self.touch_upper
        )

    def next(self):
        return self.true_range[0] and self.range_len[0] > 20 and self.range_touch[0] > 3


class TrendEndUpEntryCond(EntryCond):
    """
    末端走强
    data0: 输入源
    data1: 定势条件

    """

    alias = ("TrendEndUpEntryCond",)

    params = (
        ("upper_ratio", 1),
        ("vol_period", 5),
        ("vol_ratio", 2),
    )

    def __init__(self):
        self.touch_upper_price = Step(
            Filter(
                SynFreq(self.data, self.datas[1].touch_upper),
                SynFreq(self.data, self.datas[1].data.high),
            )
        )
        self.price_over_upper = Gt(
            self.data.close, self.touch_upper_price * self.p.upper_ratio
        )
        self.max_vol_lag = bt.indicators.Highest(
            Lag(self.data.volume), period=self.p.vol_period
        )
        self.vol_over_max = Gt(self.data.volume, self.max_vol_lag * self.p.vol_ratio)

    def next(self):
        return self.price_over_upper[0] and self.vol_over_max[0]


class TrendPriceUpAboveCmpEntryCond(EntryCond):
    """
    趋势中涨幅大于对比品种
    data0: 输入源
    data1: 定势条件

    """

    alias = ("TrendPriceUpAboveCmpEntryCond",)

    plotinfo = (("b_subplot", False),)

    plotlines = dict(
        value=dict(
            b_type="vstrip",
            b_color="bisque",
        )
    )

    def __init__(self):
        self.start_price = Step(
            Filter(
                SynFreq(self.data, self.datas[1].range_start),
                SynFreq(self.data, self.datas[1].data.open),
            )
        )
        self.end_price = Filter(
            SynFreq(self.data, self.datas[1].true_range),
            SynFreq(self.data, self.datas[1].data.close),
        )
        self.trend_amp = (self.end_price - self.start_price) / self.start_price
        self.cmp_start_price = Step(
            Filter(SynFreq(self.data, self.datas[1].range_start), self.data.open)
        )
        self.cmp_end_price = Filter(
            SynFreq(self.data, self.datas[1].true_range), self.data.close
        )
        self.cmp_trend_amp = (
            self.cmp_end_price - self.cmp_start_price
        ) / self.cmp_start_price

        # 作图参数
        self.start_price.plotinfo.b_plot = True
        self.start_price.plotinfo.subplot = False
        self.start_price.plotinfo.b_plotname = "start_price"

    def next(self):
        return self.trend_amp[0] > self.cmp_trend_amp[0]
