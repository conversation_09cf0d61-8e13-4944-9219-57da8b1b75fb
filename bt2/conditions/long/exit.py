## 退出条件

from ..exit import KeepTillCloseExitCond
import backtrader as bt
from ...indicators import *
import datetime


class YOYOExitCond(KeepTillCloseExitCond):
    """
    YOYO退出
    """

    alias = ("YOYOExitCond",)

    params = (
        ("atr_period", 5),
        ("atr_ratio", 1),
    )

    def __init__(self):
        self.atr = bt.indicators.ATR(self.data, period=self.p.atr_period)

    def next(self):
        if (self.data.close[-1] - self.data.close[0]) >= self.p.atr_ratio * self.atr[0]:
            return True
        return False


class VolumeSurgeRSIExitCond(KeepTillCloseExitCond):
    """
    放量冲高退出
    """

    alias = ("VolumeSurgeRSIExitCond",)

    params = (
        ("rsi_period", 5),
        ("rsi_thres", 95),
        ("vol_period", 3),
    )

    def __init__(self):
        self.avg_vol = bt.indicators.SMA(
            Lag(self.data.volume), period=self.p.vol_period
        )
        self.rsi = bt.indicators.RSI_Safe(self.data, period=self.p.rsi_period)
        self.dlaghigh = bt.ind.Highest(Lag(self.data.high), period=self.day_period)

    def next(self):
        open_price = self.broker.get_open_price()
        if (
            open_price
            and self.data.volume[0] >= self.avg_vol[0] * 3
            and self.rsi[0] >= self.p.rsi_thres
            and open_price * 1.01 <= self.data.close[0] < self.dlaghigh[0] * 0.999
        ):
            return True
        return False

    def submit_close_order(self):
        # 创建并提交新订单
        price = self.data.close[0] * 1.001
        self.order = self._owner.close(
            exectype="Limit",
            valid=bt.num2date(self.data.get_curr_dt()) + datetime.timedelta(seconds=2),
            price=price,
            timeInForce="GTC",
            trigger=self.get_name(),
        )
        return True


class RSIWeakExitCond(KeepTillCloseExitCond):
    """
    rsi转弱退出
    """

    alias = ("RSIWeakExitCond",)

    params = (
        ("rsi_period", 15),
        ("rsi_thres", 50),
    )

    def __init__(self):
        self.rsi = bt.indicators.RSI_Safe(self.data, period=self.p.rsi_period)

    def next(self):
        if self.rsi[0] < self.p.rsi_thres:
            return True
        return False


class BenchMarkBarPriceDownExitCond(KeepTillCloseExitCond):
    """
    以开仓后的出现的大振幅bar作为标的, 当前价小于标的最低价, 则退出
    """

    alias = ("BenchMarkBarPriceDownExitCond",)

    params = (("benchmark_thres", 2),)

    def __init__(self):
        self.open_bar_num = KeepState(self.data, default=-1)  # 开仓经过的bar的数量
        self.benchmark_bar_price_low = KeepState(default=0)
        self.amplag = AmplitudeRateLag1(self.data)

    def update_status(self):
        if self.broker.is_open_position():
            if self.open_bar_num[0] == -1:
                self.open_bar_num.set_value(0)
            elif self.open_bar_num[0] == self.open_bar_num[-1]:
                self.logger.info(
                    "datetime:%s, open_bar_num:%s",
                    bt.num2date(self.data.datetime[0]),
                    self.open_bar_num[0],
                )
                self.open_bar_num.set_value(self.open_bar_num[0] + 1)
            else:
                pass
        else:
            self.open_bar_num.set_value(-1)
            self.benchmark_bar_price_low.set_value(0)
        if self.open_bar_num[0] >= 2:
            if self.amplag[0] >= self.p.benchmark_thres:
                self.benchmark_bar_price_low.set_value(self.data.low[-1])
            else:
                pass

    def next(self):
        if self.data.close[0] < self.benchmark_bar_price_low[0]:
            return True
        return False


class SARCrossingAboveExitCond(KeepTillCloseExitCond):
    """
    开仓时sar在价格以下, 开仓后sar上穿价格退出
    """

    alias = ("SARCrossingAboveExitCond",)

    def __init__(self):
        self.sar = bt.indicators.PSAR(self.data, af=0.02)
        self.sar_under_price = KeepState(default=0)
        self.no_open_before = True  # never opened before

    def update_status(self):
        if self.broker.no_order() and self.broker.no_open_position():  # 无仓位无订单
            self.no_open_before = True
            self.sar_under_price.set_value(0)
        if self.broker.is_open_position() and self.no_open_before:
            self.no_open_before = False
            if self.sar[0] < self.data.close[0] and self.sar_under_price[0] == 0:
                self.sar_under_price.set_value(1)

    def next(self):
        # 当bar数据一样时，sar无法计算会报异常
        try:
            if self.sar[0] > self.data.close[0] and self.sar_under_price[0]:
                return True
        except Exception:
            pass
        return False


class MAAndSARCrossExitCond(KeepTillCloseExitCond):
    """
    sar上穿MA下穿
    """

    alias = ("MAAndSARCrossExitCond",)

    params = (("ma_period", 10),)

    def __init__(self):
        self.sar = bt.indicators.PSAR(self.data, af=0.02)
        self.ma = bt.indicators.SMA(period=self.p.ma_period)

    def next(self):
        if self.sar[0] > self.data.close[0] and self.ma[0] > self.data.close[0]:
            return True
        return False


class MADescendingOrderExitCond(KeepTillCloseExitCond):
    """
    均线空头排列
    """

    alias = ("MADescendingOrderExitCond",)

    params = (("ma_periods", (3, 5)),)

    def __init__(self):
        self.mas = [
            bt.indicators.SMA(self.data, period=period) for period in self.p.ma_periods
        ]
        self.ma_order = MultiDatasOrder(*self.mas, asc=True)

    def next(self):
        return self.ma_order[0]


class PriceBelowOpenLowExitCond(KeepTillCloseExitCond):
    """
    价格低于开仓bar低值
    """

    alias = ("PriceBelowOpenLowExitCond",)

    def __init__(self):
        self.open_low = None

    def update_status(self):
        if self.broker.is_open_position() and not self.open_low:
            self.open_low = self.data.low[0]
        if self.broker.no_open_position():
            self.open_low = None

    def next(self):
        return self.data.close[0] < self.open_low


class PriceBelowLowExitCond(KeepTillCloseExitCond):
    """
    价格低于过去bar低值
    """

    alias = ("PriceBelowLowExitCond",)

    params = (("low_period", 5),)

    def __init__(self):
        self.lag_low = bt.indicators.Lowest(Lag(self.data.low), period=5)

    def next(self):
        return self.data.close[0] < self.lag_low[0]


class PriceBelowSARSwitchLowExitCond(KeepTillCloseExitCond):
    """
    价格低于sar反转时最低值退出
    """

    alias = ("PriceBelowSARSwitchLowExitCond",)

    params = (("low_num", 5),)

    def __init__(self):
        self.sar = bt.indicators.PSAR()
        self.lowest = bt.indicators.Lowest(self.data.low, period=self.p.low_num)
        self.sar_switch = Switch(Gt(self.data, self.sar))
        self.switch_low = Step(Filter(self.sar_switch, self.lowest))

        self.price_below_low = Lt(self.data, self.switch_low)

    def next(self):
        return self.price_below_low[0]


class PriceBelowATRExitCond(KeepTillCloseExitCond):
    """
    价格低于上一个最低价n倍atr退出
    """

    alias = ("PriceBelowATRExitCond",)

    params = (
        ("atr_period", 20),
        ("atr_n", 1),
    )

    def __init__(self):
        self.atr = bt.indicators.ATR(period=self.p.atr_period)
        self.lowest = Lag(self.data.low)
        self.lower_price = self.lowest - self.atr * self.p.atr_n
        self.trigger = Lt(self.data.close, self.lower_price)

    def next(self):
        return self.trigger[0]


class LongButOpenIntDescExitCond(KeepTillCloseExitCond):
    """
    持仓量下行退出，一般用于做多场景下的趋势验证
    """

    alias = ("LongButOpenIntDescExitCond",)
    params = (("open_desc_th", -1),)  # 持仓量变化阈值%

    def __init__(self):
        self.sar = bt.indicators.ParabolicSAR(b_plot=True)
        self.price_up = Gt(self.data, self.sar)
        self.openint_change_ratio = StepZero(
            ValueChangeInRange(self.price_up, self.data.openint)
        )

    def next(self):
        return self.openint_change_ratio[0] < self.p.open_desc_th  # 持仓量下跌超过阈值
