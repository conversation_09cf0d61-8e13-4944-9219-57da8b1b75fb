### 最大振幅相关退出

from ..exit import ExitCond, KeepTillCloseExitCond
import backtrader as bt
from ...indicators import *
import datetime


class AmpPriceBelowLowestExitCond(KeepTillCloseExitCond):
    """
    当前价小于等于开仓最低价止损

    datas0: 振幅摆单条件
    datas1: 数据源
    """

    alias = ("AmpPriceBelowLowestExitCond",)

    def next(self):
        if self.datas[1].close[0] <= self.datas[0].ordered_lowest[0]:
            self.update_extra_args(
                ordered_max_amp=self.datas[0].ordered_max_amp[0],
            )
            return True
        return False


class AmpPriceAboveHighestExitCond(ExitCond):
    """
    当前价大于等于开仓最低价, 部分止盈

    datas0: 振幅摆单条件
    datas1: 数据源
    """

    alias = ("AmpPriceAboveHighestExitCond",)

    def __init__(self):
        self.order = None
        self.closed = False

    def update_status(self):
        if self.broker.no_open_position():
            self.closed = False

    def next(self):
        if self.closed:  # 已经平过一次
            return False
        if self.datas[1].close[0] >= self.datas[0].ordered_highest[0]:
            self.update_extra_args(
                ordered_max_amp=self.datas[0].ordered_max_amp[0],
            )
            self.closed = True
            return True
        return False

    def submit_close_order(self):
        """
        提交止盈订单
        部分止盈
        """
        if self.order is not None and self.order.alive():  # 订单存活
            return True
        size = self.broker.get_size() * (2 / 3)
        # 创建并提交新订单
        self.order = self._owner.close(
            size=size,
            exectype="Limit",
            valid=bt.num2date(self._owner.data.get_curr_dt())
            + datetime.timedelta(seconds=2),
            priceMatch="QUEUE",
            timeInForce="GTC",
            trigger=self.get_name(),
        )
        return True


class AmpPriceDownStopExitCond(KeepTillCloseExitCond):
    """
    下跌过大止损

    datas0: 振幅摆单条件
    datas1: 数据源
    """

    alias = ("PriceUpStopExitCond",)

    params = (("stop_ratio", 1),)

    def next(self):
        if self.datas[1].close[0] <= self.datas[0].ordered_price[0] * (
            1 - self.datas[0].ordered_max_amp[0] / 100 * self.p.stop_ratio
        ):
            self.update_extra_args(
                ordered_max_amp=self.datas[0].ordered_max_amp[0],
            )
            return True
        return False

    def submit_close_order(self):
        # 创建并提交新订单
        self._owner.close(
            exectype="Limit",
            valid=bt.num2date(self._owner.data.get_curr_dt())
            + datetime.timedelta(seconds=2),
            priceMatch="OPPONENT",
            timeInForce="IOC",
            trigger=self.get_name(),
        )
        return True


class AmpBreakevenExitCond(KeepTillCloseExitCond):
    """
    小盈利保本退出

    datas0: 振幅摆单条件
    datas1: 数据源
    """

    alias = ("AmpBreakevenExitCond",)

    params = (("stop_ratio", 0.25),)

    def __init__(self):
        self.open_highest_price = KeepState(default=float("-inf"))

    def update_status(self):
        if self.broker.no_order() and self.broker.no_open_position():  # 无仓位无订单
            self.open_highest_price.reset_value()
            self.stop_price = None
        if self.broker.is_open_position():
            self.open_highest_price.max_value(self.datas[1].close[0])
            open_price = self.broker.get_open_price()
            ordered_max_amp = self.datas[0].ordered_max_amp[0]
            self.lower = open_price * (1 + ordered_max_amp / 100 * 0.5)
            self.upper = open_price * (1 + ordered_max_amp / 100 * 2)
            if self.lower < self.open_highest_price[0] <= self.upper:
                self.stop_price = open_price * (
                    1 + ordered_max_amp / 100 * self.p.stop_ratio
                )

    def next(self):
        if self.stop_price != None and self.datas[1].close[0] <= self.stop_price:
            self.update_extra_args(
                ordered_max_amp=self.datas[0].ordered_max_amp[0],
                stop_price=self.stop_price,
                lower=self.lower,
                upper=self.upper,
            )
            return True
        return False


class AmpStopProfitExitCond(KeepTillCloseExitCond):
    """
    大盈利止盈退出

    datas0: 振幅摆单条件
    datas1: 数据源
    """

    alias = ("AmpStopProfitExitCond",)

    def next(self):
        open_price = self.broker.get_open_price()
        ordered_max_amp = self.datas[0].ordered_max_amp[0]
        stop_price = open_price * (1 + ordered_max_amp / 100 * 2)
        if self.datas[1].close[0] >= stop_price:
            self.update_extra_args(
                ordered_max_amp=self.datas[0].ordered_max_amp[0],
                stop_price=stop_price,
            )
            return True
        return False
