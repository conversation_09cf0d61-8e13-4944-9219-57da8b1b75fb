from ..open import OpenOrderCond
import backtrader as bt
from ...indicators import *


class NewHighBalanceOverPriceOpenCond(OpenOrderCond):
    """
    根据新高程度和资金计算账户价值开仓, 超价
    """

    alias = ("NewHighBalanceOverPriceOpenCond",)  # 别名

    params = (
        ("symbol_count", 50),
        ("value_ratio", 0.5),
        ("price_over_ratio", 0.005),
    )

    def __init__(self):
        self.dlaghigh = bt.ind.Highest(Lag(self.data.high), period=self.day_period)
        self.d10high = Bn10DayHighPlatInd(self.data)
        self.d30high = Bn30DayHighPlatInd(self.data)
        self.order = None

    def submit_open_order(self):
        """提交开仓订单"""
        # 新低程度
        if self.data.close[0] > self.d30high[0]:
            value_ratio = 4
        elif self.data.close[0] > self.d10high[0]:
            value_ratio = 2
        elif self.data.close[0] > 0.999 * self.dlaghigh[0]:
            value_ratio = 0.5
        else:
            value_ratio = self.p.value_ratio

        # 创建并提交新订单
        cashbalance = self.broker.get_cashbalance()
        lever = self.broker.get_levers()[self.data]
        value = cashbalance / self.p.symbol_count * lever * value_ratio
        price = (
            self.data.close[0]
            + self.data.close[0] * self.p.price_over_ratio * self.p.side
        )
        if self.p.side == self.LONG:
            action = self._owner.buy
            side = "long"
        else:
            action = self._owner.sell
            side = "short"
        self.order = action(
            side=side,
            exectype="Limit",
            price=price,
            parValue=value,
            timeInForce="IOC",
        )
        self.update_extra_args(
            value_ratio=value_ratio,
        )
        return True
