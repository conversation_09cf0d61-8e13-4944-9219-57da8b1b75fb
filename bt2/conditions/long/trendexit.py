## 退出条件

from ..exit import KeepTillCloseExitCond
import backtrader as bt
from ...indicators import *


class PriceBelowTrendRangeLowExitCond(KeepTillCloseExitCond):
    """
    价格低于定势范围下限止损
    data0: 输入源
    data1: 定势条件

    """

    alias = ("PriceBelowTrendRangeLowExitCond",)

    def __init__(self):
        self.range_low = self.datas[1].range_low

    def next(self):
        return self.data.close[0] < self.range_low[0]


class PriceBelowTrendLenLowExitCond(KeepTillCloseExitCond):
    """
    价格低于前n个bar最低点止损
    bar长度由蓄势长度决定
    data0: 输入源(15m)
    data1: 定势条件

    """

    alias = ("PriceBelowTrendLenLowExitCond",)

    def __init__(self):
        self.range_len = Step(self.datas[1].range_len)
        self.low_15m = bt.indicators.Lowest(Lag(self.data.low), period=1)
        self.low_1h = bt.indicators.Lowest(Lag(self.data.low), period=4)
        self.low_4h = bt.indicators.Lowest(Lag(self.data.low), period=16)
        self.low_1d = bt.indicators.Lowest(Lag(self.data.low), period=96)

    def next(self):
        level = self.range_len[0] // 60
        low_thres = self.low_1d[0]
        if level <= 3:
            low_thres == self.low_4h[0]
        if level <= 2:
            low_thres == self.low_1h[0]
        if level <= 1:
            low_thres == self.low_15m[0]

        if self.data.close[0] < low_thres:
            self.update_extra_args(
                level=level,
                low_thres=low_thres,
            )
            return True
