import backtrader as bt
import datetime
from ..entry import EntryCond
from ....indicators import *


class SAREntryCond(EntryCond):
    """
    sar进入
    """

    alias = ("SAREntryCond",)
    params = (
        ("sar_period", 2),
        ("sar_af", 0.02),
    )


class PriceOverSAREntryCond(SAREntryCond):
    """
    价格超过sar
    """

    alias = ("PriceOverSAREntryCond",)

    def __init__(self):
        self.sar = bt.indicators.ParabolicSAR(
            self.data, period=self.p.sar_period, af=self.p.sar_af
        )

    def next(self):
        if self.data.close[0] > self.sar[0]:
            return True
        return False


class SARSwitchToBelowEntryCond(SAREntryCond):
    """
    sar反转
    """

    alias = ("SARSwitchToBelowEntryCond",)

    def __init__(self):
        self.sar = bt.indicators.ParabolicSAR(
            self.data, period=self.p.sar_period, af=self.p.sar_af
        )
        self.sar_switch = Switch(Gt(self.data, self.sar))

    def next(self):
        return self.sar_switch[0]


class PriceBelowSARSwitchStartEntryCond(SAREntryCond):
    """
    价格低于sar下降趋势开始时的价格
    """

    alias = ("PriceBelowSARSwitchStartEntryCond",)

    def __init__(self):
        self.sar = bt.indicators.ParabolicSAR(
            self.data, period=self.p.sar_period, af=self.p.sar_af
        )
        self.sar_switch = Switch(Lt(self.data, self.sar))
        self.sar_switch_start = Step(Filter(self.sar_switch, self.data.open))
        self.price_down_sar_switch_start = Lt(self.data.close, self.sar_switch_start)

    def next(self):
        return self.price_down_sar_switch_start[0]


class SARAboveAmpOpenintVolumeChangeEntryCond(SAREntryCond):
    """
    sar处于价格上方时, 价格下跌, 持仓量, 交易量大幅改变
    """

    alias = ("SARAboveAmpOpenintVolumeChangeEntryCond",)

    params = (
        ("pricedown_thres", 5),
        ("openint_thres", 5),
        ("turnover_thres", 1500000),
        ("vol_oi_ratio_thres", 10),
        ("b_plot", False),
    )

    def __init__(self):
        self.sar = bt.indicators.ParabolicSAR(
            period=self.p.sar_period, af=self.p.sar_af, b_plot=self.p.b_plot
        )
        self.sar_above = Gt(self.sar, self.data.close, b_plot=self.p.b_plot)
        self.price_up_ratio = StepZero(
            DataChangeInRange(self.sar_above, self.data), b_plot=self.p.b_plot
        )
        self.price_ratio = StepZero(
            DataAmpInRange(self.sar_above, self.data), b_plot=self.p.b_plot
        )
        self.openint_ratio = StepZero(
            ValueAmpInRange(self.sar_above, self.data.openint), b_plot=self.p.b_plot
        )
        self.vol_openint_ratio = self.data.volume / self.data.openint * 100
        self.vol_openint_ratio_plot = BPlot(
            self.vol_openint_ratio, b_plot=self.p.b_plot
        )
        self.turnover = self.data.close * self.data.volume
        self.turnover_plot = BPlot(self.turnover, b_plot=self.p.b_plot)
        self.large_vol = StepZero(
            Filter(
                self.sar_above,
                And(
                    GtValue(self.turnover, num=self.p.turnover_thres),
                    GtValue(self.vol_openint_ratio, num=self.p.vol_oi_ratio_thres),
                    b_plot=self.p.b_plot,
                ),
            )
        )
        self.score = (
            GtValue(self.price_ratio, num=self.p.pricedown_thres) * 100
            + GtValue(self.openint_ratio, num=self.p.openint_thres) * 100
            + self.large_vol * 100
        )
        self.score_plot = BPlot(self.score, b_plot=self.p.b_plot)

    def next(self):
        return self.price_up_ratio[0] < 0 and self.score[0] >= 100


class SARPriceDownAmpEntryCond(SAREntryCond):
    """
    sar处于价格上方时, abs(涨幅)/振幅比例大于阈值
    """

    alias = ("SARPriceDownAmpEntryCond",)

    params = (
        ("down_amp_thres", 50),
        ("b_plot", False),
    )

    def __init__(self):
        self.sar = bt.indicators.ParabolicSAR(
            period=self.p.sar_period, af=self.p.sar_af, b_plot=self.p.b_plot
        )
        self.sar_above = Gt(self.sar, self.data.close, b_plot=self.p.b_plot)
        self.abs_price_up_ratio = Abs(
            StepZero(DataChangeInRange(self.sar_above, self.data), b_plot=self.p.b_plot)
        )
        self.price_ratio = StepZero(
            DataAmpInRange(self.sar_above, self.data), b_plot=self.p.b_plot
        )
        self.pd_amp_ratio = Div(self.abs_price_up_ratio, self.price_ratio) * 100
        self.pd_amp_ratio_plot = BPlot(self.pd_amp_ratio)

    def next(self):
        return self.pd_amp_ratio[0] > self.p.down_amp_thres
