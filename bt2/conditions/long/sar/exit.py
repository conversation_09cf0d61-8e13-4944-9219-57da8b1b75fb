from ..exit import KeepTillCloseExitCond
from ...exit import ExitCond
import backtrader as bt
from ....indicators import *
import datetime


class SARSwitchToAboveExitCond(ExitCond):
    """
    sar反转退出
    一个bar只执行一次
    """

    alias = ("SARSwitchToAboveExitCond",)

    def __init__(self):
        self.sar = bt.indicators.ParabolicSAR(b_plot=True)
        self.sar_switch = Switch(Lt(self.data, self.sar))
        self.toggle = TogglePositive(BarStart(), self.sar_switch)
        self.trigger = And(self.toggle, self.sar_switch)

    def next(self):
        return self.trigger[0]

    def submit_close_order(self):
        # 创建并提交新订单
        size = self.broker.get_size() * 0.5
        self.order = self._owner.close(
            exectype="Limit",
            valid=bt.num2date(self.data.get_curr_dt()) + datetime.timedelta(seconds=2),
            priceMatch="OPPONENT_5",
            size=size,
            timeInForce="GTC",
            trigger=self.get_name(),
        )
        return True


class SARShortSwitchExitCond(KeepTillCloseExitCond):
    """
    sar持续过短反转
    """

    alias = ("SARShortSwitchExitCond",)

    params = (("length", 3),)

    def __init__(self):
        self.sar = bt.indicators.ParabolicSAR(b_plot=True)
        self.sar_below = Gt(self.data, self.sar)
        self.sar_below_len = Step(ValueKeepLength(self.sar_below, value=1), b_plot=True)
        self.sar_below_len_lt = LteValue(self.sar_below_len, num=self.p.length)
        self.sar_above = Lt(self.data, self.sar)
        self.trigger = And(self.sar_below_len_lt, self.sar_above)

    def next(self):
        return self.trigger[0]
