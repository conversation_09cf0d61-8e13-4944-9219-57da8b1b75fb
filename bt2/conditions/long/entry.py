## 进入条件

import backtrader as bt
import datetime
from ..entry import EntryCond, LiveEntryCond
from ...indicators import *


class PriceAboveEntryCond(EntryCond):
    """
    价格超过最近一个bar的最高价
    """

    alias = ("PriceAboveEntryCond",)

    def __init__(self):
        self.lag_high = Lag(self.data.high)

    def next(self):
        if self.data.close[0] > self.lag_high[0]:
            return True
        return False


class PriceAboveMAEntryCond(EntryCond):
    """
    价格超过均线
    """

    alias = ("PriceAboveMAEntryCond",)

    params = (("ma_period", 10),)

    def __init__(self):
        self.ma = bt.indicators.MovingAverageSimple(period=self.p.ma_period)

    def next(self):
        if self.data.close[0] > self.ma[0]:
            return True
        return False


class NearHighEntryCond(EntryCond):
    """
    价格逼近最高价, 但没有达到最高价
    """

    alias = ("NearHighEntryCond",)

    def __init__(self):
        self.dlaghigh = bt.ind.Highest(Lag(self.data.high), period=self.day_period)
        self.dlaglow = bt.ind.Lowest(Lag(self.data.low), period=self.day_period)
        self.delta = self.dlaghigh - self.dlaglow

    def next(self):
        if (
            self.dlaghigh[0] - self.delta[0] * 0.1
            <= self.data.close[0]
            < self.dlaghigh[0] * 0.999
        ):
            return True
        return False


class PriceUpBreakEntryCond(EntryCond):
    """
    价格上涨满足门槛值和突破值关系
    """

    alias = ("PriceUpBreakEntryCond",)

    def __init__(self):
        # day high
        self.dlaghigh = bt.ind.Highest(Lag(self.data.high), period=self.day_period)
        # 振幅
        self.amp = AmplitudeRate(period=self.day_period)
        # 门槛值
        self.entry_value = None
        # 突破值
        self.break_value = None
        # 尝试次数
        self.try_count = 0

    def next(self):
        if self.entry_value == None:
            if self.p.simulate or self._owner.data.is_live_status():
                self.entry_value = self.dlaghigh[0]
                self.break_value = self.entry_value * (1 + 0.1 * self.amp[0] / 100)
                self.update_extra_args(
                    origin_entry_value=self.entry_value,
                    origin_break_value=self.break_value,
                )
        if self.entry_value * 0.999 <= self.data.close[0] < self.break_value * 0.999:
            if self.try_count < 2:
                self.try_count += 1
                self.update_extra_args(
                    first_entry=True,
                    try_count=self.try_count,
                    entry_value=self.entry_value,
                    break_value=self.break_value,
                )
                return True
        if self.data.close[0] >= self.break_value * 0.999:
            self.entry_value = self.break_value
            self.break_value = self.entry_value * (1 + 0.1 * self.amp[0] / 100)
            self.try_count = 1
            self.update_extra_args(
                first_entry=False,
                try_count=self.try_count,
                entry_value=self.entry_value,
                break_value=self.break_value,
            )
            return True
        return False


class Near30DayHighEntryCond(LiveEntryCond):
    """
    价格接近30日新高(包含当日)
    """

    alias = ("Near30DayHighEntryCond",)

    def __init__(self):
        self.mhigh = Bn30DayHighPlatInd()
        self.dlaghigh = bt.ind.Highest(Lag(self.data.high), period=self.day_period)
        self.dlaglow = bt.ind.Lowest(Lag(self.data.low), period=self.day_period)
        self.delta = self.dlaghigh - self.dlaglow

    def next(self):
        if (
            max(self.mhigh[0], self.dlaghigh[0]) - self.delta[0] * 0.1
            <= self.data.close[0]
        ):
            return True
        return False


class Near10DayHighEntryCond(LiveEntryCond):
    """
    价格接近10日新高(包含当日)
    """

    alias = ("Near10DayHighEntryCond",)

    params = (("delta_ratio", 0.15),)

    def __init__(self):
        self.d10high = Bn10DayHighPlatInd()
        self.dlaghigh = bt.ind.Highest(Lag(self.data.high), period=self.day_period)
        self.dlaglow = bt.ind.Lowest(Lag(self.data.low), period=self.day_period)
        self.delta = self.dlaghigh - self.dlaglow

    def next(self):
        if (
            max(self.d10high[0], self.dlaghigh[0]) - self.delta[0] * self.p.delta_ratio
            <= self.data.close[0]
        ):
            return True
        return False


class NewHighCountEntryCond(LiveEntryCond):
    """
    24h价格突破新高次数
    """

    alias = ("NewHighCountEntryCond",)

    params = (("brk_count", 2),)

    def __init__(self):
        self.count = Bn24HNewHighCountPlatInd()

    def next(self):
        if self.count[0] >= self.p.brk_count:
            return True
        return False


class MAAscendingOrderEntryCond(EntryCond):
    """
    均线多头排列
    """

    alias = ("MAAscendingOrderEntryCond",)

    params = (("ma_periods", (3, 5, 7)),)

    def __init__(self):
        self.mas = [
            bt.indicators.SMA(self.data, period=period) for period in self.p.ma_periods
        ]
        self.ma_order = MultiDatasOrder(*self.mas)

    def next(self):
        return self.ma_order[0]


class ADXRIncreaseEntryCond(EntryCond):
    """
    adxr连续上涨且大于阈值
    """

    alias = ("ADXRIncreaseEntryCond",)

    params = (
        ("dmi_period", 7),
        ("thres", 30),
    )

    def __init__(self):
        self.adxr = AverageDirectionalMovementIndexRatingSafe(period=self.p.dmi_period)
        self.adxrgt = GtValue(self.adxr, num=self.p.thres)
        self.adxr_inc = ADXRIncrease(dmi_period=self.p.dmi_period)

    def next(self):
        return self.adxr_inc[0] and self.adxrgt[0]


class ADXPositiveEntryCond(EntryCond):
    """
    adxr上涨信号
    """

    alias = ("ADXPositiveEntryCond",)

    params = (("dmi_period", 7),)

    def __init__(self):
        self.adxr_inc = ADXPositive(dmi_period=self.p.dmi_period)

    def next(self):
        return self.adxr_inc[0]


class GtCmpPriceUpEntryCond(EntryCond):
    """
    涨幅大于对比品种
    """

    alias = ("GtCmpPriceUpEntryCond",)

    def __init__(self):
        self.priceup = Lag(PriceChangeRate(self.datas[0]))
        self.priceup_cmp = Lag(PriceChangeRate(self.datas[1]))
        self.priceup_cmp_gt = GtValue(self.priceup_cmp, num=0)

        self.gt = Gt(self.priceup, self.priceup_cmp)

    def next(self):
        return self.gt[0] and self.priceup_cmp_gt[0]


class LastOrderPriceUpEntryCond(EntryCond):
    """
    根据上一个订单价格, 达到一定涨幅追单
    """

    alias = ("LastOrderPriceUpEntryCond",)

    params = (("price_ratio", 0.1), ("value_ratio", 0.5))

    def __init__(self):
        self.last_order = None
        pass

    def next(self):
        if not self.broker.get_open_price():
            # 没有开仓时, 条件成立, 但不进行开仓
            self.last_order = None
            return True
        # 获取上一个成交订单
        order = self.broker.get_latest_executed_order()
        if order.is_buy() and self.data.close[0] > order.executed.price * (
            1 + self.p.price_ratio
        ):
            self.last_order = order
            return True
        self.last_order = None
        return False

    def submit_open_order(self):
        if self.last_order is not None:
            self.logger.info("上次成交订单: %s", self.last_order)
            value = self.broker.get_cashbalance() * self.p.value_ratio
            new_order = self._owner.buy(
                side="long",
                exectype="Limit",
                parValue=value,
                priceMatch="OPPONENT_5",
                valid=bt.num2date(self.data.get_curr_dt())
                + datetime.timedelta(seconds=2),
                timeInForce="GTC",
            )
            self.logger.info("追加下单: %s", new_order)
            self.last_order == None
            return True
        else:
            return False


class TurnoverVolOpenintEntryCond(EntryCond):
    """
    成交额, 交易量, 持仓量满足一定关系进入
    """

    alias = ("TurnoverVolOpenintEntryCond",)

    params = (
        ("turnover_thres", 1500000),
        ("vol_oi_ratio_thres", 0.1),
    )

    def __init__(self):
        self.turnover = self.data.close * self.data.volume
        self.vol = self.data.volume
        self.openint = self.data.openint
        self.vol_openint_ratio = self.vol / self.openint

    def next(self):
        return (
            self.turnover[0] > self.p.turnover_thres
            and self.vol_openint_ratio[0] > self.p.vol_oi_ratio_thres
        )


class VolumeSoftEntryCond(EntryCond):
    """
    成交额, 交易量, 温和的情况下关系进入
    """

    alias = ("VolumeSoftEntryCond",)

    params = (
        ("rsi_period", 14),
        ("turnover_thres", 1000000),  # 15分钟百万的交易，对标日交易额在1亿水平
        ("volume_high_thres", 60),
        ("volume_low_thres", 45),
    )

    def __init__(self):
        self.turnover = self.data.close * self.data.volume
        self.avg_turnover = bt.indicators.SMA(self.turnover, period=self.p.rsi_period)
        self.rsi = bt.indicators.RSI_Safe(self.data.volume, period=self.p.rsi_period)

    def next(self):
        return (
            self.p.turnover_thres * 5 >= self.avg_turnover[0] > self.p.turnover_thres
            and self.p.volume_low_thres <= self.rsi[0] <= self.p.volume_high_thres
        )


class IndexMANotDscOrderEntryCond(EntryCond):
    """
    指数均线非空头排列
    """

    alias = ("IndexMANotDscOrderEntryCond",)

    params = (("ma_periods", (1, 10, 20)),)

    def __init__(self):
        self.mas = [
            bt.indicators.SMA(self.data, period=period) for period in self.p.ma_periods
        ]
        self.ma_order = MultiDatasOrder(*self.mas, asc=True)

    def next(self):
        return not self.ma_order[0]


class IndexMADscOrderEntryCond(EntryCond):
    """
    指数均线空头排列
    """

    alias = ("IndexMADscOrderEntryCond",)

    params = (("ma_periods", (20, 40, 120)),)

    def __init__(self):
        self.mas = [
            bt.indicators.SMA(self.data, period=period) for period in self.p.ma_periods
        ]
        self.ma_order = MultiDatasOrder(*self.mas, asc=True)

    def next(self):
        return self.ma_order[0]


class MACrossEntryCond(EntryCond):
    """
    均线多头,并且近期呈发散排列
    """

    alias = ("MACrossEntryCond",)

    params = (("ma_periods", (3, 5, 7)),)

    def __init__(self):
        self.mas = [
            bt.indicators.SMA(self.data, period=period) for period in self.p.ma_periods
        ]
        self.ma_order = MultiDatasOrder(*self.mas)

    def next(self):
        if len(self.mas) != 3:
            return False
        ma5 = self.mas[0]
        ma20 = self.mas[1]
        ma60 = self.mas[2]
        return self.ma_order[0] and \
            ma5[-1] < ma20[-1] and ma20[-1] > ma60[-1] and \
            ma5[-2] < ma20[-2] and ma20[-2] > ma60[-2] \
            and ma5[-3] < ma20[-3] and ma20[-3] > ma60[-3] \
            and ma5[-4] < ma20[-4] and ma20[-4] > ma60[-4] \
            and ma5[-4] < ma5[-3] < ma5[-2] < ma5[-1] < ma5[0]