from backtrader.resamplerfilter import _BaseResampler


class Replayer2(_BaseResampler):
    """
    根据replayer更改逻辑
    原有replayer重复传递同一个tick时, 会生成一个新的datetime,且会增加0.00001
    该replayer接到同一个ts的tick时, 会先把原有的那个tick删去, 重复刷新同一个ts的tick
    """

    params = (
        ("bar2edge", True),
        ("adjbartime", False),
        ("rightedge", True),
    )

    replaying = True

    def _sametsdata(self, data):
        # 检查是否为同一时刻
        # new data at position 0, still untouched from stream
        if not self.subdays:
            return False

        # Time already delivered
        return len(data) > 1 and data.datetime[0] == data.datetime[-1]

    def __call__(self, data, fromcheck=False, forcedata=None):

        samets = False  # 同一个ts

        if not fromcheck:
            # 检查最新的ts是否大于上一个ts
            if self._latedata(data):
                if not self.p.takelate:
                    data.backwards(force=True)
                    return True

            # 检查是否为同一ts更新数据源
            if self._sametsdata(data):
                samets = True

            # 检查是否到达边界
            onedge, docheckover = self._dataonedge(data)  # for subdays
            data._tick_fill(force=True)  # update

            cond = onedge
            # 再根据bar检查一次是否到达边界
            if not cond:  # original is or, if true it would suffice
                if docheckover:
                    cond = self._checkbarover(data, fromcheck=fromcheck)

            # 如果为同一ts更新, 则不算到达边界
            if samets:
                cond = False

            # 确认已经到达边界
            if cond:
                # 该条件未处理
                if not onedge and self.doadjusttime:  # insert tick with adjtime
                    raise Exception(
                        f"replayer 暂未处理该情况: {onedge}, {self.doadjusttime}"
                    )
                # 初始化bar
                self.bar.bstart()
                # 利用当前数据源更新bar
                self.bar.bupdate(data)
                # 判断更新的bar是否为第一个bar
                if self._firstbar:
                    self._firstbar = False

            # 边界之外的情况
            else:
                # 根据目前的数据更新bar
                self.bar.bupdate(data)
                # 时间取上一个bar的时间
                self.bar.datetime = data.datetime[-1]
                # 当数据源不是第一个bar时回退并更新, 否则data当前会缺少数据导致更新失败
                if not self._firstbar:
                    # 数据源回退一个bar, 因为该bar是属于first bar的更新值, 不应当出现在时间轴中
                    data.backwards(force=True)
                    # 用累计bar更新当前数据源
                    data._updatebar(self.bar.lvalues(), forward=False, ago=0)

        else:
            # 当fromcheck为True时, 直接返回False
            return False

        return False
