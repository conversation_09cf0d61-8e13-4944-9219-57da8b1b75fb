def strategy_replace_init(strategy, new_init):
    """
    修改策略, 替换原有init方法
    Args:
        strategy (bt2.StorageStrategy): 策略类型
        add_init (function): 新增逻辑函数
    """
    return type("Psudo_" + strategy.__name__, (strategy,), {"__init__": new_init})


import datetime


def calc_last_time(pin=""):
    """计算此处时间到上一次标记时间的花费时间"""
    last_dt = None

    def calc_spend():
        now_dt = datetime.datetime.now()
        if last_dt:
            delta = (now_dt - last_dt).totalseconds()
