import backtrader as bt
from datetime import datetime
from dateutil.relativedelta import relativedelta
import dateutil

FREQ_MS_DICT = {
    "tick": 500,
    "1m": 500 * 2 * 60,
    "5m": 500 * 2 * 60 * 5,
    "15m": 500 * 2 * 60 * 15,
    "1h": 500 * 2 * 60 * 60,
    "4h": 500 * 2 * 60 * 60 * 4,
    "1d": 500 * 2 * 60 * 60 * 24,
    "1W": 500 * 2 * 60 * 60 * 24 * 7,
}


def num2ts(x, tz=None, naive=True):
    """backtrader中的num转换为时间戳(ms)"""
    return int(round(bt.num2date(x, tz, naive).timestamp() * 1000, 0))


def num2date(x, tz=None, naive=True):
    """num转换为datetime"""
    return ts2date(num2ts(x, tz, naive))


def ts2num(x, tz=None):
    """时间戳(ms)转换为backtrader中的num"""
    return bt.date2num(ts2date(x), tz)


def ts2date(x):
    """时间戳(ms)转换为datetime"""
    return datetime.fromtimestamp(int(x) / 1000)


def freq2timeframe(freq: str) -> dict:
    """频率转化为时间框架参数"""
    FREQ_DICT = {
        "tick": (bt.TimeFrame.Ticks, 1),
        "1m": (bt.TimeFrame.Minutes, 1),
        "5m": (bt.TimeFrame.Minutes, 5),
        "15m": (bt.TimeFrame.Minutes, 15),
        "1h": (bt.TimeFrame.Minutes, 60),
        "4h": (bt.TimeFrame.Minutes, 240),
        "1d": (bt.TimeFrame.Days, 1),
        "1W": (bt.TimeFrame.Weeks, 1),
        "1M": (bt.TimeFrame.Months, 1),
        "1Y": (bt.TimeFrame.Years, 1),
    }
    if freq not in FREQ_DICT:
        raise ValueError(f"频率参数 {freq} 设置错误, 应当为 {list(FREQ_DICT.keys())}")
    return dict(zip(("timeframe", "compression"), FREQ_DICT[freq]))


def str2date(x: str) -> datetime.date:
    return dateutil.parser.parse(x)


def str2ts(x: str) -> int:
    return int(str2date(x).timestamp() * 1000)


def str2num(x: str) -> float:
    return bt.date2num(str2date(x))


def freq2delta(freq: str) -> int:
    """频率转化为间隔毫秒数"""
    return FREQ_MS_DICT.get(freq, None)


def freq2getstartts(freq: str):
    """不同频率对应的ms转化开始ms的方法"""
    tolerance = 0.1  # 容忍时间

    def base_floor(x, base, start=0):
        return (start + x + tolerance) // base * base

    def month_start(x):
        dt = datetime.fromtimestamp(x / 1000)
        return (
            dt + relativedelta(day=1, hour=8, minute=0, second=0, microsecond=0)
        ).timestamp() * 1000

    def year_start(x):
        dt = datetime.fromtimestamp(x / 1000)
        return (
            dt
            + relativedelta(month=1, day=1, hour=8, minute=0, second=0, microsecond=0)
        ).timestamp() * 1000

    FREQ_METHOD = {
        "tick": lambda x: base_floor(x, 500),
        "1m": lambda x: base_floor(x, 1000 * 60),
        "5m": lambda x: base_floor(x, 1000 * 60 * 5),
        "15m": lambda x: base_floor(x, 1000 * 60 * 15),
        "1h": lambda x: base_floor(x, 1000 * 60 * 60),
        "4h": lambda x: base_floor(x, 1000 * 60 * 240),
        "1d": lambda x: base_floor(x, 1000 * 60 * 60 * 24),
        "1W": lambda x: base_floor(x, 1000 * 60 * 60 * 24 * 7)
        - 1000 * 60 * 60 * 24 * 3,
        "1M": month_start,
        "1Y": year_start,
    }

    return FREQ_METHOD[freq]


def freq2getendts(freq: str):
    """不同频率对应的ms转化结束ms的方法"""
    tolerance = 0.1  # 容忍时间

    def base_ceil(x, base, start=0):
        return ((start + x + tolerance) // base + 1) * base

    def month_end(x):
        dt = datetime.fromtimestamp(x / 1000)
        return (
            dt + relativedelta(day=1, hour=8, minute=0, second=0, microsecond=0)
        ).timestamp() * 1000 - 1

    def year_end(x):
        dt = datetime.fromtimestamp(x / 1000)
        return (
            dt
            + relativedelta(month=1, day=1, hour=8, minute=0, second=0, microsecond=0)
        ).timestamp() * 1000 - 1

    FREQ_METHOD = {
        "tick": lambda x: base_ceil(x, 500) - 1,
        "1m": lambda x: base_ceil(x, 1000 * 60) - 1,
        "5m": lambda x: base_ceil(x, 1000 * 60 * 5) - 1,
        "15m": lambda x: base_ceil(x, 1000 * 60 * 15) - 1,
        "1h": lambda x: base_ceil(x, 1000 * 60 * 60) - 1,
        "4h": lambda x: base_ceil(x, 1000 * 60 * 240) - 1,
        "1d": lambda x: base_ceil(x, 1000 * 60 * 60 * 24) - 1,
        "1W": lambda x: base_ceil(x, 1000 * 60 * 60 * 24 * 7)
        - 1000 * 60 * 60 * 24 * 3
        - 1,
        "1M": month_end,
        "1Y": year_end,
    }

    return FREQ_METHOD[freq]


def freq2dayperiod(freq: str):
    """
    频率转换为日线周期
    """
    FREQ_PERIOD = {
        "1m": 60 * 24,
        "5m": 12 * 24,
        "15m": 4 * 24,
        "60m": 24,
        "1h": 24,
        "4h": 6,
        "1d": 1,
    }

    return FREQ_PERIOD[freq]


def freq_period_ratio(freq1, freq2):
    """
    频率周期的比值
    """
    return freq2dayperiod(freq2) / freq2dayperiod(freq1)
