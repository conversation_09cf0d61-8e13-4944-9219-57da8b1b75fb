# 自定义indicator类型
from backtrader import Indicator

# Indicator新增方法


def get_plotinfo(self):
    """返回规定的做图字段"""
    res = self.plotinfo._getpairs()
    return res


@classmethod
def get_name(cls):
    """获取名称"""
    if cls.alias:
        if isinstance(cls.alias, str):
            return cls.alias
        return cls.alias[0]
    return cls.__name__


# 修改原有Indicator类


Indicator.get_plotinfo = get_plotinfo

Indicator.get_name = get_name


class Indicator2(Indicator):
    """
    自定义指标类型
    修改内容:
    plot参数修改
    """

    plotinfo = dict(
        b_plotname="",  # 指标做图名称, 指定名称的才会保存
        b_plot=False,  # 是否展示图
        b_subplot=True,  # 是否绘制在附图中
        b_plotabove=False,  # 是否绘制在主图上方
    )
