## 持仓类型

from backtrader import Position


class HedgePosition:
    """
    双向持仓类型
    Args:
        long: 多头持仓信息
        short: 空头持仓信息
    """

    def __init__(self, long=None, short=None):
        self.long = Position() if not long else long
        self.short = Position() if not short else short
        self.updt = None

    def set(self, size, price, side=None):
        if side == "long":
            return self.set_long(size, price)
        if side == "short":
            return self.set_short(size, price)

    def update(self, size, price, side):
        if side == "long":
            return self.update_long(size, price)
        if side == "short":
            return self.update_short(size, price)
        raise TypeError(f"param side must be long or short, not {side}")

    def set_long(self, size, price):
        return self.long.set(size, price)

    def set_short(self, size, price):
        return self.short.set(size, price)

    def clone(self):
        return HedgePosition(long=self.long.clone, short=self.short.clone)

    def update_long(self, size, price):
        return self.long.update(size, price)

    def update_short(self, size, price):
        return self.short.update(size, price)
