### 成交量相关指标


from ..indicator import Indicator2


class SimpleTurnover(Indicator2):
    """简单成交额计算, 由收盘价*成交量计算得到
    Formula:
        turnover = close * volume
    Returns:
        turnover (float)
    """

    lines = ("turnover",)

    def __init__(self):
        self.lines.turnover = self.data.close * self.data.volume


class LinearBarVolume(Indicator2):
    """
    线性预测当前bar的交易量
    """

    lines = ("volume",)

    def next(self):
        curr_dt = self.data.get_curr_dt()
        start_dt = self.data.datetime[0]
        end_dt = self.data.closedt[0]
        if curr_dt and start_dt and end_dt:
            self.lines.volume[0] = round(
                self.data.volume[0] * (end_dt - start_dt) / (curr_dt - start_dt), 0
            )
        else:
            self.lines.volume[0] = 0
