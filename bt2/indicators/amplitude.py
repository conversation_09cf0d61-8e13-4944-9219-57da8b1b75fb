## 振幅指标

from ..indicator import Indicator2
import backtrader as bt
from .operator import *


class AmplitudeRate(Indicator2):
    """
    过去一段时间的振幅比率
    Formula:
        过去一段时间的最高价 - 过去一段时间的最低价 / 过去一段时间的开盘价 * 100
    Args:
        period (int), 过去一段时间窗口
    Returns:
        amplitude (float)
    """

    lines = ("amplitude",)
    params = (("period", 1440),)  # 可配置的时间窗口

    def __init__(self):
        # 过去一段时间的最高价和最低价
        self.highest = bt.ind.Highest(self.data.high, period=self.p.period)
        self.lowest = bt.ind.Lowest(self.data.low, period=self.p.period)
        # 过去一段时间的开盘价
        self.opening_price = self.data.open(-self.p.period + 1)

        # 振幅比率
        self.lines.amplitude = (self.highest - self.lowest) / self.opening_price * 100


class AMP(AmplitudeRate):
    """
    当前bar的振幅比率
    """

    params = (("period", 1),)


class AmplitudeRateLag1(Indicator2):
    """
    上一个bar的振幅比率
    Formula:
        上一个bar的最高价 - 上一个bar的最低价 / 上一个bar的开盘价 * 100
    Returns:
        amplitude (float)
    """

    lines = ("amplitude",)

    def __init__(self):
        # 上一个bar的最高价和最低价
        self.highest = self.data.high(-1)
        self.lowest = self.data.low(-1)
        # 上一个bar的开盘价
        self.opening_price = self.data.open(-1)
        # 振幅比率
        self.lines.amplitude = (self.highest - self.lowest) / self.opening_price * 100


class PriceUpRate(Indicator2):
    """
    过去一段时间的涨幅比率
    Formula:
        (过去一段时间的最高价 - 过去一段时间的开盘价) / 过去一段时间的开盘价 * 100
    Args:
        period (int), 过去一段时间窗口
    Returns:
        rate (float)
    """

    lines = ("rate",)
    params = (("period", 1),)  # 可配置的时间窗口

    def __init__(self):
        # 过去一段时间的最高价
        self.highest = bt.ind.Highest(self.data.high, period=self.p.period)
        # 过去一段时间的开盘价
        self.opening_price = self.data.open(-self.p.period + 1)
        # 涨幅比率
        self.lines.rate = (self.highest - self.opening_price) / self.opening_price * 100


class PriceUpLag1(Indicator2):
    """
    上一个bar的涨幅比率
    Formula:
        (上一个bar的最高价 - 上一个bar的开盘价) / 上一个bar的开盘价 * 100
    Args:
        period (int), 过去一段时间窗口
    Returns:
        rate (float)
    """

    lines = ("rate",)

    def __init__(self):
        # 涨幅比率
        self.lines.rate = (
            (self.data.high(-1) - self.data.open(-1)) / self.data.open(-1) * 100
        )


class PriceChangeRate(Indicator2):
    """
    价格变动比率
    Formula:
        (收盘价 - 开盘价) / 开盘价 * 100
    Args:
        period (int), 过去一段时间窗口
    Returns:
        rate (float)
    """

    lines = ("rate",)

    params = (("period", 1),)

    def __init__(self):
        # 过去一段时间的开盘价
        self.opening_price = self.data.open(-self.p.period + 1)
        self.lines.rate = (
            (self.data.close - self.opening_price) / self.opening_price * 100
        )


class DataChangeInRange(Indicator2):
    """
    数据源在某段时间的变动率
    data0: 时间区间
    data1: 数据源
    """

    lines = ("rate",)

    def __init__(self):
        data = self.datas[1]
        _range = self.datas[0]
        self.start_value = Step(Filter(Switch(_range), data.open))
        self.curr_value = Filter(_range, data.close)
        self.lines.rate = (self.curr_value - self.start_value) / self.start_value * 100


class DataAmpInRange(Indicator2):
    """
    数据源在某段时间的振幅
    data0: 时间区间
    data1: 数据源
    """

    lines = ("rate",)

    def __init__(self):
        data = self.datas[1]
        _range = self.datas[0]
        self.start_value = Step(Filter(Switch(_range), data.open))
        self.high_value = FilterHigh(_range, data.high)
        self.low_value = FilterLow(_range, data.low)
        self.lines.rate = (self.high_value - self.low_value) / self.start_value * 100


class ValueChangeInRange(Indicator2):
    """
    数据在某段时间的变动率
    data0: 时间区间
    data1: 数据
    """

    lines = ("rate",)

    def __init__(self):
        data = self.datas[1]
        _range = self.datas[0]
        self.start_value = Step(Filter(Switch(_range), data))
        self.curr_value = Filter(_range, data)
        self.lines.rate = (self.curr_value - self.start_value) / self.start_value * 100


class ValueAmpInRange(Indicator2):
    """
    数据在某段时间的振幅
    data0: 时间区间
    data1: 数据
    """

    lines = ("rate",)

    def __init__(self):
        data = self.datas[1]
        _range = self.datas[0]
        self.start_value = Step(Filter(Switch(_range), data))
        self.high_value = FilterHigh(_range, data)
        self.low_value = FilterLow(_range, data)
        self.lines.rate = (self.high_value - self.low_value) / self.start_value * 100
