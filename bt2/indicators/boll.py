## 布林指标

from ..indicator import Indicator2
import backtrader as bt


class BollingerBandsWidthRate(Indicator2):
    """
    布林带宽度百分比
    width (%) = (top - bot) / mid * 100
    """

    alias = ("BBandsWidth",)

    lines = (
        "width",
        "mid",
        "top",
        "bot",
    )

    params = (
        ("period", 20),
        ("devfactor", 2.0),
        ("movav", bt.indicators.MovAv.Simple),
    )

    def __init__(self):
        self.bolling = bt.indicators.BollingerBands(
            period=self.p.period,
            devfactor=self.p.devfactor,
            movav=self.p.movav,
        )
        self.lines.mid = self.bolling.mid
        self.lines.top = self.bolling.top
        self.lines.bot = self.bolling.bot
        self.lines.width = (
            (self.bolling.top - self.bolling.bot) / self.bolling.mid * 100
        )
