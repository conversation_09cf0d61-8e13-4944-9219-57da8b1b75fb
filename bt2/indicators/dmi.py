# DMI相关指标

from ..indicator import Indicator2
from .operator import *
from backtrader.indicators.directionalmove import _DirectionalIndicator


class AverageDirectionalMovementIndexSafe(_DirectionalIndicator):
    """
    针对原版ADX进行修改, 除数增加极小值避免除0异常
    """

    alias = ("ADX",)

    lines = ("adx",)

    plotlines = dict(adx=dict(_name="ADX"))

    def __init__(self):
        super(AverageDirectionalMovementIndexSafe, self).__init__()

        dx = abs(self.DIplus - self.DIminus) / (self.DIplus + self.DIminus + 1e-10)
        self.lines.adx = 100.0 * self.p.movav(dx, period=self.p.period)


class DirectionalIndicatorSafe(AverageDirectionalMovementIndexSafe):
    alias = ("DI",)
    lines = (
        "plusDI",
        "minusDI",
    )

    def __init__(self):
        super(DirectionalIndicatorSafe, self).__init__()

        self.lines.plusDI = self.DIplus
        self.lines.minusDI = self.DIminus


class AverageDirectionalMovementIndexRatingSafe(AverageDirectionalMovementIndexSafe):
    alias = ("ADXR",)

    lines = ("adxr",)
    plotlines = dict(adxr=dict(_name="ADXR"))

    def __init__(self):
        super(AverageDirectionalMovementIndexRatingSafe, self).__init__()

        self.lines.adxr = (self.l.adx + self.l.adx(-self.p.period)) / 2.0


class ADXRIncrease(Indicator2):
    """
    ADXR递增
    """

    lines = ("value",)

    params = (
        ("dmi_period", 5),
        ("increase_period", 3),
    )

    plotinfo = dict(
        plot=True,  # 是否展示图
    )

    def __init__(self):
        self.adxr = AverageDirectionalMovementIndexRatingSafe(period=self.p.dmi_period)
        self.lines.value = Increase(self.adxr, period=self.p.increase_period)


class ADXRDecrease(Indicator2):
    """
    ADXR递减
    """

    lines = ("value",)

    params = (
        ("dmi_period", 5),
        ("increase_period", 3),
    )

    plotinfo = dict(
        plot=True,  # 是否展示图
    )

    def __init__(self):
        self.adxr = AverageDirectionalMovementIndexRatingSafe(period=self.p.dmi_period)
        self.lines.value = Decrease(self.adxr, period=self.p.increase_period)


class ADXPositive(Indicator2):
    """
    DX+ > DX-
    """

    lines = ("value",)

    params = (("dmi_period", 5),)

    plotinfo = dict(
        plot=True,  # 是否展示图
    )

    def __init__(self):
        self.adx = DirectionalIndicatorSafe(period=self.p.dmi_period)
        self.lines.value = self.adx.plusDI > self.adx.minusDI


class ADXNegative(Indicator2):
    """
    DX+ < DX-
    """

    lines = ("value",)

    params = (("dmi_period", 5),)

    plotinfo = dict(
        plot=True,  # 是否展示图
    )

    def __init__(self):
        self.adx = DirectionalIndicatorSafe(period=self.p.dmi_period)
        self.lines.value = self.adx.plusDI < self.adx.minusDI
