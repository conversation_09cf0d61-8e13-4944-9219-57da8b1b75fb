from ..indicator import Indicator2
import backtrader as bt


class BarStart(Indicator2):
    """
    当前bar为新bar
    """

    lines = ("value",)

    def __init__(self):
        self._old_dt = 0

    def next(self):
        # 历史数据永远为新的bar
        if not self.data.is_live_status():
            self.lines.value[0] = True
            return

        if self.data.datetime[0] != self._old_dt:
            self.lines.value[0] = True
            self._old_dt = self.data.datetime[0]
        else:
            self.lines.value[0] = False


class ConcatBar(Indicator2):
    """
    将过去N个bar合成一个bar
    """

    lines = ("close", "open", "high", "low", "volume")

    params = (("period", 2),)

    def __init__(self):
        self.addminperiod(self.p.period)
        self.lines.close = self.data.close
        self.lines.open = self.data.open(-self.p.period + 1)
        self.lines.high = bt.indicators.Highest(self.data.high, period=self.p.period)
        self.lines.low = bt.indicators.Lowest(self.data.low, period=self.p.period)
        self.lines.volume = bt.indicators.SumN(self.data.volume, period=self.p.period)


class BarOpened(Indicator2):
    """
    当前bar开过仓
    """

    lines = ("value",)

    def __init__(self):
        self.newbar = BarStart()
        self._old_abs_position = 0
        self._once = False

    def next(self):
        if self.newbar[0]:
            self._once = False
        if self._owner.broker.get_abs_size() > self._old_abs_position:
            self._once = True
        self._old_abs_position = self._owner.broker.get_abs_size()
        self.lines.value[0] = self._once
