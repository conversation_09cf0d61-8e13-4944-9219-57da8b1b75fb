### 横盘指标

from ..indicator import Indicator2
from .boll import BollingerBandsWidthRate
from .operator import *
from .amplitude import *


class BollingerUpwardTension(Indicator2):
    """
    布林线表示的横盘上涨蓄势指标
    Args:
        boll_period (int): 布林周期
        boll_devfactor (float): 标准差系数
        boll_width_thres (float): 布林带宽度阈值
        break_thres (int): 尝试突破次数阈值
    """

    lines = ("value",)

    params = (
        ("boll_period", 60),
        ("boll_devfactor", 2),
        ("boll_width_thres", 3),
        ("break_thres", 3),
    )

    def __init__(self):
        # 布林线宽度
        self.boll = BollingerBandsWidthRate(
            period=self.p.boll_period, devfactor=self.p.boll_devfactor
        )
        # 布林带收窄
        self.narrow_boll = LteValue(self.boll, num=self.p.boll_width_thres)
        # 横盘布林带
        self.amp = AmplitudeRate(period=20)
        self.amplt = LtValue(self.amp, num=2)
        self.Consolidation = And(
            self.narrow_boll,
        )
        # 突破点
        self.break_up = Switch(Gt(self.data, self.boll.top))
        # 横盘突破点累积
        self.break_up_time = FilterAdd(self.narrow_boll, self.break_up)
        # 累积次数超过阈值
        self.lines.value = GteValue(self.break_up_time, num=self.p.break_thres)
