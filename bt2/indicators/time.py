from ..indicator import Indicator2
from ..utils import num2ts
from backtrader import num2date


class IsMinuteMultiple(Indicator2):
    """
    检查当前时间的分钟数是否为某个值的整数倍
    """

    lines = ("value",)
    params = (("multiple", 15),)

    def next(self):
        dt = num2date(self.data.datetime[0])
        m = dt.minute
        s = dt.second
        self.lines.value[0] = 1 if m % self.p.multiple == 0 and s == 0 else 0


class BarOpenMinute(Indicator2):
    """
    当前时间距离当前bar开盘时间分钟数
    """

    lines = ("minute",)

    def next(self):
        curr_dt = num2ts(self.data.get_curr_dt())
        if curr_dt:
            open_dt = num2ts(self.data.datetime[0])
            self.lines.minute[0] = (curr_dt - open_dt) // 1000 // 60


class BarOpenSecond(Indicator2):
    """
    当前时间距离当前bar开盘时间秒数
    """

    lines = ("second",)

    def next(self):
        curr_dt = num2ts(self.data.get_curr_dt())
        if curr_dt:
            open_dt = num2ts(self.data.datetime[0])
            self.lines.second[0] = (curr_dt - open_dt) // 1000


class BarCloseSecond(Indicator2):
    """
    当前时间距离当前bar收盘时间秒数
    """

    lines = ("second",)

    def next(self):
        curr_dt = num2ts(self.data.get_curr_dt())
        if curr_dt:
            close_dt = num2ts(self.data.closedt[0])
            self.lines.second[0] = (close_dt - curr_dt) // 1000
