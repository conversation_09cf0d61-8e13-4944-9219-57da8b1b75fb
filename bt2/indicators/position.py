## 涉及仓位的指标

from ..indicator import Indicator2


class PositionInd(Indicator2):
    """获取当前数据源的仓位
    Returns:
        position (int): 仓位
    """

    lines = ("position",)

    def __init__(self):
        pass

    def next(self):
        self.lines.position[0] = self._owner.getposition(self.data).size


class AvgPriceInd(Indicator2):
    """获取当前数据源的开仓均价
    Returns:
        avgPrice (float): 开仓均价
    """

    lines = ("avgPrice",)

    def __init__(self):
        pass

    def next(self):
        self.lines.avgPrice[0] = self._owner.getposition(self.data).price


class HedgePositionInd(Indicator2):
    """获取当前数据源的仓位, 双向仓位
    Returns:
        position (int): 仓位
    """

    lines = ("long", "short")

    def __init__(self):
        pass

    def next(self):
        self.lines.long[0] = self._owner.getposition(self.data).long.size
        self.lines.short[0] = self._owner.getposition(self.data).short.size


class NoPos(Indicator2):
    """
    没有仓位时返回1
    Returns:
        value (int): 仓位
    """

    lines = ("position",)

    def next(self):
        self.lines.position[0] = self._owner.getposition(self.data).size == 0


class NoHedgePos(Indicator2):
    """
    没有仓位时返回1
    Returns:
        long (int): 多仓
        short(int): 空仓
    """

    lines = (
        "long",
        "short",
    )

    def next(self):
        self.lines.long[0] = self._owner.getposition(self.data).long.size == 0
        self.lines.short[0] = self._owner.getposition(self.data).short.size == 0


class ReversalUpward(PositionInd):
    """
    逆转上升, 反包, 在空仓的情况下当前bar的最高价超过了上一个bar的最高价
    在实盘模拟中, 一旦当前bar触发了反包, 则维持状态知道下一个bar
    Returns:
        position (int): 仓位
        reverse (int), 是否出现反包状态
    """

    lines = ("reverse",)

    plotlines = {"position": {"_plotskip": True}}
    plotinfo = {"plottype": {"position": None}}

    def __init__(self):
        super().__init__()

    def next(self):
        super().next()
        if (
            not self.lines.position[0] or self.lines.reverse[0]
        ):  # 无仓位跳过 或 已经产生反包跳过
            return
        self.lines.reverse[0] = self.data.high[0] > self.data.high[-1]


class StartingCapital(PositionInd):
    """
    开仓前的资金
    当仓位从0变为非0或者反转时,记录上一个bar的资金
    Returns:
        position (int): 仓位
        startingcap (float), 开仓资金
    """

    lines = ("startingcap",)

    plotlines = {"position": {"_plotskip": True}}
    plotinfo = {"plottype": {"position": None}}

    def __init__(self):
        super().__init__()
        self.addminperiod(2)  # 至少保留两个值
        self.broker = self._owner.broker

    def prenext(self):
        self.lines.startingcap[0] = self.broker.getvalue()

    def next(self):
        super().next()
        curr_pos = self.lines.position[0]
        prev_pos = self.lines.position[-1]
        if curr_pos == prev_pos == 0:  # 没有仓位且未发生仓位变动
            # 取当前价值
            self.lines.startingcap[0] = self.broker.getvalue()
        if curr_pos == prev_pos != 0:  # 保持仓位
            # 维持上一个值
            self.lines.startingcap[0] = self.lines.startingcap[-1]
        if curr_pos != 0 and prev_pos == 0:  # 开仓
            # 维持上一个值
            self.lines.startingcap[0] = self.lines.startingcap[-1]
        if curr_pos == 0 and prev_pos != 0:  # 平仓
            # 更新当前价值
            self.lines.startingcap[0] = self.broker.getvalue()
        if curr_pos * prev_pos < 0:  # 仓位反转
            # 维持上一个值
            self.lines.startingcap[0] = self.lines.startingcap[-1]
        if self.lines.startingcap[0] == None:
            raise ValueError("开仓前资金为无效值, 可能是在第二个bar开仓导致")


class CapitalInd(Indicator2):
    """
    当前资金
    Returns:
        capital (float)
    """

    lines = ("capital",)

    def next(self):
        self.lines.capital[0] = self._owner.broker.getvalue()


class UnrealizedPNL(StartingCapital):
    """
    浮盈/亏, 开仓前资金, 当前资金
    Returns:
        unrealizedpnv (float): 浮盈/亏
        startingcap (float): 开仓前资金
        capital (float): 当前资金
        position (int): 仓位
    """

    lines = (
        "unrealizedpnv",
        "capital",
    )

    plotlines = {
        "position": {"_plotskip": True},
        "startingcap": {"_plotskip": True},
        "capital": {"_plotskip": True},
    }
    plotinfo = {"plottype": {"position": None, "startingcap": None, "capital": None}}

    def __init__(self):
        super().__init__()

    def next(self):
        super().next()
        self.lines.capital[0] = self._owner.broker.getvalue()
        self.lines.unrealizedpnv[0] = self.lines.capital[0] - self.lines.startingcap[0]


class PosMaxDrawdown(UnrealizedPNL):
    """
    计算持仓最大回撤
    非持仓时为0
    Returns:
        maxdrawdown (float): 持仓最大回撤
        unrealizedpnv (float): 浮盈/亏
        startingcap (float): 开仓前资金
        capital (float): 当前资金
        position (int): 仓位
    """

    lines = ("maxdrawdown",)

    plotlines = {
        "position": {"_plotskip": True},
        "startingcap": {"_plotskip": True},
        "capital": {"_plotskip": True},
        "unrealizedpnv": {"_plotskip": True},
    }
    plotinfo = {
        "plottype": {
            "position": None,
            "startingcap": None,
            "capital": None,
            "unrealizedpnv": None,
        }
    }

    def __init__(self):
        super().__init__()

    def next(self):
        super().next()
        if self.lines.maxdrawdown[0] != self.lines.maxdrawdown[0]:  # nan值取0
            self.lines.maxdrawdown[0] = 0
        if self.lines.position[0] == 0:  # 当前bar未持仓
            self.lines.maxdrawdown[0] = 0
            return
        if (
            self.lines.position[-1]
            and self.lines.position[0] != self.lines.position[-1]
        ):  # 在上一个bar持仓条件下改变仓位则重新计算
            self.lines.maxdrawdown[0] = 0
        self.lines.maxdrawdown[0] = min(
            self.lines.maxdrawdown[-1], self.lines.unrealizedpnv[0]
        )


class PosMaxProfit(UnrealizedPNL):
    """
    计算持仓最大收益
    非持仓时为0
    Returns:
        maxprofit (float): 持仓最大收益
        unrealizedpnv (float): 浮盈/亏
        startingcap (float): 开仓前资金
        capital (float): 当前资金
        position (int): 仓位
    """

    lines = ("maxprofit",)

    plotlines = {
        "position": {"_plotskip": True},
        "startingcap": {"_plotskip": True},
        "capital": {"_plotskip": True},
        "unrealizedpnv": {"_plotskip": True},
    }

    plotinfo = {
        "plottype": {
            "position": None,
            "startingcap": None,
            "capital": None,
            "unrealizedpnv": None,
        }
    }

    def __init__(self):
        super().__init__()

    def next(self):
        super().next()
        if self.lines.maxprofit[0] != self.lines.maxprofit[0]:  # nan值取0
            self.lines.maxprofit[0] = 0
        if self.lines.position[0] == 0:  # 当前bar未持仓
            self.lines.maxprofit[0] = 0
            return
        if (
            self.lines.position[-1]
            and self.lines.position[0] != self.lines.position[-1]
        ):  # 在上一个bar持仓条件下改变仓位则重新计算
            self.lines.maxprofit[0] = 0
        self.lines.maxprofit[0] = max(
            self.lines.maxprofit[-1], self.lines.unrealizedpnv[0]
        )
