# 价格波动相关指标

from ..indicator import Indicator2
from .operator import Abs
import backtrader as bt

from logging import getLogger

logger = getLogger(__name__)


class NewLow(Indicator2):
    """
    创新低, 当前价格低于过去一段时间的最低价, 则该bar视为创新低
    过去一段时间的最低价不包含当前bar
    Formula:
        newlow = low < Lowest(low(-1), period)
    Args:
        period (int): 过去价格的时间窗口
    Returns:
        newlow (bool)
    """

    lines = ("newlow",)
    params = (("period", 1440),)

    def __init__(self):
        self.low_delay = self.data.low(-1)
        self.lowest = bt.indicators.Lowest(
            self.low_delay, period=self.p.period - 1
        )  # 实际窗口长度为去掉当前bar的长度
        self.lines.newlow = self.data.low < self.lowest


class PriceRange(Indicator2):
    """价差, 一段时间内收盘价和开盘价的差值
    Formula:
        pricerange = close - open
    Args:
        period (int): 时间窗口, 0表示针对当前bar进行计算
    Returns:
        pricerange (float)
    """

    lines = ("pricerange",)
    params = (("period", 0),)

    def __init__(self):
        self.lines.pricerange = self.data.close(0 - self.p.period) - self.data.open


class DatasPriceRange(Indicator2):
    """两个数据源价差"""

    lines = ("pricerange",)

    def __init__(self):
        avg_price = (self.datas[0].close + self.datas[1].close) / 2
        self.lines.pricerange = (self.datas[0].close - self.datas[1].close) / avg_price
        # self.addminperiod(20)

    def next(self):
        super().next()
        # logger.info(f'next ind--- {bt.num2date(self.datas[0].datetime[0])}, {bt.num2date(self.datas[1].datetime[0])}, {self.lines.pricerange[0]}')


class LivePriceCrossEqValue(Indicator2):
    """价格等于或穿越设定值, 用于实时价格"""

    lines = ("value",)

    params = (("price", 0),)

    def __init__(self):
        self._old_value = None

    def next(self):
        if self._old_value == None:
            self._old_value = self.data.close[0]
            self.lines.value[0] = False
            return
        if (self.data.close[0] - self.p.price) * (self.p.price - self._old_value) >= 0:
            self._old_value = self.data.close[0]
            self.lines.value[0] = True
            return
        self._old_value = self.data.close[0]
        self.lines.value[0] = False
        return


class BullishBar(Indicator2):
    """
    阳线
    """

    lines = ("value",)

    def __init__(self):
        self.lines.value = self.data.open < self.data.close


class BearishBar(Indicator2):
    """
    阴线
    """

    lines = ("value",)

    def __init__(self):
        self.lines.value = self.data.open > self.data.close


class BodyLength(Indicator2):
    """
    k线实体大小, open-close
    """

    lines = ("value",)

    def __init__(self):
        self.lines.value = Abs(self.data.open - self.data.close)


class UpperShadowLength(Indicator2):
    """
    k线上影线大小, high-max(open, close)
    """

    lines = ("value",)

    def __init__(self):
        self.lines.value = self.data.high - bt.Max(self.data.open, self.data.close)


class LowerShadowLength(Indicator2):
    """
    k线下影线大小, low(open, close) - low
    """

    lines = ("value",)

    def __init__(self):
        self.lines.value = bt.Min(self.data.open, self.data.close) - self.data.low
