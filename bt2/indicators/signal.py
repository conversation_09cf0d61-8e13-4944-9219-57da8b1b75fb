# 信号类型

from ..indicator import Indicator2
from ..redismanager import PlatIndRedisManager
from logging import getLogger

logger = getLogger(__name__)


class PlatRedisIndicator(Indicator2):
    """
    通过读取平台redis, 具有实时性
    """

    lines = ("data",)

    _redis_key = ""  # redis 对应指标的key
    _timeout = 3600  # 更新时间超时设置
    _multi = True  # 是否包含多个品种

    @classmethod
    def get_all(cls):
        return PlatIndRedisManager.get_current_index_all(cls._redis_key, cls._timeout)

    def next(self):
        try:
            # 必须为实时数据源
            if self.data.is_live_status():
                res = self.get_all()
                if self._multi:
                    res = res[self.data.get_symbol().upper()]
                for line, linealias in zip(self.lines, self.getlinealiases()):
                    line[0] = res[linealias]
            else:
                for line, linealias in zip(self.lines, self.getlinealiases()):
                    line[0] = float("nan")
        except Exception as e:
            logger.error(f"{self._redis_key} got wrong")
            logger.exception(e)
            raise e


class BnBrakePlatInd(PlatRedisIndicator):
    _redis_key = "binance_market_brake_1m_5"
    _multi = False


class BnTimeTo30DayLowPlatInd(PlatRedisIndicator):
    _redis_key = "binance_symbol_lpt_1d_30"


class BnTimeTo30DayHighPlatInd(PlatRedisIndicator):
    _redis_key = "binance_symbol_hpt_1d_30"


class BnWeightedRSIPlatInd(PlatRedisIndicator):
    _redis_key = "binance_price_newrsi"


class BnNormalizedATRPlatInd(PlatRedisIndicator):
    _redis_key = "binance_price_natr"


class BnTurnOverPlatInd(PlatRedisIndicator):
    _redis_key = "binance_price_quoteVolume_1m_5"


class BnNHNLPlatInd(PlatRedisIndicator):
    _redis_key = "binance_market_nhnl_1d_30_5m"
    _multi = False


class BnNHNL24hPlatInd(PlatRedisIndicator):
    _redis_key = "binance_market_nhnl_1h_24_5m"
    _multi = False


class BnRSI5mPlatInd(PlatRedisIndicator):
    _redis_key = "binance_symbol_rsi_1m_5"


class BnRSI10mPlatInd(PlatRedisIndicator):
    _redis_key = "binance_symbol_rsi_1m_10"


class BnMACD5mPlatInd(PlatRedisIndicator):
    _redis_key = "binance_symbol_macd_1m_5_10"


class Bn10DayHighPlatInd(PlatRedisIndicator):
    _redis_key = "binance_price_high_1d_10"


class Bn10DayLowPlatInd(PlatRedisIndicator):
    _redis_key = "binance_price_low_1d_10"


class Bn30DayHighPlatInd(PlatRedisIndicator):
    _redis_key = "binance_price_high_1d_30"


class Bn30DayLowPlatInd(PlatRedisIndicator):
    _redis_key = "binance_price_low_1d_30"


class Bn30DayNewHighPlatInd(PlatRedisIndicator):
    _redis_key = "binance_symbol_newOverHigh_1d_30"


class Bn30DayNewLowPlatInd(PlatRedisIndicator):
    _redis_key = "binance_symbol_newOverLow_1d_30"


class Bn24HNewHighCountPlatInd(PlatRedisIndicator):
    _redis_key = "binance_price_nhc_1h_24"


class Bn24HNewLowCountPlatInd(PlatRedisIndicator):
    _redis_key = "binance_price_nlc_1h_24"


class BnHistHighNearPlatInd(PlatRedisIndicator):
    """
    币安历史新高接近程度
    """

    _redis_key = "binance_symbol_his_high_near"
