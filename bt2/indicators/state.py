from ..indicator import Indicator2


class KeepState(Indicator2):
    """
    状态量, 不进行自动计算, 通过策略手动更新
    默认值为上一个有效值
    Args:
        default: 默认值
    """

    params = (("default", float("nan")),)
    lines = ("value",)

    def __init__(self):
        self.addminperiod(2)
        self.keep_value = self.p.default

    def prenext(self):
        self.lines.value[0] = self.p.default

    def next(self):
        self.lines.value[0] = self.keep_value

    def set_value(self, value):
        self.lines.value[0] = self.keep_value = value

    def reset_value(self):
        self.lines.value[0] = self.keep_value = self.p.default

    def max_value(self, value):
        """取自身和给定值中的最大值"""
        value = max(self.keep_value, value) if self.notna() else value
        self.set_value(value)

    def min_value(self, value):
        """取自身和给定值中的最小值"""
        value = min(self.keep_value, value) if self.notna() else value
        self.set_value(value)

    def add(self, value=1):
        res = self.lines.value[-1] + value
        self.set_value(res)

    def isnan(self):
        return self.lines.value[0] != self.lines.value[0]

    def notnan(self):
        return not self.isnan()

    isna = isnan

    notna = notnan


class ResetState(Indicator2):
    """
    状态量, 不进行自动计算, 通过策略手动更新
    当新的bar到来时, 取值设定为默认值
    Args:
        default: 默认值
    """

    params = (("default", False),)
    lines = ("value",)

    def __init__(self):
        self.addminperiod(2)

    def prenext(self):
        self.lines.value[0] = self.p.default

    def next(self):
        if self.lines.value[0] != self.lines.value[0]:  # nan
            self.lines.value[0] = self.p.default

    def set_value(self, value):
        self.lines.value[0] = value

    def reset_value(self):
        self.lines.value[0] = self.p.default
