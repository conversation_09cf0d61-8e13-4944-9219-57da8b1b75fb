## 运算相关指标

from ..indicator import Indicator2
import math
import functools
import operator
import numpy as np
import bisect


class OP(Indicator2):
    """
    函数指标
    """

    lines = ("value",)

    plotlines = dict(
        value=dict(
            b_marker="line",  # 图标形状
            b_size=1.0,  # 图标大小
        )
    )


class BPlot(OP):
    """
    作图指标
    """

    plotinfo = (("b_plot", True),)

    plotlines = dict(value=dict(b_color="auto"))

    def next(self):
        self.lines.value[0] = self.data[0]


class ConstValue(OP):
    """返回设定值
    Args:
        num (float): 设定值
    Returns:
        value (bool)
    """

    params = (("num", 1),)

    def next(self):
        self.lines.value[0] = self.p.num


class EqValue(OP):
    """等于设定值返回1, 否则返回0
    Args:
        num (float): 设定值
    Returns:
        value (bool)
    """

    params = (("num", 0),)

    def next(self):
        if self.data[0] == self.p.num:
            self.lines.value[0] = 1
        else:
            self.lines.value[0] = 0


class GtValue(OP):
    """大于设定值返回1, 否则返回0
    Args:
        num (float): 设定值
    Returns:
        value (bool)
    """

    params = (("num", 0),)

    def __init__(self):
        self.lines.value = self.data > self.p.num

    # def next(self):
    #     self.lines.value[0] = self.data[0] > self.p.num


class LtValue(OP):
    """小于设定值返回1, 否则返回0
    Args:
        num (float): 设定值
    Returns:
        value (bool)
    """

    params = (("num", 0),)

    def next(self):
        self.lines.value[0] = self.data[0] < self.p.num


class GteValue(OP):
    """大于等于设定值返回1, 否则返回0
    Args:
        num (float): 设定值
    Returns:
        value (bool)
    """

    params = (("num", 0),)

    def __init__(self):
        self.lines.value = self.data >= self.p.num


class LteValue(OP):
    """小于等于设定值返回1, 否则返回0
    Args:
        num (float): 设定值
    Returns:
        value (bool)
    """

    params = (("num", 0),)

    def __init__(self):
        self.lines.value = self.data <= self.p.num


class DivInd(OP):
    """指标比值
    Formula:
        value = datas[0] / datas[1] * 100
    Returns:
        value (float)
    """

    def __init__(self):
        self.lines.value = self.datas[0] / self.datas[1] * 100


class Cmp(OP):
    """
    Function:
        value = a op b
        a > b: 1
        a < b: -1
        a == b: 0
    """

    def next(self):
        if self.datas[0][0] > self.datas[1][0]:
            self.lines.value[0] = 1
            return
        if self.datas[0][0] < self.datas[1][0]:
            self.lines.value[0] = -1
            return
        self.lines.value[0] = 0


class CmpValue(OP):
    """
    Function:
        value = a op b
        a > b: 1
        a < b: -1
        a == b: 0
    """

    params = (("num", 0),)

    def next(self):
        if self.data[0] > self.p.num:
            self.lines.value[0] = 1
            return
        if self.data[0] < self.p.num:
            self.lines.value[0] = -1
            return
        self.lines.value[0] = 0


class Eq(OP):
    """
    Function:
        value = a == b
    """

    def __init__(self):
        self.lines.value = self.datas[0] == self.datas[1]


class Gt(OP):
    """
    Function:
        value = a > b
    """

    def __init__(self):
        self.lines.value = self.datas[0] > self.datas[1]


class Lt(OP):
    """
    Function:
        value = a < b
    """

    def __init__(self):
        self.lines.value = self.datas[0] < self.datas[1]


class Gte(OP):
    """
    Function:
        value = a >= b
    """

    def __init__(self):
        self.lines.value = self.datas[0] >= self.datas[1]


class Lte(OP):
    """
    Function:
        value = a <= b
    """

    def __init__(self):
        self.lines.value = self.datas[0] <= self.datas[1]


class Not(OP):
    """
    Function:
        not
    """

    def next(self):
        self.lines.value[0] = not bool(self.data[0])


class Neg(OP):
    """
    Function:
        neg
    """

    def next(self):
        self.lines.value[0] = -self.data[0]


class And(OP):
    """
    Function:
        and
    """

    def next(self):
        self.lines.value[0] = functools.reduce(
            operator.and_, (bool(data[0]) for data in self.datas)
        )


class Or(OP):
    """
    Function:
        or
    """

    def next(self):
        self.lines.value[0] = functools.reduce(
            operator.or_, (bool(data[0]) for data in self.datas)
        )


class Min(OP):
    """
    Function:
        min
    """

    def next(self):
        self.lines.value[0] = min((bool(data[0]) for data in self.datas))


class Max(OP):
    """
    Function:
        max
    """

    def next(self):
        self.lines.value[0] = max((bool(data[0]) for data in self.datas))


class Sign(OP):
    """
    Function:
        sign
    """

    def next(self):
        if self.data[0] > 0:
            self.lines.value[0] = 1
        elif self.data[0] < 0:
            self.lines.value[0] = -1
        else:
            self.lines.value[0] = 0


class SameSign(OP):
    """
    Function:
        same sign
    """

    def next(self):
        self.lines.value[0] = self.datas[0][0] * self.datas[1][0] > 0


class AccumulateInd(OP):
    """一段时间的指标累积值
    使用math.fsum求和

    Formula:
        value = sum(data)
    Args:
        period (int): 时间窗口长度
    Returns:
        value (float)
    """

    params = (("period", 5),)

    def __init__(self):
        self.addminperiod(self.p.period + 1)

    def next(self):
        self.lines.value[0] = math.fsum(self.data.get(size=self.p.period))


Accum = AccumulateInd  # 别名


class MultiDatasOrder(OP):
    """
    多个指标按照输入排序, 正序返回1, 倒序返回-1, 其余返回0
    排序包含相等值为非顺序
    Args:
        asc (bool): True: 判断升序, False: 判断降序, None: 升序为1, 降序为-1
    """

    params = (
        (
            "asc",
            False,
        ),
    )

    def __init__(self):
        if len(self.datas) < 2:
            raise ValueError("输入数量至少为2")

    def next(self):
        values = [d[0] for d in self.datas]
        asc_ordered = all(values[i] < values[i + 1] for i in range(len(values) - 1))
        desc_ordered = all(values[i] > values[i + 1] for i in range(len(values) - 1))
        if self.p.asc == False:
            self.lines.value[0] = bool(desc_ordered)
        elif self.p.asc == True:
            self.lines.value[0] = bool(asc_ordered)
        else:
            if asc_ordered:
                self.lines.value[0] = 1
            elif desc_ordered:
                self.lines.value[0] = -1
            else:
                self.lines.value[0] = 0


class Switch(OP):
    """
    状态转换时返回1
    Args:
        onlytrue (bool): 只判断0->1时
    """

    params = (("onlytrue", True),)

    def __init__(self):
        self.addminperiod(2)

    def next(self):
        if self.data[0] and not self.data[-1]:
            self.lines.value[0] = 1
        elif not self.data[0] and self.data[-1] and not self.p.onlytrue:
            self.lines.value[0] = 1
        else:
            self.lines.value[0] = 0


class Lag(OP):
    """
    延迟数据
    Args:
        period (int): 延迟长度
    """

    params = (("period", 1),)

    def __init__(self):
        self.addminperiod(self.p.period)

    def next(self):
        self.lines.value[0] = self.data[-self.p.period]


class Abs(OP):
    """绝对值"""

    def next(self):
        self.lines.value[0] = abs(self.data[0])


class Div(OP):
    """a/b"""

    def next(self):
        if self.datas[1][0] != 0:
            self.lines.value[0] = self.datas[0][0] / self.datas[1][0]


class Mul(OP):
    """a*b"""

    def __init__(self):
        self.lines.value = self.datas[0] * self.datas[1]


class Step(OP):
    """
    当数据源为非0值时, 取数据源的值; 当数据源为0, inf, -inf 或nan时, 取上一个值
    """

    params = (("default", float("nan")),)

    def __init__(self):
        self.addminperiod(2)

    def nextstart(self):
        self.lines.value[0] = self.p.default

    def next(self):
        if (
            self.data[0] == 0
            or self.data[0] != self.data[0]
            or self.data[0] == float("inf")
            or self.data[0] == float("-inf")
        ):  # 0 或 nan
            self.lines.value[0] = self.lines.value[-1]
        else:
            self.lines.value[0] = self.data[0]


class StepZero(OP):
    """
    当数据源为inf, -inf 或nan时, 取上一个值, 其余时候取当前值
    """

    params = (("default", float("nan")),)

    def __init__(self):
        self.addminperiod(2)

    def nextstart(self):
        self.lines.value[0] = self.p.default

    def next(self):
        if (
            self.data[0] != self.data[0]
            or self.data[0] == float("inf")
            or self.data[0] == float("-inf")
        ):  # 0 或 nan
            self.lines.value[0] = self.lines.value[-1]
        else:
            self.lines.value[0] = self.data[0]


class ValueKeepLength(OP):
    """
    某个值保持持续不变的长度
    """

    params = (("value", 1),)

    def __init__(self):
        self.addminperiod(2)

    def next(self):
        if self.data[0] != self.p.value:
            self.lines.value[0] = 0
        else:
            self.lines.value[0] = self.lines.value[-1] + 1


class LinePiece(OP):
    """
    分段函数, 输入为两个data
    例如: xs=[10, 20], ys=[1, 2],描述函数为:
    datas[0] <= 10: y = 1 * datas[1]
    10 < datas[0] <=20: y =  datas[0] * 0.1 * datas[1]
    datas[0] > 20: y = 2 * datas[1]
    """

    params = (
        ("xs", []),
        ("ys", []),
    )

    def __init__(self):
        self.addminperiod(2)
        assert (
            len(self.p.xs) == len(self.p.ys) >= 2
        ), f"LinePiece参数不符合要求, 检查参数: {self.p.xs}, {self.p.ys}"
        self.ks = np.diff(self.p.ys) / np.diff(self.p.xs)

    def next(self):
        x, value = self.datas[0][0], self.datas[1][0]
        xs, ys, ks = self.p.xs, self.p.ys, self.ks

        if x <= xs[0]:
            self.lines.value[0] = ys[0] * value
            return
        if x > xs[-1]:
            self.lines.value[0] = ys[-1] * value
            return
        start_idx = bisect.bisect_left(xs, x) - 1
        dy = (x - xs[start_idx]) * ks[start_idx]
        self.lines.value[0] = (ys[start_idx] + dy) * value
        return


class TrimMean(OP):
    """
    截断算术平均
    Args:
        period (int): 统计周期
        top (int): 截断上限
        num (int): 计算数据长度
        reverse (bool): 逆序排序, 默认False
    """

    params = (
        ("period", 1440),
        ("top", 0),
        ("num", 10),
        ("reverse", True),
    )

    def __init__(self):
        self.addminperiod(self.p.period)

    def next(self):
        array = sorted(self.data.get(size=1440), reverse=self.p.reverse)
        self.lines.value[0] = (
            sum(array[self.p.top : self.p.top + self.p.num]) / self.p.num
        )


class Increase(OP):
    """
    周期内单调递增
    """

    params = (("period", 5),)

    def __init__(self):
        self.addminperiod(self.p.period)
        self.gt = Gt(self.data, Lag(self.data))
        self.accum = Accum(self.gt, period=self.p.period)
        self.lines.value = GteValue(self.accum, num=self.p.period)


class Decrease(OP):
    """
    周期内单调递减
    """

    params = (("period", 5),)

    def __init__(self):
        self.addminperiod(self.p.period)
        self.gt = Lt(self.data, Lag(self.data))
        self.accum = Accum(self.gt, period=self.p.period)
        self.lines.value = GteValue(self.accum, num=self.p.period)


class SynFreq(OP):
    """
    将数据频率同步
    data0: 标准freq数据
    data1: 待同步freq数据
    """

    def __init__(self):
        self.lines.value = self.datas[1]


class Filter(OP):
    """
    筛选操作
    满足筛选条件时, 执行指定函数
    函数: a, b *args -> v, a为该指标上一个值, b以及*args为数据源当前值
    默认函数为取当前值
    data0: filter
    data1: data
    ...
    Args:
        default (float): 默认值
    """

    params = (("default", float("nan")),)

    # 默认函数
    _func = staticmethod(lambda a, b: b)

    def __init__(self):
        self.addminperiod(2)
        self.filter = self.datas[0]
        self.srcs = self.datas[1:]

    def next(self):
        if self.filter[0]:
            self.lines.value[0] = self._func(
                self.lines.value[-1], *[d[0] for d in self.srcs]
            )
        else:
            self.lines.value[0] = self.p.default


class FilterCount(Filter):
    """
    过滤计数
    """

    params = (("default", 0),)

    _func = staticmethod(lambda a, b: a + 1)


class FilterAdd(Filter):
    """
    过滤求和
    当条件指标满足时, 对指标进行累加
    当条件指标不满足时, 重置为0
    """

    params = (("default", 0),)

    _func = staticmethod(lambda a, b: a + b)


class FilterHigh(Filter):
    params = (("default", float("-inf")),)

    _func = staticmethod(lambda a, *args: max(a, *args))


class FilterLow(Filter):
    params = (("default", float("inf")),)

    _func = staticmethod(lambda a, *args: min(a, *args))


class TriggerSet(OP):
    """
    触发后设置值
    函数: a, b *args -> v, a为该指标上一个值, b以及*args为数据源当前值
    data0: trigger
    data1: data
    """

    params = (("set", float("nan")),)

    # 默认函数
    _func = staticmethod(lambda a, b: b)

    def __init__(self):
        self.addminperiod(2)
        self.trigger = self.datas[0]
        self.srcs = self.datas[1:]

    def next(self):
        if self.trigger[0]:
            self.lines.value[0] = self.p.set
        else:
            self.lines.value[0] = self._func(
                self.lines.value[-1], *[d[0] for d in self.srcs]
            )


class TriggerSetAdd(TriggerSet):
    """
    求和过程的触发
    """

    params = (("set", 0),)

    _func = staticmethod(lambda a, b: a + b)


class TriggerSetCount(TriggerSet):
    """
    计数
    """

    params = (("set", 0),)

    _func = staticmethod(lambda a, b: a + 1)


class Toggle(OP):
    """
    正向触发设置为1, 负向触发设置为0
    负向触发优先
    data0: trigger true
    data1: trigger false
    """

    params = (("default", False),)

    def __init__(self):
        self.addminperiod(2)
        self.trigger_true = self.datas[0]
        self.trigger_false = self.datas[1]

    def next(self):
        if self.trigger_false[0]:
            self.lines.value[0] = False
        elif self.trigger_true[0]:
            self.lines.value[0] = True
        else:
            self.lines.value[0] = self.lines.value[-1]


ToggleNegative = Toggle


class TogglePositive(OP):
    """
    正向触发设置为1, 负向触发设置为0
    正向触发优先
    data0: trigger true
    data1: trigger false
    """

    params = (("default", False),)

    def __init__(self):
        self.addminperiod(2)
        self.trigger_true = self.datas[0]
        self.trigger_false = self.datas[1]

    def next(self):

        if self.trigger_true[0]:
            self.lines.value[0] = True
        elif self.trigger_false[0]:
            self.lines.value[0] = False
        else:
            self.lines.value[0] = self.lines.value[-1]


class LastN(OP):
    """
    过去n次求值
    """

    params = (("n", 7),)

    lines = ("record",)

    _func = staticmethod(lambda ls: ls[0])

    def __init__(self):
        self.addminperiod(self.p.n + 1)

    def prenext(self):
        if self.data[0] != self.data[0] and len(self.lines.record) > 1:  # nan
            self.lines.record.array[1:] = self.lines.record.array[:-1]
        else:
            self.lines.record[0] = self.data[0]

    def next(self):
        if self.data[0] != self.data[0]:  # nan
            self.lines.record.array[-self.p.n :] = self.lines.record.array[
                -self.p.n - 1 : -1
            ]
        else:
            self.lines.record[0] = self.data[0]
        if len([l for l in self.lines.record.array[-self.p.n :] if l == l]) >= self.p.n:
            self.lines.value[0] = self._func(self.lines.record.get(size=self.p.n))


class LastNHigh(LastN):
    _func = staticmethod(lambda ls: max(ls))


class LastNLow(LastN):
    _func = staticmethod(lambda ls: min(ls))


class If(OP):
    """
    条件判断
    data0: condition
    data1: true
    data2: false
    """

    def __init__(self):
        self.addminperiod(2)
        self.condition = self.datas[0]
        self.true = self.datas[1]
        self.false = self.datas[2]

    def next(self):
        if self.condition[0]:
            self.lines.value[0] = self.true[0]
        else:
            self.lines.value[0] = self.false[0]
