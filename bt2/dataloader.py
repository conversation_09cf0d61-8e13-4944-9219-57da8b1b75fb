# 数据载入类


class DataLoader:
    """数据载入基类, 包括数据的准备, 发送, 结束
    Methods:
        start: 数据准备工作, 比如连接数据库
        stop: 数据结束工作, 比如关闭数据库连接
        next: 数据发送, 返回一个字典, 如果数据发送完毕则返回False
            返回的字典包含基本字段:
                t (int): 时间戳, ms
                o, h, l, c (float): open, high, low, close
                v (float): volume
                q (float): turnover
                T (int): 收盘时间戳, ms
                edge (bool): 是否为收盘tick
                lt (int): 当前时间戳, ms
    """

    def start(self):
        NotImplemented

    def stop(self):
        NotImplemented

    def next(self):
        return {
            "t": int(),
            "o": float(),
            "h": float(),
            "l": float(),
            "c": float(),
            "v": int(),
            "q": float(),
            "T": int(),
            "edge": bool(),
            "lt": float(),
            "oi": float(),
        }
        # return False
