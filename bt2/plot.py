## 作图相关函数
from backtrader import AutoInfoClass, IndicatorBase, LineSeriesStub, DataBase
from bokeh.plotting import figure
from bokeh.layouts import gridplot
from bokeh.models import (
    WheelZoomTool,
    CrosshairTool,
    HoverTool,
    Span,
    ColumnDataSource,
    CustomJS,
    NumeralTickFormatter,
    Label,
    DataRange1d,
    ResetTool,
    PanTool,
)
from bokeh.events import MouseMove
import numpy as np
import pandas as pd
import bisect
from itertools import zip_longest
from .utils import freq_period_ratio, num2date
from .order import EventOrder
from .trade import EventTrade
from bokeh.palettes import Bright


def update_exists(d1, d2):
    """
    字典更新已存在的key, 返回新的dict
    """
    return {k: d2[k] if k in d2 else d1[k] for k in d1.keys() | d2.keys()}


class BokehPlotInfo:
    """
    bokeh作图函数
    """

    plotinfo_base = dict(
        b_plotname="",  # 指标做图名称, 指定名称的才会保存
        b_plot=False,  # 是否展示图
        b_subplot=True,  # 是否绘制在附图中
        b_plotabove=False,  # 是否绘制在主图上方
    )

    one_plotline_base = dict(
        b_type="line",  # 展示类型, line, scatter, varea
        b_marker="circle",  # 图标形状
        b_line_width=2.0,  # 线宽度
        b_size=1.0,  # 图标大小
        b_color="auto",  # 图标颜色
        b_digit=5,  # 默认精度
        b_alpha=0.6,  # 透明度
        b_with_price=False,  # 跟随价格
    )

    @classmethod
    def set_bokeh_plotinfo(cls, ind, **kwargs):
        """
        将指标的plotinfo设置为bokeh使用参数
        """
        plotinfo = getattr(ind, "plotinfo", AutoInfoClass)
        base_plotinfo = cls.plotinfo_base.copy()
        base_plotinfo.update(kwargs)
        new_plotinfo = plotinfo._derive("bokeh", base_plotinfo, [])
        ind.plotinfo = new_plotinfo
        return ind

    @classmethod
    def set_bokeh_plotlines(cls, ind, **kwargs):
        """
        将指标的plotlines设置为bokeh使用参数
        """
        plotlines = getattr(ind, "plotlines", AutoInfoClass)
        base_plotlines = {k: cls.plotinfo_base for k in plotlines._getkeys()}
        for k, v in kwargs.items():
            if k not in base_plotlines:
                base_plotlines[k] = v
            else:
                base_plotlines[k].update(v)
        new_plotlines = plotlines._derive("bokeh", base_plotlines, [], recurse=True)
        ind.plotlines = new_plotlines
        return ind

    @classmethod
    def get_bokeh_plotinfo(cls, ind):
        """
        获取bokeh作图的必要信息
        """
        return update_exists(cls.plotinfo_base, ind.plotinfo._getkwargs())

    @classmethod
    def get_bokeh_plotline(cls, ind):
        """
        获取bokeh作图的字段信息
        """
        return {
            key: update_exists(cls.one_plotline_base, value._getkwargs())
            for key, value in ind.plotlines._getkwargs().items()
        }

    @classmethod
    def get_ind_data(cls, ind, tries=0):
        """
        获取指标的数据源
        """
        tries += 1
        if tries >= 10:
            return None
        if isinstance(ind, DataBase):
            return ind
        if isinstance(ind, IndicatorBase):
            return cls.get_ind_data(ind.data, tries)
        if isinstance(ind, LineSeriesStub):
            return cls.get_ind_data(ind.owner, tries)


class BokehStrategyPlotter:
    """
    bokeh策略作图
    """

    def __init__(
        self,
        plot_order=True,
        plot_equity=True,
        plot_open_int=True,
        subplot_height=120,
        sizing_mode="stretch_width",
    ):
        self._plot_order = plot_order  # 是否绘制订单
        self._plot_equity = plot_equity  # 是否绘制资金线
        self._plot_open_int = plot_open_int  # 是否绘制持仓量
        self._mainplot_height = 500  # 主图高度
        self._subplot_height = subplot_height  # 附图高度
        self._sizing_mode = sizing_mode

    def plot(self, stra):
        """
        绘制策略
        """
        ## 按照数据源分类
        ## 结构: 数据源: {main: 主图, sub: 下方附图, above: 上方附图, plotind: 待绘制指标}

        data_source_dict = {
            data: {
                "main": None,
                "ratio": None,
                "sub": [],
                "above": [],
                "plotinds": [],
                "plotconds": [],
            }
            for data in stra.datas
        }
        ## condition 分类
        for condition in stra.get_all_conditions():
            plotinfo = BokehPlotInfo.get_bokeh_plotinfo(condition)
            if plotinfo["b_plot"]:
                data = BokehPlotInfo.get_ind_data(condition)
                cond_name = [condition.get_name()]
                data_source_dict[data]["plotconds"].append((cond_name, condition))

        # condition下的indicator分类
        for plotdict in data_source_dict.values():
            for cond_name, condition in plotdict["plotconds"]:
                plotdict["plotinds"].append((cond_name, condition))
                ind_dict = condition.get_indicator_dict()
                for ind_name, ind in ind_dict.items():
                    plotinfo = BokehPlotInfo.get_bokeh_plotinfo(ind)
                    if plotinfo["b_plot"]:
                        data = BokehPlotInfo.get_ind_data(ind)
                        plotname = cond_name + [ind_name]
                        data_source_dict[data]["plotinds"].append((plotname, ind))
        # 作子图
        main_data = None  ## 控制其他主图的主图
        for data, plotdict in data_source_dict.items():
            # 画主图
            if main_data is None:
                main_data = data  # 第一个data作为主图data
                plotdict["ratio"] = ratio = 1
                plotdict["main"] = base_plot, base_ds = self.plot_data(data)
                if self._plot_equity:
                    # 只考虑主图绘制资金线
                    plotdict["sub"].append(
                        self.plot_ind(
                            stra.observers.broker, base_plot, base_ds, ratio, ["equity"]
                        )
                    )
                    # 只考虑主图绘制交易
                    plotdict["sub"].append(
                        self.plot_trade(stra, base_plot, base_ds, ratio)
                    )
                if self._plot_order:
                    # 只考虑主图绘制订单
                    self.plot_order(stra, base_plot, base_ds)
            else:
                base_plot, base_ds = data_source_dict[main_data]["main"]
                plotdict["ratio"] = ratio = freq_period_ratio(
                    data.p.freq, main_data.p.freq
                )
                plotdict["main"] = base_plot, base_ds = self.plot_data(
                    data, base_plot, base_ds, ratio=ratio
                )
            # 绘制交易量
            plotdict["sub"].append(self.plot_volume(data, base_plot, base_ds, ratio))
            # 绘制指标
            for plotname, ind in plotdict["plotinds"]:
                ind_plot, ind_ds = self.plot_ind(
                    ind, base_plot, base_ds, ratio, plotname
                )
                plotinfo = BokehPlotInfo.get_bokeh_plotinfo(ind)
                if plotinfo["b_subplot"]:
                    if plotinfo["b_plotabove"]:
                        plotdict["above"].append((ind_plot, ind_ds))
                    else:
                        plotdict["sub"].append((ind_plot, ind_ds))

        # 合并图
        plots = zip_longest(
            *(
                (
                    [ps[0] for ps in plotdict["above"]]
                    + [plotdict["main"][0]]
                    + [ps[0] for ps in plotdict["sub"]]
                )
                for plotdict in data_source_dict.values()
            )
        )

        return gridplot(plots, sizing_mode=self._sizing_mode)

    def plot_data(self, data, base_plot=None, base_ds=None, ratio=1):
        # 绘制k线
        if base_plot is None or base_ds is None:
            # 无基准视图
            cross_width = Span(dimension="width", line_width=1)
            cross_height = Span(dimension="height", line_width=1, name="cross_height")
            ds = ColumnDataSource(
                {
                    "index": np.array(range(len(data.datetime.array))),
                    "open": np.array(data.open.array),
                    "close": np.array(data.close.array),
                    "high": np.array(data.high.array),
                    "low": np.array(data.low.array),
                    "dt": np.vectorize(num2date)(data.datetime.array),
                }
            )
            ds.data["color"] = np.where(
                ds.data["open"] < ds.data["close"], "green", "red"
            )
            ds.data["datetime"] = np.vectorize(lambda x: x.strftime("%x %X"))(
                ds.data["dt"]
            )
            ds.data["amp"] = (
                (ds.data["high"] - ds.data["low"]) / ds.data["open"] * 100
            ).round(3)
            # 数据
            width = 0.8
        else:
            # 有基准视图
            cross_width = Span(dimension="width", line_width=1)
            cross_height = cross_height = base_plot.select(name="cross_height")[0]
            ds = ColumnDataSource(
                {
                    "open": np.array(data.open.array),
                    "close": np.array(data.close.array),
                    "high": np.array(data.high.array),
                    "low": np.array(data.low.array),
                    "dt": np.vectorize(num2date)(data.datetime.array),
                }
            )
            ds.data["color"] = np.where(
                ds.data["open"] < ds.data["close"], "green", "red"
            )
            ds.data["datetime"] = np.vectorize(lambda x: x.strftime("%x %X"))(
                ds.data["dt"]
            )
            ds.data["index"] = np.array(
                [bisect.bisect_left(base_ds.data["dt"], x) for x in ds.data["dt"]]
            )
            ds.data["amp"] = (
                (ds.data["high"] - ds.data["low"]) / ds.data["open"] * 100
            ).round(3)
            # 数据
            width = 0.8 * ratio
        # 创建图表
        p = figure(
            title=f"KLine_{data.get_symbol()}_{data.p.freq}",
            background_fill_color="#efefef",
            y_axis_label="price",
            width=1000,
            height=self._mainplot_height,
            tools="",
        )
        p.y_range = DataRange1d()
        p.yaxis.formatter = NumeralTickFormatter(format="0,0")
        p.xaxis.major_label_overrides = {
            i: dt.strftime("%X") for i, dt in zip(ds.data["index"], ds.data["dt"])
        }
        p.xaxis.ticker.min_interval = ratio
        if base_plot:
            p.x_range = base_plot.x_range

        # 选择工具
        resettool = ResetTool(syncable=False)
        pantool = PanTool(dimensions="width", syncable=False)

        # 缩放工具
        widthzoomtool = WheelZoomTool(dimensions="width", syncable=False)
        heightzoomtool = WheelZoomTool(dimensions="height", syncable=False)

        p.add_tools(widthzoomtool, heightzoomtool, resettool, pantool)

        # 十字线工具
        crosshair = CrosshairTool(overlay=[cross_width, cross_height])

        p.add_tools(
            crosshair,
        )
        # 对应标签
        label = Label(
            x=0,
            y=0,
            x_units="screen",
            y_units="screen",
            text=" ",
            background_fill_alpha=0.5,
            anchor="bottom_left",
            padding=5,
            syncable=False,
            text_baseline="top",
            tags=["datetime", "open", "high", "low", "close", "amp"],
        )
        p.add_layout(label, place="above")

        ind_label = Label(
            x=0,
            y=0,
            x_units="screen",
            y_units="screen",
            text=" ",
            background_fill_alpha=0.5,
            anchor="bottom_left",
            padding=5,
            syncable=False,
            text_baseline="bottom",
            tags=[],
            name="main_ind_label",
        )
        p.add_layout(ind_label)

        # JavaScript 回调函数，用于更新交叉线位置
        move_callback = CustomJS(
            args=dict(span=cross_height, ratio=ratio),
            code="""
            export default ({span, ratio}, obj, data, context) => {
                let ex = Math.floor(obj.x / ratio) * ratio
                span.location = ex
                }
        """,
        )
        p.js_on_event(MouseMove, move_callback)

        callback = CustomJS(
            args=dict(label=label, source=ds, ratio=ratio),
            code="""
            export default ({label, source, ratio}, obj, data, context) => {
                let ex = Math.floor(obj.location / ratio) * ratio
                // 构造展示文本，每个字段单独一行
                let idx = source.data['index'].indexOf(ex)
                let textLines = [];
                for (let field of label.tags) {
                    let value = source.data[field][idx]; // 获取对应字段的值
                    textLines.push(`${field}: ${value}`); // 格式化为 "field: value"
                }
                label.text = textLines.join(", "); // 将多行文本拼接成字符串
            }
        """,
        )

        cross_height.js_on_change("location", callback)

        # 自动调整坐标轴
        p.x_range.js_on_change(
            "start",
            CustomJS(
                args=dict(
                    source=ds, y_range=p.y_range, ratio=ratio, heighttool=heightzoomtool
                ),
                code="""
            export default ({source, y_range, ratio, heighttool}, x_range, data, context) => {
                if (heighttool.active){
                    return
                }
                let start_idx = Math.max(Math.floor(x_range.start), source.data.index[0])
                let end_idx = Math.min(Math.floor(x_range.end), source.data.index[source.data.index.length-1])
                let y_min = Math.min(...source.data.low.slice(Math.floor(start_idx/ratio), Math.floor(end_idx/ratio)))
                let y_max = Math.max(...source.data.high.slice(Math.floor(start_idx/ratio), Math.floor(end_idx/ratio)))
                let delta = y_max - y_min
                y_range.start = y_min - delta * y_range.range_padding
                y_range.end = y_max  + delta * y_range.range_padding
            }
        """,
            ),
        )

        p.vbar(
            source=ds, x="index", top="open", bottom="close", width=width, color="color"
        )
        p.vbar(
            source=ds, x="index", top="high", bottom="low", width=0.01, color="color"
        )
        return p, ds

    def plot_volume(self, data, plot, ds, ratio=1):
        """绘制交易量"""
        # 创建图表
        cross_width = Span(dimension="width", line_width=1)
        cross_height = plot.select(name="cross_height")[0]
        ds.data["volume"] = np.array(data.volume.array)
        ds.data["openint"] = np.array(data.openint.array)
        width = 0.8 * ratio
        p = figure(
            y_axis_label="volume",
            width=1000,
            height=self._subplot_height,
            tools="",
            title=None,
        )
        p.x_range = plot.x_range
        p.xaxis.major_label_overrides = {
            i: dt.strftime("%X") for i, dt in zip(ds.data["index"], ds.data["dt"])
        }
        p.yaxis.formatter = NumeralTickFormatter(format="0,0")
        p.xaxis.ticker.min_interval = 1

        # 选择工具
        resettool = ResetTool(syncable=False)
        pantool = PanTool(dimensions="width", syncable=False)

        # 缩放工具
        widthzoomtool = WheelZoomTool(dimensions="width", syncable=False)
        heightzoomtool = WheelZoomTool(dimensions="height", syncable=False)

        p.add_tools(widthzoomtool, heightzoomtool, resettool, pantool)

        # 添加十字线工具
        p.add_tools(CrosshairTool(overlay=[cross_width, cross_height]))
        # 增加数据展示
        label = Label(
            x=0,
            y=0,
            x_units="screen",
            y_units="screen",
            text=" ",
            background_fill_alpha=0.5,
            anchor="bottom_left",
            padding=5,
            syncable=False,
            text_baseline="top",
            tags=["volume", "openint"],
        )
        p.add_layout(label, place="above")

        # JavaScript 回调函数，用于更新交叉线位置
        move_callback = CustomJS(
            args=dict(span=cross_height, ratio=ratio),
            code="""
            export default ({span, ratio}, obj, data, context) => {
                let ex = Math.floor(obj.x / ratio) * ratio
                span.location = ex
                }
        """,
        )
        p.js_on_event(MouseMove, move_callback)

        callback = CustomJS(
            args=dict(label=label, source=ds, ratio=ratio),
            code="""
            export default ({label, source, ratio}, obj, data, context) => {
                let ex = Math.floor(obj.location / ratio) * ratio
                // 构造展示文本，每个字段单独一行
                let idx = source.data['index'].indexOf(ex)
                let textLines = [];
                for (let field of label.tags) {
                    let value = source.data[field][idx]; // 获取对应字段的值
                    textLines.push(`${field}: ${value}`); // 格式化为 "field: value"
                }
                label.text = textLines.join(", "); // 将多行文本拼接成字符串
            }
        """,
        )

        cross_height.js_on_change("location", callback)

        # 交易量
        p.vbar(source=ds, x="index", top="volume", width=width, color="color")
        # 持仓量
        if self._plot_open_int:
            p.line(source=ds, x="index", y="openint", color="navy")
        return p, ds

    def plot_trade(self, stra, plot, ds, ratio=1):
        # 绘制Trade
        # 创建图表
        cross_width = Span(dimension="width", line_width=1)
        cross_height = plot.select(name="cross_height")[0]
        p = figure(
            y_axis_label="pnl",
            width=1000,
            height=self._subplot_height,
            tools="",
            title=None,
        )
        p.x_range = plot.x_range
        p.xaxis.major_label_overrides = {
            i: dt.strftime("%X") for i, dt in zip(ds.data["index"], ds.data["dt"])
        }
        p.yaxis.formatter = NumeralTickFormatter(format="0,0")
        p.xaxis.ticker.min_interval = 1

        # 选择工具
        resettool = ResetTool(syncable=False)
        pantool = PanTool(dimensions="width", syncable=False)

        # 缩放工具
        widthzoomtool = WheelZoomTool(dimensions="width", syncable=False)
        heightzoomtool = WheelZoomTool(dimensions="height", syncable=False)

        p.add_tools(widthzoomtool, heightzoomtool, resettool, pantool)

        # 添加十字线工具
        p.add_tools(CrosshairTool(overlay=[cross_width, cross_height]))
        # JavaScript 回调函数，用于更新交叉线位置
        move_callback = CustomJS(
            args=dict(span=cross_height, ratio=ratio),
            code="""
            export default ({span, ratio}, obj, data, context) => {
                let ex = Math.floor(obj.x / ratio) * ratio
                span.location = ex
                }
        """,
        )
        p.js_on_event(MouseMove, move_callback)

        trade_df = pd.DataFrame(
            columns=EventTrade.csv_header,
            data=[
                trade.to_data()
                for trade in stra._trades[stra.data][0]
                if trade.isclosed
            ],
        )
        if trade_df.empty:
            return p, ds
        trade_df["idx"] = trade_df["end_dt"].map(
            lambda x: bisect.bisect_left(ds.data["dt"], x)
        )
        trade_df["dt"] = np.datetime_as_string(trade_df["end_dt"], unit="m")
        trade_df["hold_minutes"] = trade_df["hold_sec"] / 60
        trade_df["income"] = trade_df["pnl"] - trade_df["comm"]
        trade_df["color"] = np.where(trade_df["income"] > 0, "green", "red")
        orderds = ColumnDataSource(trade_df)
        renderer = p.scatter(
            source=orderds,
            x="idx",
            y="income",
            marker="circle",
            line_color="color",
            line_width=2,
            fill_color="color",
            size=10,
            legend_label="trade",
        )

        p.legend.location = "bottom_left"
        p.legend.click_policy = "mute"
        p.legend.background_fill_alpha = 0.35
        # 增加标签
        hovertool = HoverTool(
            tooltips=[
                ("dt", "@dt"),
                ("income", "@income"),
                ("pnl", "@pnl"),
                ("max_pnl", "@max_pnl"),
                ("comm", "@comm"),
                ("hold_minutes", "@hold_minutes"),
            ],
            description="order",
            syncable=False,
            renderers=[renderer],
        )
        p.add_tools(hovertool)
        return p, ds

    def plot_order(self, stra, plot, ds):
        # 绘制订单
        def calc_same_count(data):
            same_value = data == data.shift()
            cumsum = (same_value != same_value.shift()).cumsum()  # 找到连续段的编号
            true_counts = same_value.groupby(
                cumsum
            ).cumsum()  # 对连续段内的 True 进行累加
            true_counts[~same_value] = 0  # 对 False 的位置赋值为 0
            return true_counts

        ## 在k线基础上绘制
        order_df = pd.DataFrame(
            columns=EventOrder.csv_header,
            data=[order.to_data() for order in stra.broker.orders],
        )
        if order_df.empty:
            return plot, ds
        order_df["idx"] = order_df["executed_dt"].map(
            lambda x: bisect.bisect_left(ds.data["dt"], x)
        )
        order_df["count"] = calc_same_count(order_df["idx"])
        order_df["dt"] = np.datetime_as_string(order_df["executed_dt"], unit="m")
        order_df["high"] = ds.data["high"][order_df["idx"]]
        order_df["low"] = ds.data["low"][order_df["idx"]]

        def calc_position(x):
            dist = (x["high"] - x["low"]) * 0.1
            if x["ord_type"] == "BUY":
                return x["high"] + dist * (2 * x["count"] + 1)
            else:
                return x["low"] - dist * (2 * x["count"] + 1)

        order_df["position"] = order_df.apply(calc_position, axis=1)
        order_df["color"] = np.where(order_df["ord_type"] == "BUY", "green", "red")
        order_df["marker"] = np.where(
            order_df["ord_type"] == "BUY", "inverted_triangle", "triangle"
        )
        orderds = ColumnDataSource(order_df)
        renderer = plot.scatter(
            source=orderds,
            x="idx",
            y="position",
            marker="marker",
            line_color="color",
            line_width=2,
            fill_color="white",
            size=10,
            legend_label="order",
        )

        plot.legend.location = "bottom_left"
        plot.legend.click_policy = "mute"
        plot.legend.background_fill_alpha = 0.35
        # 增加标签
        hovertool = HoverTool(
            tooltips=[
                ("dt", "@dt"),
                ("price", "@executed_price{0.0000}"),
                ("size", "@size"),
                ("status", "@status"),
                ("ord_type", "@ord_type"),
                ("side", "@side"),
                ("trigger", "@trigger"),
            ],
            description="order",
            syncable=False,
            renderers=[renderer],
        )
        plot.add_tools(hovertool)
        return plot, ds

    def plot_ind(self, ind, base_plot, base_ds, ratio=1, plotname=[]):
        # 绘制指标
        cross_width = Span(dimension="width", line_width=1)
        cross_height = base_plot.select(name="cross_height")[0]
        plotinfo = BokehPlotInfo.get_bokeh_plotinfo(ind)
        plotlines = BokehPlotInfo.get_bokeh_plotline(ind)

        if plotinfo["b_plotname"]:
            y_axis_label = plotinfo["b_plotname"]
        else:
            y_axis_label = ".".join(plotname)

        if not plotinfo["b_subplot"]:
            # 画在主图
            p = base_plot
            label = base_plot.select(name="main_ind_label")[0]
        else:
            # 新附图
            p = figure(
                y_axis_label=y_axis_label,
                width=1000,
                height=self._subplot_height,
                tools="",
                title=None,
            )
            p.x_range = base_plot.x_range
            p.xaxis.major_label_overrides = base_plot.xaxis.major_label_overrides
            p.yaxis.formatter = NumeralTickFormatter(format="0,0")
            p.xaxis.ticker.min_interval = ratio

            # 选择工具
            resettool = ResetTool(syncable=False)
            pantool = PanTool(dimensions="width", syncable=False)

            # 缩放工具
            widthzoomtool = WheelZoomTool(dimensions="width", syncable=False)
            heightzoomtool = WheelZoomTool(dimensions="height", syncable=False)

            p.add_tools(widthzoomtool, heightzoomtool, resettool, pantool)

            # 添加十字线工具
            p.add_tools(CrosshairTool(overlay=[cross_width, cross_height]))
            label = Label(
                x=0,
                y=0,
                x_units="screen",
                y_units="screen",
                text=" ",
                background_fill_alpha=0.5,
                anchor="bottom_left",
                padding=5,
                syncable=False,
                text_baseline="top",
                tags=[],
            )
            p.add_layout(label, place="above")
            # JavaScript 回调函数，用于更新交叉线位置
            move_callback = CustomJS(
                args=dict(span=cross_height, ratio=ratio),
                code="""
                export default ({span, ratio}, obj, data, context) => {
                    let ex = Math.floor(obj.x / ratio) * ratio
                    span.location = ex
                    }
            """,
            )
            p.js_on_event(MouseMove, move_callback)

        callback = CustomJS(
            args=dict(label=label, source=base_ds, ratio=ratio),
            code="""
            export default ({label, source, ratio}, obj, data, context) => {
                let ex = Math.floor(obj.location / ratio) * ratio
                const numberFormatter = new Intl.NumberFormat('en-US')
                // 构造展示文本，每个字段单独一行
                let idx = source.data['index'].indexOf(ex)
                let textLines = [];
                for (let field of label.tags) {
                    let value = source.data[field][idx]; // 获取对应字段的值
                    let formattedValue = typeof value === "number" ? numberFormatter.format(value) : value;
                    textLines.push(`${field}: ${formattedValue}`); // 格式化为 "field: value"
                }
                label.text = textLines.join(", "); // 将多行文本拼接成字符串
            }
        """,
        )
        cross_height.js_on_change("location", callback)

        colors = list(Bright[7])[::-1]
        for key, line in zip(ind._getlines(), ind.lines):
            key_name = f"{y_axis_label}.{key}"
            lineinfo = plotlines[key]
            base_ds.data[key_name] = np.array(line.array).round(
                plotlines[key]["b_digit"]
            )
            label.tags.append(key_name)
            if lineinfo["b_color"] == "auto":
                color = colors.pop()
            else:
                color = lineinfo["b_color"]
            if lineinfo["b_type"] == "line":
                p.line(
                    source=base_ds,
                    x="index",
                    y=key_name,
                    width=lineinfo["b_line_width"],
                    color=color,
                    legend_label=key_name,
                )
            elif lineinfo["b_type"] == "scatter":
                p.scatter(
                    source=base_ds,
                    x="index",
                    y=key_name,
                    marker=lineinfo["b_marker"],
                    size=lineinfo["b_size"],
                    color=color,
                    legend_label=key_name,
                )
            elif lineinfo["b_type"] == "vstrip":
                start_idx, end_idx = self.get_start_end_idx(base_ds.data[key_name])
                x0 = base_ds.data["index"][start_idx]
                x1 = base_ds.data["index"][end_idx]
                p.vstrip(
                    x0=x0,
                    x1=x1,
                    color=color,
                    alpha=lineinfo["b_alpha"],
                    legend_label=key_name,
                )
            else:
                pass

        p.legend.location = "bottom_left"
        p.legend.click_policy = "mute"
        p.legend.background_fill_alpha = 0.35

        return p, base_ds

    @classmethod
    def get_start_end_idx(cls, arr):
        # 初始化空的起始和结束索引列表
        start_indices = []
        end_indices = []

        # 遍历数组
        in_sequence = False  # 用于标记是否处于连续的 1 序列中
        for i in range(len(arr)):
            if arr[i] == 1 and not in_sequence:
                # 发现连续 1 的起始位置
                start_indices.append(i)
                in_sequence = True
            elif arr[i] == 0 and in_sequence:
                # 发现连续 1 的结束位置
                end_indices.append(i - 1)
                in_sequence = False

        # 如果数组以 1 结束，我们需要记录最后的结束索引
        if in_sequence:
            end_indices.append(len(arr) - 1)
        return start_indices, end_indices
